import React, { useState, useEffect, useRef } from 'react';
import { useToast } from "@/hooks/use-toast"; // Assuming this hook is correctly implemented
import { useWalletContext } from "@/context/WalletContext"; // ADD this line, renamed cashBalance for convenience and added spendFromWallet
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { commodities } from "@/data/commodityData"; // Assuming this data exists
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils"; // Assuming this utility exists
import {
  Truck,
  Route,
  Navigation,
  Calendar,
  Package,
  Clock,
  MapPin,
  AlertCircle,
  Mail,
  PhoneCall,
  CreditCard,
  Wallet,
  Smartphone,
  CircleDollarSign,
  ShoppingCart,
  User,
  ExternalLink,
  Phone, // Added Phone icon
  CheckCircle
} from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from "@/components/ui/dialog";
// Toaster component should be rendered once at your app's root
// import { Toaster } from "@/components/ui/toaster"; // Assuming Toaster is in Layout/App component


// Define truck types available for booking
interface TruckType {
  id: string;
  name: string;
  capacity: string; // e.g., "Up to 3 tons"
  maxCapacityTons: number; // Numeric max capacity
  suitable: string[];
  icon: React.ReactNode;
  availableCount: number;
}

const truckTypes: TruckType[] = [
  { id: "small-truck", name: "Light Truck", capacity: "Up to 3 tons", maxCapacityTons: 3, suitable: ["Vegetables", "Fruits", "Seeds"], icon: <Truck size={24} />, availableCount: 24, },
  { id: "medium-truck", name: "Medium Truck", capacity: "4-8 tons", maxCapacityTons: 8, suitable: ["Grains", "Fertilizers", "Feeds"], icon: <Truck size={32} />, availableCount: 16, },
  { id: "large-truck", name: "Heavy Truck", capacity: "9-15 tons", maxCapacityTons: 15, suitable: ["Bulk Grains", "Machinery", "Equipment"], icon: <Truck size={40} />, availableCount: 8, },
  { id: "refrigerated-truck", name: "Refrigerated Truck", capacity: "Up to 8 tons", maxCapacityTons: 8, suitable: ["Perishables", "Dairy Products", "Meat"], icon: <Truck size={32} />, availableCount: 12, }
];

interface TrackingUpdate {
  timestamp: string;
  location: string;
  statusMessage: string;
}

interface ConfirmedBooking {
  id: string;
  origin: string;
  destination: string;
  commodity: string;
  quantity: string; // e.g., "12 tons"
  truckType: string; // Truck name
  pickupDate: string;
  estimatedArrival: string;
  price: number;
  status: 'scheduled' | 'in-transit' | 'delivered' | 'cancelled';
  currentLocation?: string;
  driverDetails?: { name: string; contact: string; vehicleReg?: string; };
  trackingUpdates?: TrackingUpdate[];
  // Include fields from form for potential display/reference if needed
  weight: number; // Storing numeric weight for calculations
  volume?: number;
  specialRequirements?: string;
  pickupTime?: string;
  contactName?: string;
  contactPhone?: string;
}

// Mock initial demo shipments with expanded details
const initialDemoShipments: ConfirmedBooking[] = [
  { id: "TR-78901", origin: "Kampala", destination: "Jinja", commodity: "Maize", quantity: "12 tons", weight: 12, truckType: "Heavy Truck", pickupDate: "2024-07-25", estimatedArrival: "2024-07-26", price: 1450000, status: "in-transit", currentLocation: "Mukono Town", driverDetails: { name: "John Doe", contact: "0777123456", vehicleReg: "UAB 123X" }, trackingUpdates: [ { timestamp: "2024-07-25 09:00", location: "Kampala Depot", statusMessage: "Shipment picked up" }, { timestamp: "2024-07-25 11:30", location: "Bweyogerere", statusMessage: "En route to Jinja" }, { timestamp: "2024-07-25 14:00", location: "Mukono Town", statusMessage: "Approaching destination" }, ] },
  { id: "TR-78902", origin: "Gulu", destination: "Kampala", commodity: "Coffee Beans", quantity: "8 tons", weight: 8, truckType: "Medium Truck", pickupDate: "2024-07-20", estimatedArrival: "2024-07-22", price: 1200000, status: "delivered", driverDetails: { name: "Alice Smith", contact: "0755987654" }, trackingUpdates: [ { timestamp: "2024-07-22 15:00", location: "Kampala Warehouse", statusMessage: "Delivered and signed." } ] },
  { id: "TR-78903", origin: "Mbarara", destination: "Entebbe", commodity: "Milk", quantity: "5 tons", weight: 5, truckType: "Refrigerated Truck", pickupDate: "2024-08-01", estimatedArrival: "2024-08-02", price: 980000, status: "scheduled", driverDetails: { name: "Driver TBC", contact: "N/A" } },
];

const popularRoutes = [
  { origin: "Kampala", destination: "Jinja", price: "120,000 UGX/ton" },
  { origin: "Kampala", destination: "Mbarara", price: "200,000 UGX/ton" },
   { origin: "Gulu", destination: "Kampala", price: "250,000 UGX/ton" }, // Added popular route example
   { origin: "Mbale", destination: "Kampala", price: "180,000 UGX/ton" }, // Added popular route example
];

// Combine and refine booking form schema
const bookingFormSchema = z.object({
  origin: z.string().min(2, { message: "Origin must be at least 2 characters." }).toLowerCase(), // Standardize to lowercase
  destination: z.string().min(2, { message: "Destination must be at least 2 characters." }).toLowerCase(), // Standardize to lowercase
  commodity: z.string().min(1, { message: "Please select a commodity." }),
  weight: z.string().min(1, { message: "Weight is required." }).refine(val => !isNaN(parseFloat(val)) && parseFloat(val) > 0, { message: "Weight must be a positive number." }),
  volume: z.string().optional().refine(val => val === "" || (!isNaN(parseFloat(val || '')) && parseFloat(val || '') >= 0), { message: "Volume must be a non-negative number or empty." }), // Added optional volume validation
  specialRequirements: z.string().optional(), // Added optional special requirements
  truckType: z.string().min(1, { message: "Please select a truck type." }),
  pickupDate: z.string().min(1, "Please select a pickup date.")
    .refine(dateString => /^\d{4}-\d{2}-\d{2}$/.test(dateString), "Invalid date format (YYYY-MM-DD).")
    .refine(dateStr => {
      const selectedDate = new Date(dateStr);
      if (isNaN(selectedDate.getTime())) return false;
      const today = new Date(); today.setHours(0, 0, 0, 0);
      // Allow today's date or future dates
      return selectedDate >= today;
    }, "Pickup date cannot be in the past."),
  pickupTime: z.string().optional(), // Added optional pickup time
  contactName: z.string().optional(), // Added optional contact name
  contactPhone: z.string().optional(), // Added optional contact phone
  paymentMethod: z.string().min(1, "Please select a payment method."), // Added payment method validation
}).superRefine((data, ctx) => {
  // Ensure origin and destination are different
  if (data.origin && data.destination && data.origin === data.destination) {
      ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Origin and destination cannot be the same location.",
          path: ["destination"], // Or origin, or both
      });
  }

  const weightNum = parseFloat(data.weight);
  const selectedTruck = truckTypes.find(t => t.id === data.truckType);
  // Check if truck has sufficient capacity based on *numeric* weight
  if (selectedTruck && weightNum > selectedTruck.maxCapacityTons) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: `Load (${weightNum} tons) exceeds ${selectedTruck.name} capacity (${selectedTruck.maxCapacityTons} tons). Select a larger truck or reduce weight.`, path: ["weight"] });
  }
});


const Transportation = () => {
  const { toast } = useToast();
  const { cashBalance: userWalletBalance, spendFromWallet, depositFunds: fundUserWallet } = useWalletContext(); // ADD this line, renamed cashBalance for convenience and added spendFromWallet
  const [selectedTruckType, setSelectedTruckType] = useState<string | null>(null);
  const [estimatedPrice, setEstimatedPrice] = useState<number | null>(null);
  const [isBookingConfirmed, setIsBookingConfirmed] = useState(false);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [bookingDetailsForPayment, setBookingDetailsForPayment] = useState<z.infer<typeof bookingFormSchema> | null>(null);
  const estimateMadeRef = useRef(false); // To track if a price estimate has been made
  const [isFundWalletModalOpen, setIsFundWalletModalOpen] = useState(false);
  const [depositAmount, setDepositAmount] = useState<string>(""); // State for fund wallet input
  const [confirmedBookings, setConfirmedBookings] = useState<ConfirmedBooking[]>(initialDemoShipments);
  const [isTrackingModalOpen, setIsTrackingModalOpen] = useState(false);
  const [trackingShipmentDetails, setTrackingShipmentDetails] = useState<ConfirmedBooking | null>(null);

  // State for the Fund Wallet Modal
  const [selectedFundingMethodKey, setSelectedFundingMethodKey] = useState<string | null>(null);
  const [fundingPhoneNumber, setFundingPhoneNumber] = useState<string>("");

  // Define simplified payment methods for the funding modal
  const fundingPaymentMethods = [
    { key: "mtn_ug", name: "MTN Mobile Money (UG)", icon: <Smartphone className="mr-3 h-5 w-5 text-yellow-500" />, type: "mobile_money", placeholder: "e.g., 077xxxxxxx" },
    { key: "airtel_ug", name: "Airtel Money (UG)", icon: <Smartphone className="mr-3 h-5 w-5 text-red-500" />, type: "mobile_money", placeholder: "e.g., 075xxxxxxx" },
    // Card payment can be added here if needed, similar to Wallet.tsx
    // { key: "card", name: "Visa / Mastercard", icon: <CreditCard className="mr-3 h-5 w-5 text-blue-500" />, type: "card", description: "Secure card payment" },
  ];

  // Use useForm hook with combined schema and default values
  const form = useForm<z.infer<typeof bookingFormSchema>>({
    resolver: zodResolver(bookingFormSchema),
    defaultValues: {
      origin: "",
      destination: "",
      commodity: "",
      weight: "",
      volume: "",
      specialRequirements: "",
      truckType: "",
      pickupDate: "",
      pickupTime: "",
      contactName: "",
      contactPhone: "",
      paymentMethod: "wallet", // Default payment method
    },
     mode: "onChange", // Validate as user types
  });

  // Effect to reset price/confirmation when relevant fields change after an estimate
  const watchedFieldsForPriceReset = [form.watch("origin"), form.watch("destination"), form.watch("weight"), form.watch("truckType"), form.watch("volume")]; // Added volume to watched fields
  useEffect(() => { if (estimateMadeRef.current) { setEstimatedPrice(null); setIsBookingConfirmed(false); toast({ title: "Price Reset", description: "Booking details changed, please calculate price again."}); estimateMadeRef.current = false;} /* eslint-disable-next-line react-hooks/exhaustive-deps */ }, watchedFieldsForPriceReset);

  // Handler for truck type selection - simplified from multi-step version
  const handleTruckTypeSelect = (id: string) => {
      const truck = truckTypes.find(t => t.id === id);
      const weight = parseFloat(form.getValues("weight") || '0'); // Get current weight value

      // Check if truck has sufficient capacity IF weight is entered and valid
      if (truck && weight > 0 && weight > truck.maxCapacityTons) {
           toast({
             title: "Capacity Warning",
             description: `This truck (${truck.name}) cannot carry ${weight} tons. Please select a truck with higher capacity or reduce weight.`,
             variant: "destructive"
           });
           // Do NOT select the truck if it exceeds capacity and weight is valid
           // Optionally, clear the truckType field here: form.setValue("truckType", ""); setSelectedTruckType(null);
           return;
       }

      setSelectedTruckType(id);
      form.setValue("truckType", id, { shouldValidate: true }); // Update form state and validate
      // No multi-step logic needed here
  };

  // Calculate estimated price based on form inputs
  const calculateEstimatedPrice = (data: z.infer<typeof bookingFormSchema>): number => {
    const basePricePerTon = 100000;
    const truckMultipliers: Record<string, number> = {
      "small-truck": 1,
      "medium-truck": 1.2,
      "large-truck": 1.5,
      "refrigerated-truck": 2,
    };
    // Simplified distance logic for demo - could be replaced with a lookup or API call
    const routeKey = `${data.origin.toLowerCase()}-${data.destination.toLowerCase()}`;
     let distanceMultiplier = 1.5; // Default multiplier for unknown routes

    // Example lookup based on popular routes (simplified - not actual distance calculation)
    if (routeKey === "kampala-jinja") distanceMultiplier = 1;
    else if (routeKey === "kampala-mbarara") distanceMultiplier = 1.8;
    else if (routeKey === "gulu-kampala") distanceMultiplier = 2.2;
    else if (routeKey === "mbale-kampala") distanceMultiplier = 1.8;


    const weight = parseFloat(data.weight) || 0;
    if (weight <= 0) return 0; // No price for zero or negative weight

    const truckMultiplier = truckMultipliers[data.truckType] || 1;

    const price = basePricePerTon * weight * truckMultiplier * distanceMultiplier;

    // Add a small random factor or fixed fee for variation
    const finalPrice = Math.round(price * (1 + (Math.random() - 0.5) * 0.1)); // +/- 5% random variation
    return Math.max(100000, finalPrice); // Ensure a minimum price
  };

  // Calculate estimated arrival date
  const calculateEstimatedArrival = (pickupDateStr: string): string => {
    const date = new Date(pickupDateStr);
    if (isNaN(date.getTime())) return "N/A";
    // Simple logic: 1 day for same/nearby region, 2-3 days for further distances
    const routeKey = `${form.getValues("origin").toLowerCase()}-${form.getValues("destination").toLowerCase()}`;
    let transitDays = 1;
    if (routeKey === "kampala-mbarara" || routeKey === "gulu-kampala" || routeKey === "mbale-kampala") transitDays = 2;
    if (routeKey === "gulu-mbarara") transitDays = 3; // Example for a very long route

    date.setDate(date.getDate() + transitDays);
     // Return in YYYY-MM-DD format
    return date.toISOString().split('T')[0];
  };

  // On form submission handler - determines whether to calculate price or proceed to payment
  const onSubmit = async (data: z.infer<typeof bookingFormSchema>) => {
    // Validate all fields before proceeding
    const isValid = await form.trigger(); // Trigger validation for all fields
    if (!isValid) {
        toast({
           title: "Validation Error",
           description: "Please fix the errors in the form.",
           variant: "destructive"
       });
        return; // Stop if form is not valid
    }

    if (estimatedPrice === null || !estimateMadeRef.current) {
      // First submission: Calculate price and show summary
      const price = calculateEstimatedPrice(data);
      setEstimatedPrice(price);
      setIsBookingConfirmed(false); // Reset confirmation status
      estimateMadeRef.current = true; // Mark that estimate has been made
      toast({ title: "Price Estimated", description: "Review the summary below and click 'Proceed to Payment' to continue." });
    } else {
      // Second submission: Proceed to payment (assuming price is already estimated)

      // Re-calculate price to ensure it hasn't changed unexpectedly
      const currentPrice = calculateEstimatedPrice(data);
      if (currentPrice !== estimatedPrice) {
         // If data changed since estimate, update price and prompt re-review
         setEstimatedPrice(currentPrice);
         estimateMadeRef.current = true; // Keep estimate marked as made
         setIsBookingConfirmed(false); // Reset confirmation
         toast({ title: "Price Updated", description: "Booking details or route factors changed. Please review the new estimated price and proceed to payment.", variant: "default"}); // Use default or info variant
         return; // Stop submission, user needs to click again
      }

      // Store data for payment modal and open it
      setBookingDetailsForPayment(data);
      setIsPaymentModalOpen(true);
    }
  };

  // Handler after payment is processed (mock)
  const finalizeBookingAfterPayment = (paymentMethod: string) => {
    if (!bookingDetailsForPayment || estimatedPrice === null) return;

    // Simulate creating a new booking entry
    const bookingId = `TR-${Math.floor(Math.random() * 90000) + 10000}`; // Simple unique ID mock
    const truckName = truckTypes.find(t => t.id === bookingDetailsForPayment.truckType)?.name || "Unknown Truck";
    const arrivalDate = calculateEstimatedArrival(bookingDetailsForPayment.pickupDate);
    const commodityName = commodities.find(c => c.name.toLowerCase() === bookingDetailsForPayment.commodity)?.name || bookingDetailsForPayment.commodity;

    const newBooking: ConfirmedBooking = {
      id: bookingId,
      origin: bookingDetailsForPayment.origin,
      destination: bookingDetailsForPayment.destination,
      commodity: commodityName,
      quantity: `${bookingDetailsForPayment.weight} tons`, // Store as string for display
      weight: parseFloat(bookingDetailsForPayment.weight), // Store numeric weight
      volume: bookingDetailsForPayment.volume ? parseFloat(bookingDetailsForPayment.volume) : undefined,
      specialRequirements: bookingDetailsForPayment.specialRequirements,
      truckType: truckName, // Store truck name
      pickupDate: bookingDetailsForPayment.pickupDate,
      pickupTime: bookingDetailsForPayment.pickupTime,
      contactName: bookingDetailsForPayment.contactName,
      contactPhone: bookingDetailsForPayment.contactPhone,
      estimatedArrival: arrivalDate,
      price: estimatedPrice,
      status: 'scheduled', // Initial status
      driverDetails: { name: "Pending Assignment", contact: "N/A" }, // Default driver details
      trackingUpdates: [{ timestamp: new Date().toISOString().slice(0, 19).replace('T', ' '), location: "System", statusMessage: "Booking confirmed, awaiting driver assignment."}] // Initial tracking update
    };

    setConfirmedBookings(prevBookings => [newBooking, ...prevBookings]); // Add new booking to the list

    // Close payment modal
    setIsPaymentModalOpen(false);

    // Set booking confirmed state to show success message/UI
    setIsBookingConfirmed(true);

    // Show success toast notification
    toast({ title: "Booking Confirmed!", description: `Payment successful via ${paymentMethod}. Your booking ID is ${newBooking.id}.`, className: "bg-green-100 text-green-800 border-green-300", duration: 6000 });

    // Optional: Reset the form after a delay to allow user to see confirmation message
     setTimeout(() => {
         form.reset();
         setSelectedTruckType(null);
         setEstimatedPrice(null);
         setIsBookingConfirmed(false);
         setBookingDetailsForPayment(null); // Clear booking details for payment
         estimateMadeRef.current = false; // Reset estimate flag
         // Optionally switch to 'My Shipments' tab
          // document.querySelector('button[value="my-shipments"]')?.click(); // This is a DOM manipulation, less React-idiomatic
          // A better way is using state if tabs component allows controlling active tab via prop
          // Example (assuming Tabs component supports controlled mode):
          // setActiveTab("my-shipments"); // If Tabs value prop is controlled by `activeTab` state
     }, 4000); // Reset after 4 seconds
  };

  // Mock Payment Processing Handlers
  const handleCardPaymentProcessing = () => { toast({ title: "Processing Card Payment...", description: "Please wait." }); setTimeout(() => finalizeBookingAfterPayment("Card"), 1500); };
  
  const handleWalletPaymentProcessing = async () => { // Made async
    if (estimatedPrice === null) return;
    
    if (userWalletBalance >= estimatedPrice) {
      // NEW: Call context function for payment
      const paymentSuccessful = await spendFromWallet(estimatedPrice, "Transportation Service", `Booking ID (to be generated): ${bookingDetailsForPayment?.origin} to ${bookingDetailsForPayment?.destination}`);
      
      if (paymentSuccessful) {
        finalizeBookingAfterPayment("Wallet");
      } else {
        // Toast for payment failure is handled by spendFromWallet, but you might want additional specific UI updates here.
        toast({
            title: "Wallet Payment Failed",
            description: "Could not process payment from wallet. Please check balance or try another method.",
            variant: "destructive"
        });
      }
    } else {
      toast({
        title: "Insufficient Wallet Balance",
        description: (
          <div>
            Your wallet balance is {userWalletBalance.toLocaleString()} UGX.<br/>
            Required: {estimatedPrice.toLocaleString()} UGX.
            <Button 
              variant="link" 
              className="p-0 h-auto ml-1 text-blue-600 hover:underline" 
              onClick={() => { 
                setIsPaymentModalOpen(false); 
                setIsFundWalletModalOpen(true); 
              }}
            > 
              Fund your wallet? 
            </Button>
          </div>
        ),
        variant: "destructive",
        duration: 7000
      });
    }
  };

  // Fund Wallet Modal Handler
  const handleFundWalletDeposit = () => {
    const amount = parseFloat(depositAmount);
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid positive amount to deposit.",
        variant: "destructive"
      });
      return;
    }

    const selectedMethod = fundingPaymentMethods.find(m => m.key === selectedFundingMethodKey);

    if (!selectedMethod) {
      toast({ title: "Payment Method Required", description: "Please select a payment method to fund your wallet.", variant: "destructive" });
      return;
    }
    
    if (selectedMethod.type === "mobile_money" && (!fundingPhoneNumber || !/^\\d{9,10}$/.test(fundingPhoneNumber))) {
      toast({ title: "Invalid Phone Number", description: `Please enter a valid 9 or 10 digit phone number for ${selectedMethod.name}.`, variant: "destructive"});
      return;
    }
  
    toast({ 
      title: "Processing Deposit...", 
      description: "Please wait.", 
      duration: 1500
    });
  
    setTimeout(() => {
      // Construct PaymentMethod-like object and accountDetails for the context function
      // This matches the structure expected by WalletContext's depositFunds
      const paymentMethodForContext = {
        key: selectedMethod.key,
        name: selectedMethod.name,
        type: selectedMethod.type as "mobile_money" | "card" | "bank", // Cast to known types if necessary
        icon: selectedMethod.icon, // Assuming PaymentMethod in context can take ReactNode
        // description, placeholder, note - if needed by context's PaymentMethod type
      };
      
      const accountDetailsForContext = selectedMethod.type === "mobile_money" ? { phone: fundingPhoneNumber } : {};

      fundUserWallet(amount, paymentMethodForContext as any, accountDetailsForContext).then(() => { // Keep `as any` for paymentMethodForContext if its type isn't fully compatible.
         toast({
            title: "Wallet Funded Successfully!",
            description: `${amount.toLocaleString()} UGX has been added. Your new balance will reflect shortly.`,
            className: "bg-green-100 text-green-800 border-green-300",
            duration: 5000
        });
        setIsFundWalletModalOpen(false);
        setDepositAmount("");
        setFundingPhoneNumber("");
        setSelectedFundingMethodKey(null);
      }).catch((error) => {
        console.error("Funding failed:", error)
        toast({
            title: "Funding Failed",
            description: error?.message || "Could not add funds to wallet. Please try again.",
            variant: "destructive"
        });
      });
    }, 1500);
  };

  // Status badge styling function
  const getStatusBadge = (status: ConfirmedBooking['status']) => {
    switch(status) {
      case 'delivered': return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">Delivered</Badge>;
      case 'in-transit': return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">In Transit</Badge>;
      case 'scheduled': return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">Scheduled</Badge>;
      case 'cancelled': return <Badge variant="destructive">Cancelled</Badge>;
      default: return <Badge variant="outline">Unknown</Badge>;
    }
  };

  // Handler to open tracking modal
  const handleTrackShipment = (booking: ConfirmedBooking | null) => {
      if (booking) {
          setTrackingShipmentDetails(booking);
          setIsTrackingModalOpen(true);
      } else {
          // Handle tracking by ID if needed (not fully implemented in this combined version)
          toast({
              title: "Feature Note",
              description: "Tracking by ID requires a separate input field and lookup logic.",
              variant: "default"
          });
      }
  };

  // Function to get today's date in YYYY-MM-DD format for date picker min attribute
  const getMinDate = () => {
    return new Date().toISOString().split('T')[0];
  };


  return (
    <div className="container mx-auto py-8">
      {/* Toaster component should be rendered at the root of your app */}
      {/* <Toaster /> */}

      <div className="flex flex-col space-y-2 mb-8">
        <h1 className="text-3xl font-bold">Transportation Services</h1>
        <p className="text-muted-foreground">Book and manage transport for your agricultural commodities</p>
      </div>

      <Tabs defaultValue="book-transport" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="book-transport">Book Transport</TabsTrigger>
          <TabsTrigger value="my-shipments">My Shipments</TabsTrigger>
          <TabsTrigger value="popular-routes">Popular Routes</TabsTrigger>
        </TabsList>

        {/* BOOK TRANSPORT TAB */}
        <TabsContent value="book-transport">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <Card className="lg:col-span-2">
              <CardHeader><CardTitle>Book a Truck</CardTitle><CardDescription>Fill in the details to book a truck</CardDescription></CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    {/* Form Fields */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <FormField control={form.control} name="origin" render={({ field }) => (
                          <FormItem>
                            <FormLabel>Origin</FormLabel>
                            <FormControl>
                              {/* Using Input for origin/destination, could be Select/Autocomplete */}
                              <Input placeholder="Enter pickup location" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField control={form.control} name="destination" render={({ field }) => (
                          <FormItem>
                            <FormLabel>Destination</FormLabel>
                            <FormControl>
                              {/* Using Input for origin/destination, could be Select/Autocomplete */}
                              <Input placeholder="Enter delivery location" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <FormField control={form.control} name="commodity" render={({ field }) => (
                          <FormItem>
                            <FormLabel>Commodity</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select commodity" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {/* Assuming commodities is an array of {id, name} objects */}
                                {commodities.map((c) => (
                                  <SelectItem key={c.id} value={c.name.toLowerCase()}>{c.name}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField control={form.control} name="weight" render={({ field }) => (
                          <FormItem>
                            <FormLabel>Weight (tons)</FormLabel>
                            <FormControl>
                              <Input type="number" min="0.1" step="0.1" placeholder="Enter weight in tons" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                     <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                       <FormField control={form.control} name="volume" render={({ field }) => (
                           <FormItem>
                             <FormLabel>Volume (m³) <span className="text-muted-foreground font-normal">(Optional)</span></FormLabel>
                             <FormControl>
                               <Input type="number" min="0" step="0.1" placeholder="Enter volume if applicable" {...field} />
                             </FormControl>
                             <FormMessage />
                           </FormItem>
                         )}
                       />
                        <FormField control={form.control} name="pickupDate" render={({ field }) => (
                           <FormItem>
                             <FormLabel>Pickup Date</FormLabel>
                             <FormControl>
                               {/* Use getMinDate to prevent past dates */}
                               <Input type="date" {...field} min={getMinDate()} />
                             </FormControl>
                             <FormDescription>Select the date for pickup</FormDescription>
                             <FormMessage />
                           </FormItem>
                         )}
                       />
                     </div>

                     <FormField control={form.control} name="specialRequirements" render={({ field }) => (
                         <FormItem>
                           <FormLabel>Special Requirements <span className="text-muted-foreground font-normal">(Optional)</span></FormLabel>
                           <FormControl>
                             <Input placeholder="E.g., Temperature control, fragile handling" {...field} />
                           </FormControl>
                           <FormMessage />
                         </FormItem>
                       )}
                     />

                    <FormField control={form.control} name="truckType" render={({ field }) => (
                        <FormItem>
                          <FormLabel>Truck Type</FormLabel>
                          <FormControl>
                            {/* Truck selection UI - uses handleTruckTypeSelect */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-2">
                              {truckTypes.map((truck) => (
                                <div
                                  key={truck.id}
                                  onClick={() => handleTruckTypeSelect(truck.id)}
                                  // Highlight selected truck type
                                  className={cn("flex items-start p-4 border rounded-lg cursor-pointer hover:bg-secondary/50 transition-colors", selectedTruckType === truck.id ? "border-primary bg-primary/5" : "border-border")}
                                >
                                  <div className="mr-4">{truck.icon}</div>
                                  <div>
                                    <h4 className="font-medium">{truck.name}</h4>
                                    <p className="text-sm text-muted-foreground">{truck.capacity}</p>
                                    <p className="text-xs text-muted-foreground">Max: {truck.maxCapacityTons} tons</p>
                                    {/* Show available count */}
                                    <div className="mt-1 text-xs text-primary">{truck.availableCount} available</div>
                                    <div className="mt-1 text-xs">Best for: {truck.suitable.join(", ")}</div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </FormControl>
                           {/* Hidden input to connect custom UI to form state */}
                          <Input type="hidden" {...field} />
                          <FormMessage /> {/* Displays validation error related to truckType */}
                        </FormItem>
                      )}
                    />

                     <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                       <FormField control={form.control} name="contactName" render={({ field }) => (
                           <FormItem>
                             <FormLabel>Contact Name at Pickup <span className="text-muted-foreground font-normal">(Optional)</span></FormLabel>
                             <FormControl>
                               <Input placeholder="Name of person at pickup location" {...field} />
                             </FormControl>
                             <FormMessage />
                           </FormItem>
                         )}
                       />
                        <FormField control={form.control} name="contactPhone" render={({ field }) => (
                           <FormItem>
                             <FormLabel>Contact Phone at Pickup <span className="text-muted-foreground font-normal">(Optional)</span></FormLabel>
                             <FormControl>
                               <Input type="tel" placeholder="Phone number" {...field} />
                             </FormControl>
                             <FormMessage />
                           </FormItem>
                         )}
                       />
                     </div>

                    {/* Display Booking Summary and Estimated Price after first submission */}
                    {estimatedPrice !== null && ( // Only show summary if price is estimated
                      <div className="mt-6 p-4 bg-primary/5 border border-primary/20 rounded-lg">
                        <h3 className="text-lg font-medium mb-2">Booking Summary</h3>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <span className="text-muted-foreground">Origin:</span><span className="font-medium capitalize">{form.getValues("origin")}</span>
                          <span className="text-muted-foreground">Destination:</span><span className="font-medium capitalize">{form.getValues("destination")}</span>
                          <span className="text-muted-foreground">Commodity:</span><span className="font-medium capitalize">{commodities.find(c=>c.name.toLowerCase() === form.getValues("commodity"))?.name || form.getValues("commodity")}</span>
                          <span className="text-muted-foreground">Weight:</span><span className="font-medium">{form.getValues("weight")} tons</span>
                           {form.getValues("volume") && ( // Show volume if entered
                               <>
                                 <span className="text-muted-foreground">Volume:</span>
                                 <span className="font-medium">{form.getValues("volume")} m³</span>
                               </>
                            )}
                           {form.getValues("specialRequirements") && ( // Show requirements if entered
                               <>
                                 <span className="text-muted-foreground">Requirements:</span>
                                 <span className="font-medium">{form.getValues("specialRequirements")}</span>
                               </>
                           )}
                          <span className="text-muted-foreground">Truck Type:</span><span className="font-medium">{truckTypes.find(t => t.id === form.getValues("truckType"))?.name}</span>
                          <span className="text-muted-foreground">Pickup Date:</span><span className="font-medium">{form.getValues("pickupDate")}</span>
                           {form.getValues("pickupTime") && ( // Show pickup time if entered
                               <>
                                 <span className="text-muted-foreground">Pickup Time:</span>
                                 <span className="font-medium">{form.getValues("pickupTime")}</span>
                               </>
                            )}
                           {form.getValues("contactName") && ( // Show contact name if entered
                               <>
                                 <span className="text-muted-foreground">Contact Name:</span>
                                 <span className="font-medium">{form.getValues("contactName")}</span>
                               </>
                            )}
                            {form.getValues("contactPhone") && ( // Show contact phone if entered
                                <>
                                  <span className="text-muted-foreground">Contact Phone:</span>
                                  <span className="font-medium">{form.getValues("contactPhone")}</span>
                                </>
                             )}
                        </div>
                        <div className="mt-4 flex flex-col items-end">
                            <div className="text-muted-foreground text-sm">Estimated Price:</div>
                            <div className="text-2xl font-bold text-primary">{estimatedPrice.toLocaleString()} UGX</div> {/* Highlight price */}
                        </div>
                      </div>
                    )}

                     {/* Payment Method Selection (Only visible after price is estimated) */}
                     {estimatedPrice !== null && (
                        <FormField control={form.control} name="paymentMethod" render={({ field }) => (
                            <FormItem>
                                <FormLabel>Payment Method</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                        <SelectTrigger><SelectValue placeholder="Select payment method" /></SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {/* Ensure userWalletBalance here is from context and up-to-date */}
                                        <SelectItem value="wallet">Wallet (Balance: UGX {userWalletBalance.toLocaleString()})</SelectItem>
                                        <SelectItem value="mobile-money">Mobile Money</SelectItem>
                                        <SelectItem value="card">Credit/Debit Card</SelectItem>
                                        <SelectItem value="bank">Bank Transfer</SelectItem>
                                    </SelectContent>
                                </Select>
                                <FormDescription>Select your preferred payment method</FormDescription>
                                <FormMessage />
                            </FormItem>
                         )}
                       />
                     )}


                    {/* Submit Button */}
                    <Button type="submit" className="w-full" disabled={form.formState.isSubmitting}>
                      {/* Change button text based on whether price is estimated */}
                      {estimatedPrice !== null ? (
                          <>
                            <CreditCard className="mr-2 h-4 w-4" /> Proceed to Payment
                          </>
                      ) : (
                          <>
                             <CircleDollarSign className="mr-2 h-4 w-4" /> Calculate Price & Availability
                          </>
                      )}
                    </Button>

                    {/* Booking Confirmed Message */}
                    {isBookingConfirmed && (
                         <div className="flex items-center justify-center p-4 bg-green-100 text-green-800 rounded-lg mt-4 border border-green-300">
                             <CheckCircle className="mr-2 h-5 w-5" />Booking confirmed successfully! Check "My Shipments" tab.
                         </div>
                     )}
                  </form>
                </Form>
              </CardContent>
            </Card>

            {/* SIDEBAR */}
            <div className="space-y-6">
                {/* General Overview Card */}
                <Card>
                    <CardHeader><CardTitle>Transportation Overview</CardTitle></CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="flex items-center"><Truck className="h-5 w-5 mr-2 text-primary" /><div className="text-sm">{truckTypes.reduce((sum, truck) => sum + truck.availableCount, 0)}+ trucks available</div></div> {/* Summing up available trucks */}
                            <div className="flex items-center"><Route className="h-5 w-5 mr-2 text-primary" /><div className="text-sm">Nationwide coverage in Uganda</div></div>
                            <div className="flex items-center"><Clock className="h-5 w-5 mr-2 text-primary" /><div className="text-sm">24/7 support</div></div>
                            <div className="flex items-center"><Navigation className="h-5 w-5 mr-2 text-primary" /><div className="text-sm">Real-time GPS tracking</div></div>
                        </div>
                    </CardContent>
                     <CardFooter>
                         {/* Contact Support AlertDialog Trigger */}
                        <AlertDialog>
                            <AlertDialogTrigger asChild>
                                <Button variant="outline" size="sm" className="w-full"><Phone className="mr-2 h-4 w-4" /> Contact Support</Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                                <AlertDialogHeader><AlertDialogTitle>Contact Support</AlertDialogTitle><AlertDialogDescription>Choose a contact method.</AlertDialogDescription></AlertDialogHeader>
                                <div className="grid gap-4 py-4">
                                    <div className="flex items-center"><PhoneCall className="mr-2 h-4 w-4 text-green-500" /><a href="tel:+256757220113" className="hover:underline">+256757220113</a></div> {/* Added tel: schema */}
                                    <div className="flex items-center"><Mail className="mr-2 h-4 w-4 text-blue-500" /><a href="mailto:<EMAIL>" className="hover:underline"><EMAIL></a></div>
                                    {/* Add WhatsApp link */}
                                    <div className="flex items-center"><Smartphone className="mr-2 h-4 w-4 text-green-600" /><a href="https://wa.me/256757220113" target="_blank" rel="noopener noreferrer" className="hover:underline">WhatsApp +256757220113 <ExternalLink className="ml-1 inline-block h-3 w-3"/></a></div>
                                </div>
                                <AlertDialogFooter><AlertDialogCancel>Close</AlertDialogCancel></AlertDialogFooter>
                            </AlertDialogContent>
                        </AlertDialog>
                     </CardFooter>
                </Card>
                {/* Quick Quote Card (Popular Routes) */}
                <Card>
                    <CardHeader><CardTitle>Quick Quote</CardTitle><CardDescription>Common routes & average prices</CardDescription></CardHeader>
                    <CardContent>
                         <div className="space-y-2">
                             {popularRoutes.map((r, i)=>(
                                 <div key={i} className="flex justify-between items-center py-2 border-b last:border-b-0">
                                     <div className="flex items-center">
                                         <MapPin className="h-4 w-4 mr-2 text-primary" />
                                         <div className="text-sm font-medium">{r.origin} <span className="text-muted-foreground">to</span> {r.destination}</div>
                                     </div>
                                     <div className="text-sm font-medium text-right">{r.price}</div>
                                 </div>
                             ))}
                         </div>
                    </CardContent>
                    <CardFooter><Button variant="outline" className="w-full">View All Routes</Button></CardFooter> {/* Placeholder */}
                </Card>
            </div>
          </div>
        </TabsContent>

        {/* MY SHIPMENTS TAB */}
        <TabsContent value="my-shipments">
          <Card>
            <CardHeader><CardTitle>My Shipments</CardTitle><CardDescription>Track your confirmed bookings</CardDescription></CardHeader>
            <CardContent>
              {confirmedBookings.length > 0 ? (
                <div className="space-y-4">
                  {confirmedBookings.map((booking) => (
                    <div key={booking.id} className="p-4 border rounded-lg">
                      <div className="flex flex-col md:flex-row md:justify-between">
                        <div className="flex-grow">
                          <div className="flex items-center flex-wrap mb-1">
                            <Package className="h-5 w-5 mr-2 text-primary" />
                            <span className="font-semibold text-lg">{booking.id}</span>
                            <span className="mx-2 text-muted-foreground">•</span>
                            {getStatusBadge(booking.status)}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            <span className="font-medium">{booking.commodity}</span> ({booking.quantity})
                             {/* Optionally show numeric weight if different from quantity string */}
                            {booking.weight !== parseFloat(booking.quantity.split(' ')[0]) && (
                                <span className="text-xs ml-1">({booking.weight} tons)</span>
                            )}
                          </p>
                          <p className="text-sm mt-1">
                            <span className="font-medium capitalize">{booking.origin}</span>
                            <Route size={16} className="inline mx-1 text-muted-foreground" />
                            <span className="font-medium capitalize">{booking.destination}</span>
                          </p>
                          <p className="text-sm text-muted-foreground mt-1">Truck: <span className="font-medium">{booking.truckType}</span></p>
                        </div>
                        <div className="flex flex-col md:items-end mt-4 md:mt-0 md:text-right flex-shrink-0">
                          <div className="flex items-center space-x-2 text-sm">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            <span>Pickup: {booking.pickupDate}</span>
                          </div>
                           {booking.pickupTime && ( // Show pickup time if available
                               <div className="flex items-center space-x-2 text-sm">
                                 <Clock className="h-4 w-4 text-muted-foreground" />
                                 <span>Time: {booking.pickupTime}</span>
                               </div>
                            )}
                          <div className="flex items-center space-x-2 text-sm">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            <span>Est. Arrival: {booking.estimatedArrival}</span>
                          </div>
                           {booking.currentLocation && booking.status === 'in-transit' && ( // Show current location if in transit
                              <div className="flex items-center space-x-2 text-sm text-primary font-medium">
                                 <MapPin className="h-4 w-4" />
                                 <span>Current: {booking.currentLocation}</span>
                              </div>
                           )}
                          <p className="text-sm text-muted-foreground mt-2">Price: <span className="font-semibold text-lg text-primary">{booking.price.toLocaleString()} UGX</span></p>
                        </div>
                      </div>
                      <div className="mt-4 border-t pt-3 flex justify-end">
                         {/* Pass the full booking object to tracking handler */}
                        <Button variant="outline" size="sm" onClick={() => handleTrackShipment(booking)}><Navigation className="mr-2 h-4 w-4"/> Track Shipment</Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                 <div className="text-center py-8 text-muted-foreground">
                     <ShoppingCart size={48} className="mx-auto mb-2" /> {/* Changed icon to ShoppingCart */}
                     <p>No confirmed shipments yet.</p>
                     <p>Book a transport to see it here.</p>
                 </div>
              )}
            </CardContent>
            {confirmedBookings.length > 0 && ( <CardFooter><Button variant="ghost" className="w-full text-primary hover:bg-primary/10">View All Shipments</Button></CardFooter> )} {/* Placeholder */}
          </Card>
        </TabsContent>

        {/* POPULAR ROUTES TAB */}
        <TabsContent value="popular-routes">
             <div className="space-y-6">
                 <Card>
                     <CardHeader>
                         <CardTitle>Popular Transport Routes</CardTitle>
                         <CardDescription>Browse common routes and estimated prices</CardDescription>
                     </CardHeader>
                     <CardContent>
                          <div className="grid gap-4 md:grid-cols-2">
                              {popularRoutes.map((route, index) => (
                                 <Card
                                     key={index}
                                     className="overflow-hidden cursor-pointer transition-all hover:shadow-md"
                                     // When card is clicked, switch to book tab and fill route fields
                                     onClick={() => {
                                         form.setValue("origin", route.origin, { shouldValidate: true });
                                         form.setValue("destination", route.destination, { shouldValidate: true });
                                         (document.querySelector('button[value="book-transport"]') as HTMLButtonElement)?.click(); // Click the tab trigger button
                                     }}
                                 >
                                     <CardHeader className="pb-2">
                                         <CardTitle className="text-lg capitalize">{route.origin} to {route.destination}</CardTitle> {/* Capitalize origin/destination */}
                                     </CardHeader>
                                     <CardContent className="pb-2">
                                         <div className="flex items-center justify-between">
                                             <div className="flex items-center text-muted-foreground">
                                                 <Route className="mr-2 h-4 w-4" />
                                                 <span>Average Price Estimate:</span>
                                             </div>
                                             <Badge variant="secondary">{route.price}</Badge>
                                         </div>
                                     </CardContent>
                                      {/* Remove duplicate button from card footer */}
                                      {/* <CardFooter className="border-t bg-muted/50 px-6 py-3">
                                          <Button
                                              variant="ghost"
                                              size="sm"
                                              className="w-full"
                                              onClick={() => {}} // No-op as click is on card
                                          >
                                              Select This Route
                                          </Button>
                                      </CardFooter> */}
                                 </Card>
                              ))}
                          </div>
                     </CardContent>
                      <CardFooter><Button variant="outline" className="w-full">View More Routes</Button></CardFooter> {/* Placeholder */}
                 </Card>

                 <Card>
                     <CardHeader>
                         <CardTitle>Custom Route</CardTitle>
                         <CardDescription>
                             Need to transport goods on a route not listed above? Book a custom route
                         </CardDescription>
                     </CardHeader>
                     <CardContent className="flex flex-wrap gap-4 items-center">
                         <Button
                             variant="default"
                             onClick={() => {
                                 // Simply switch to the book transport tab
                                 (document.querySelector('button[value="book-transport"]') as HTMLButtonElement)?.click();
                             }}
                         >
                           <Truck className="mr-2 h-4 w-4" />
                           Book Custom Route
                         </Button>
                         {/* Removed redundant contact support button */}
                         {/* <Button variant="outline" onClick={handleContactSupport}>
                           <Phone className="mr-2 h-4 w-4" />
                           Contact Transportation Team
                         </Button> */}
                     </CardContent>
                 </Card>
             </div>
        </TabsContent>

      </Tabs>

      {/* Payment Modal (AlertDialog) */}
      {/* Only render if bookingDetailsForPayment and estimatedPrice are available */}
      {bookingDetailsForPayment && estimatedPrice !== null && (
        <AlertDialog open={isPaymentModalOpen} onOpenChange={setIsPaymentModalOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Complete Your Payment</AlertDialogTitle>
              <AlertDialogDescription>Amount Due: <span className="font-bold text-primary">{estimatedPrice.toLocaleString()} UGX</span></AlertDialogDescription> {/* Highlight amount due */}
              {/* Ensure userWalletBalance here is from context and up-to-date */}
              <div className="mt-2 pt-2 border-t text-sm">Your Wallet Balance: <span className="font-semibold text-primary">{userWalletBalance.toLocaleString()} UGX</span></div>
            </AlertDialogHeader>
            <div className="my-4 space-y-1 text-sm py-3 border-y bg-muted/50 rounded-md p-4"> {/* Added styling to summary */}
              <h4 className="font-semibold mb-2 text-base">Booking Summary:</h4>
              <p><strong>Origin:</strong> <span className="capitalize">{bookingDetailsForPayment.origin}</span></p>
              <p><strong>Destination:</strong> <span className="capitalize">{bookingDetailsForPayment.destination}</span></p>
              <p><strong>Commodity:</strong> {commodities.find(c=>c.name.toLowerCase() === bookingDetailsForPayment.commodity)?.name || bookingDetailsForPayment.commodity} ({bookingDetailsForPayment.weight} tons)</p>
              <p><strong>Truck:</strong> {truckTypes.find(t => t.id === bookingDetailsForPayment.truckType)?.name}</p>
              <p><strong>Pickup Date:</strong> {bookingDetailsForPayment.pickupDate}</p>
               {bookingDetailsForPayment.pickupTime && <p><strong>Pickup Time:</strong> {bookingDetailsForPayment.pickupTime}</p>}
               {bookingDetailsForPayment.contactName && <p><strong>Contact Name:</strong> {bookingDetailsForPayment.contactName}</p>}
               {bookingDetailsForPayment.contactPhone && <p><strong>Contact Phone:</strong> {bookingDetailsForPayment.contactPhone}</p>}
               {bookingDetailsForPayment.specialRequirements && <p><strong>Requirements:</strong> {bookingDetailsForPayment.specialRequirements}</p>}
            </div>
             {/* Mock card input - could be replaced by real payment form */}
            <div className="space-y-2"><Label htmlFor="cardNumberMock">Card Number (mock)</Label><Input id="cardNumberMock" placeholder="xxxx xxxx xxxx 1234" /></div>
             {/* Payment method specific inputs would go here in a real app */}

            <AlertDialogFooter className="mt-6 flex flex-col sm:flex-row sm:justify-end gap-2">
              <Button variant="outline" onClick={() => {setIsPaymentModalOpen(false); setBookingDetailsForPayment(null);}} className="w-full sm:w-auto">Cancel</Button> {/* Added logic to clear state on cancel */}
              <Button onClick={() => { setIsPaymentModalOpen(false); setIsFundWalletModalOpen(true); }} variant="secondary" className="w-full sm:w-auto"><Wallet className="mr-2 h-4 w-4" /> Fund Wallet</Button>
               {/* Payment Buttons */}
              <Button onClick={handleCardPaymentProcessing} className="bg-blue-600 hover:bg-blue-700 text-white w-full sm:w-auto"><CreditCard className="mr-2 h-4 w-4" /> Pay with Card</Button>
              <Button onClick={handleWalletPaymentProcessing} className={cn("text-white w-full sm:w-auto", (estimatedPrice !== null && userWalletBalance < estimatedPrice) ? "bg-gray-400 hover:bg-gray-400 cursor-not-allowed" : "bg-green-600 hover:bg-green-700")} disabled={estimatedPrice !== null && userWalletBalance < estimatedPrice}>
                <Wallet className="mr-2 h-4 w-4" /> Pay with Wallet
                {estimatedPrice !== null && userWalletBalance < estimatedPrice && <Badge variant="destructive" className="ml-2 text-xs">Insufficient</Badge>}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      {/* Fund Wallet Modal (AlertDialog) */}
      <AlertDialog open={isFundWalletModalOpen} onOpenChange={setIsFundWalletModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader><AlertDialogTitle>Fund Your Wallet</AlertDialogTitle><AlertDialogDescription>Current Balance: <span className="font-semibold text-primary">{userWalletBalance.toLocaleString()} UGX</span>. Select amount and payment method.</AlertDialogDescription></AlertDialogHeader> {/* Highlight balance */}
          <div className="py-4 space-y-4">
            <div className="space-y-2"><Label htmlFor="depositAmount">Amount to Deposit (UGX)</Label><Input id="depositAmount" type="number" value={depositAmount} onChange={(e) => setDepositAmount(e.target.value)} placeholder="e.g., 50000" min="1000"/></div>
            
            <div>
              <Label className="mb-2 block">Choose Payment Method:</Label>
              <div className="space-y-2">
                {fundingPaymentMethods.map((method) => (
                  <Button 
                    key={method.key}
                    variant={selectedFundingMethodKey === method.key ? "default" : "outline"} 
                    className="w-full justify-start text-left h-auto py-2" 
                    onClick={() => setSelectedFundingMethodKey(method.key)}
                  >
                    {method.icon}
                    <div>
                      {method.name}
                      {method.type === "card" && <p className="text-xs text-muted-foreground">{method.description}</p>}
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {selectedFundingMethodKey && fundingPaymentMethods.find(m => m.key === selectedFundingMethodKey)?.type === "mobile_money" && (
              <div className="space-y-2 pt-2">
                <Label htmlFor="fundingPhoneNumber">Phone Number</Label>
                <Input 
                  id="fundingPhoneNumber" 
                  type="tel" 
                  value={fundingPhoneNumber} 
                  onChange={(e) => setFundingPhoneNumber(e.target.value)} 
                  placeholder={fundingPaymentMethods.find(m => m.key === selectedFundingMethodKey)?.placeholder || "Enter phone number"} 
                />
              </div>
            )}
             {/* Placeholder for Card details if that method is selected and implemented */}
             {/* {selectedFundingMethodKey && fundingPaymentMethods.find(m => m.key === selectedFundingMethodKey)?.type === "card" && (
                <div className="space-y-2 pt-2 border-t">
                    <p className="text-sm font-medium">Card details would go here.</p>
                     <Input placeholder="Card Number" />
                     <Input placeholder="MM/YY" />
                     <Input placeholder="CVC" />
                </div>
            )} */}

          </div>
          <AlertDialogFooter>
             {/* Cancel button clears input */}
             <AlertDialogCancel onClick={() => {setIsFundWalletModalOpen(false); setDepositAmount(""); setFundingPhoneNumber(""); setSelectedFundingMethodKey(null);}}>Cancel</AlertDialogCancel>
             {/* Confirm button triggers deposit simulation */}
            <AlertDialogAction onClick={handleFundWalletDeposit} className="bg-green-600 hover:bg-green-700">Confirm Deposit</AlertDialogAction>
            </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Shipment Tracking Modal (Dialog) */}
      <Dialog open={isTrackingModalOpen} onOpenChange={setIsTrackingModalOpen}>
        <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto"> {/* Added max-height and overflow */}
          <DialogHeader>
            <DialogTitle>Track Shipment: {trackingShipmentDetails?.id}</DialogTitle>
            <DialogDescription>Details for shipment from <span className="capitalize">{trackingShipmentDetails?.origin}</span> to <span className="capitalize">{trackingShipmentDetails?.destination}</span>.</DialogDescription> {/* Capitalize locations */}
          </DialogHeader>
          {trackingShipmentDetails && (
            <div className="py-4 space-y-6">
              {/* Shipment Progress Bar */}
              <div className="mb-4">
                  <h4 className="font-medium mb-2">Shipment Progress</h4>
                  {/* Simple progress representation */}
                   <div className="flex items-center justify-between text-sm text-muted-foreground mb-1">
                       <span>Pickup: <span className="font-semibold capitalize">{trackingShipmentDetails.origin}</span></span>
                       {trackingShipmentDetails.status === 'in-transit' && trackingShipmentDetails.currentLocation && <span>Current: <span className="font-semibold">{trackingShipmentDetails.currentLocation}</span></span>}
                       <span>Delivery: <span className="font-semibold capitalize">{trackingShipmentDetails.destination}</span></span>
                   </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                       {/* Calculate progress width based on status */}
                      <div className="bg-primary h-2.5 rounded-full" style={{ width: trackingShipmentDetails.status === 'delivered' ? '100%' : trackingShipmentDetails.status === 'in-transit' ? '60%' : '10%' }}></div>
                  </div>
                   {/* Display current status text */}
                  <p className="text-xs text-muted-foreground mt-1 text-center">
                      Current Status: {getStatusBadge(trackingShipmentDetails.status)}
                      {trackingShipmentDetails.status === 'in-transit' && trackingShipmentDetails.currentLocation && ` at ${trackingShipmentDetails.currentLocation}`}
                  </p>
              </div>
              {/* Map Placeholder */}
              <div className="h-64 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center text-muted-foreground">
                  <MapPin size={48} className="opacity-50"/>
                  <p className="ml-2">Live Map View (Coming Soon)</p>
              </div>
              {/* Details and Driver Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <h4 className="font-medium">Key Information</h4>
                  <div className="text-sm"><strong>Commodity:</strong> {trackingShipmentDetails.commodity} ({trackingShipmentDetails.quantity})</div>
                  <div className="text-sm"><strong>Truck Type:</strong> {trackingShipmentDetails.truckType}</div>
                  <div className="text-sm"><strong>Pickup Date:</strong> {trackingShipmentDetails.pickupDate}</div>
                   {trackingShipmentDetails.pickupTime && <div className="text-sm"><strong>Pickup Time:</strong> {trackingShipmentDetails.pickupTime}</div>}
                  <div className="text-sm"><strong>Estimated Delivery:</strong> {trackingShipmentDetails.estimatedArrival}</div>
                   {/* Status-specific messages */}
                  {trackingShipmentDetails.status === 'in-transit' && ( <p className="text-sm text-green-600 font-semibold">Expect delivery by {trackingShipmentDetails.estimatedArrival}. Driver may contact you.</p> )}
                   {trackingShipmentDetails.status === 'scheduled' && trackingShipmentDetails.contactPhone && ( <p className="text-sm text-yellow-600 font-semibold">Scheduled. We will contact you or {trackingShipmentDetails.contactName || 'your contact'} on {trackingShipmentDetails.pickupDate} for pickup details.</p> )} {/* Added contact info to scheduled message */}
                </div>
                {trackingShipmentDetails.driverDetails && trackingShipmentDetails.status !== 'scheduled' && ( // Only show driver details if not scheduled
                  <div className="space-y-3">
                     <h4 className="font-medium">Driver Details</h4>
                     {trackingShipmentDetails.driverDetails.name !== "Pending Assignment" ? ( // Check if driver assigned
                         <>
                            <div className="flex items-center text-sm"><User size={16} className="mr-2 text-muted-foreground"/><span>{trackingShipmentDetails.driverDetails.name}</span></div>
                            <div className="flex items-center text-sm">
                                <PhoneCall size={16} className="mr-2 text-muted-foreground"/>
                                {/* Make phone number a callable link */}
                                <a href={`tel:${trackingShipmentDetails.driverDetails.contact}`} className="text-primary hover:underline">{trackingShipmentDetails.driverDetails.contact}</a>
                            </div>
                            {trackingShipmentDetails.driverDetails.vehicleReg && (<div className="flex items-center text-sm"><Truck size={16} className="mr-2 text-muted-foreground"/><span>Vehicle: {trackingShipmentDetails.driverDetails.vehicleReg}</span></div>)}
                         </>
                     ) : (
                         <p className="text-sm text-muted-foreground">Driver details will be available once assigned.</p>
                     )}
                  </div>
                 )}
              </div>
              {/* Tracking History */}
              {trackingShipmentDetails.trackingUpdates && trackingShipmentDetails.trackingUpdates.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Tracking History</h4>
                  <div className="space-y-3 max-h-48 overflow-y-auto border p-3 rounded-md">
                    {/* Display updates in reverse chronological order */}
                    {trackingShipmentDetails.trackingUpdates.slice().reverse().map((update, index) => (
                      <div key={index} className="text-sm pb-2 border-b last:border-b-0">
                        <p className="font-semibold">{update.statusMessage}</p>
                        <p className="text-xs text-muted-foreground">{new Date(update.timestamp).toLocaleString()} - {update.location}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              {/* Disclaimer */}
              <p className="text-xs text-muted-foreground mt-4">
                  For urgent queries, contact support. You will be contacted on the day of pickup/delivery or for schedule changes.
                  Note: Tracking updates may have slight delays.
              </p>
            </div>
          )}
          <DialogFooter className="mt-4">
            <DialogClose asChild><Button variant="outline">Close</Button></DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </div>
  );
};

export default Transportation;