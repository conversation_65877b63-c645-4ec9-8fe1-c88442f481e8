{"env": {"node": true, "es2021": true, "jest": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"indent": ["error", 2], "linebreak-style": ["error", "unix"], "quotes": ["error", "single", {"avoidEscape": true}], "semi": ["error", "always"], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "no-console": "warn"}}