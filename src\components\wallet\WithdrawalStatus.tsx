import { useState, useEffect } from "react";
import { WithdrawalResponse, checkWithdrawalStatus } from "@/services/walletService";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, RefreshCw, CheckCircle2, XCircle, Clock, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface WithdrawalStatusProps {
  withdrawal: WithdrawalResponse;
  onClose: () => void;
}

export const WithdrawalStatus = ({ withdrawal, onClose }: WithdrawalStatusProps) => {
  const [currentStatus, setCurrentStatus] = useState(withdrawal.status);
  const [message, setMessage] = useState<string | undefined>();
  const [isPolling, setIsPolling] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    let intervalId: number;

    // Only poll if the status is not final
    if (isPolling && (currentStatus === "pending" || currentStatus === "processing")) {
      intervalId = window.setInterval(async () => {
        try {
          const statusResponse = await checkWithdrawalStatus(withdrawal.id);
          setCurrentStatus(statusResponse.status);
          setMessage(statusResponse.message);

          // Stop polling if we reach a terminal state
          if (statusResponse.status === "completed" || statusResponse.status === "failed") {
            setIsPolling(false);
          }
        } catch (error) {
          console.error("Error polling withdrawal status:", error);
          setIsPolling(false);
        }
      }, 10000); // Check every 10 seconds
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [withdrawal.id, currentStatus, isPolling]);

  const handleManualRefresh = async () => {
    setIsRefreshing(true);
    try {
      const statusResponse = await checkWithdrawalStatus(withdrawal.id);
      setCurrentStatus(statusResponse.status);
      setMessage(statusResponse.message);
    } catch (error) {
      console.error("Error refreshing withdrawal status:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusIcon = () => {
    switch (currentStatus) {
      case "completed":
        return <CheckCircle2 className="h-8 w-8 text-up" />;
      case "failed":
        return <XCircle className="h-8 w-8 text-down" />;
      case "processing":
        return <Clock className="h-8 w-8 text-amber-500" />;
      case "pending":
      default:
        return <Clock className="h-8 w-8 text-muted-foreground" />;
    }
  };

  const getStatusBadge = () => {
    switch (currentStatus) {
      case "completed":
        return <Badge className="bg-up/10 border-up/30 text-up">Completed</Badge>;
      case "failed":
        return <Badge className="bg-down/10 border-down/30 text-down">Failed</Badge>;
      case "processing":
        return <Badge className="bg-amber-500/10 border-amber-500/30 text-amber-500">Processing</Badge>;
      case "pending":
      default:
        return <Badge className="bg-muted border-muted-foreground/30 text-muted-foreground">Pending</Badge>;
    }
  };

  return (
    <Card className="border-2 border-border">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl">Withdrawal Status</CardTitle>
          {getStatusBadge()}
        </div>
        <CardDescription>Reference: {withdrawal.reference}</CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex flex-col items-center justify-center py-6">
          {getStatusIcon()}
          <h3 className="text-lg font-medium mt-4">
            {currentStatus === "completed"
              ? "Withdrawal Successful"
              : currentStatus === "failed"
              ? "Withdrawal Failed"
              : currentStatus === "processing"
              ? "Processing Withdrawal"
              : "Withdrawal Pending"}
          </h3>
          <p className="text-muted-foreground text-sm mt-1">
            {currentStatus === "completed"
              ? "Your funds have been sent to your account"
              : currentStatus === "failed"
              ? "Your withdrawal could not be processed"
              : currentStatus === "processing"
              ? "Your withdrawal is being processed"
              : "Your withdrawal is waiting to be processed"}
          </p>
        </div>

        {message && (
          <Alert className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-2 gap-4 mt-2">
          <div>
            <p className="text-sm text-muted-foreground">Amount</p>
            <p className="font-medium">${withdrawal.amount.toFixed(2)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Fee</p>
            <p className="font-medium">${withdrawal.fee.toFixed(2)}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Date Initiated</p>
            <p className="font-medium">
              {new Date(withdrawal.dateInitiated).toLocaleDateString()}
            </p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Estimated Completion</p>
            <p className="font-medium">
              {new Date(withdrawal.estimatedCompletionTime).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div className="flex justify-between mt-6">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button 
            variant="secondary" 
            onClick={handleManualRefresh}
            disabled={isRefreshing || currentStatus === "completed" || currentStatus === "failed"}
          >
            {isRefreshing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh Status
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}; 