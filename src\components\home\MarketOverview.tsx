
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowUpRight, ArrowDownRight, Wheat, Coffee, Sprout } from "lucide-react";

// Updated mock data for agricultural commodities
const commodities = [
  {
    name: "Cocoa",
    symbol: "COCOA",
    price: 3526.40,
    change: 1.8,
    volume: "2.3M",
    icon: <Sprout className="h-4 w-4" />
  },
  {
    name: "Coffee (Arabica)",
    symbol: "ARABICA",
    price: 186.78,
    change: -0.5,
    volume: "3.4M",
    icon: <Coffee className="h-4 w-4" />
  },
  {
    name: "Coffee (Robusta)",
    symbol: "ROBUSTA",
    price: 156.45,
    change: 2.1,
    volume: "1.9M",
    icon: <Coffee className="h-4 w-4" />
  },
  {
    name: "Soybean",
    symbol: "SOYB",
    price: 1423.65,
    change: -1.2,
    volume: "4.5M",
    icon: <Sprout className="h-4 w-4" />
  },
  {
    name: "Sesame",
    symbol: "SESM",
    price: 1856.20,
    change: 3.4,
    volume: "980K",
    icon: <Wheat className="h-4 w-4" />
  },
  {
    name: "Sunflower",
    symbol: "SUNF",
    price: 542.35,
    change: 0.7,
    volume: "1.2M",
    icon: <Wheat className="h-4 w-4" />
  },
];

const MarketOverview = () => {
  return (
    <section className="py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold mb-3">Market Overview</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Stay updated with the latest agricultural commodity prices and market trends
        </p>
      </div>

      <Card className="border border-border bg-card">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Featured Agricultural Commodities</CardTitle>
            <Badge variant="outline" className="bg-muted">Live</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-border text-left">
                  <th className="pb-3 text-muted-foreground font-medium">Asset</th>
                  <th className="pb-3 text-muted-foreground font-medium">Price (USD)</th>
                  <th className="pb-3 text-muted-foreground font-medium">24h Change</th>
                  <th className="pb-3 text-muted-foreground font-medium">Volume</th>
                  <th className="pb-3 text-muted-foreground font-medium text-right">Trade</th>
                </tr>
              </thead>
              <tbody>
                {commodities.map((item) => (
                  <tr key={item.symbol} className="border-b border-border">
                    <td className="py-4">
                      <div className="flex items-center">
                        <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center mr-3">
                          {item.icon}
                        </div>
                        <div>
                          <div className="font-medium">{item.name}</div>
                          <div className="text-xs text-muted-foreground">{item.symbol}</div>
                        </div>
                      </div>
                    </td>
                    <td className="py-4">
                      <div className="font-medium">${item.price.toLocaleString()}</div>
                    </td>
                    <td className="py-4">
                      <div className={`flex items-center ${item.change >= 0 ? "text-up" : "text-down"}`}>
                        {item.change >= 0 ? (
                          <ArrowUpRight className="h-4 w-4 mr-1" />
                        ) : (
                          <ArrowDownRight className="h-4 w-4 mr-1" />
                        )}
                        {Math.abs(item.change)}%
                      </div>
                    </td>
                    <td className="py-4">{item.volume}</td>
                    <td className="py-4 text-right">
                      <a 
                        href={`/trade/${item.symbol}`}
                        className="text-sm font-medium text-primary hover:underline"
                      >
                        Trade
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </section>
  );
};

export default MarketOverview;
