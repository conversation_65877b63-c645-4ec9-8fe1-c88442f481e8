import { Request, Response, NextFunction } from 'express';
import { Schema } from 'joi';
import { ApiError } from './errorHandler';

/**
 * Validation middleware using Joi schemas
 */
export const validateRequest = (schema: Schema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Determine which part of the request to validate
    const dataToValidate = {
      ...(req.body && { body: req.body }),
      ...(req.query && { query: req.query }),
      ...(req.params && { params: req.params }),
    };

    // Validate with <PERSON><PERSON>
    const { error, value } = schema.validate(dataToValidate, {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true, // Remove unknown fields
    });

    if (error) {
      const errorMessage = error.details
        .map((detail) => detail.message)
        .join(', ');
      
      return next(new ApiError(400, `Validation error: ${errorMessage}`));
    }

    // Replace request data with validated data
    if (value.body) req.body = value.body;
    if (value.query) req.query = value.query;
    if (value.params) req.params = value.params;

    next();
  };
}; 