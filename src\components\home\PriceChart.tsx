import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { ArrowUpRight, ArrowDownRight, Info, RefreshCw, Clock } from "lucide-react";
import { AreaChart, Area, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from "recharts";
import { commodities, Commodity } from "@/data/commodityData";
import { 
  Tooltip as UITooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip";

// Generate random historical price data based on the current price
const generateHistoricalData = (
  commodity: Commodity, 
  period: '1h' | '1d' | '1w' | '1m' | '1y',
  dataPoints = 20
) => {
  const basePrice = commodity.currentPrice;
  let volatility: number;
  let labels: string[];
  
  // Set appropriate volatility and labels based on time period
  switch (period) {
    case '1h':
      volatility = 0.005; // 0.5% volatility for 1 hour
      labels = Array.from({ length: dataPoints }, (_, i) => 
        `${new Date().getHours() - dataPoints + i + 1}:00`
      );
      break;
    case '1d':
      volatility = 0.02; // 2% volatility for 1 day
      labels = Array.from({ length: dataPoints }, (_, i) => 
        `${Math.floor(24 / dataPoints * i)}:00`
      );
      break;
    case '1w':
      volatility = 0.05; // 5% volatility for 1 week
      const daysOfWeek = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      labels = Array.from({ length: dataPoints }, (_, i) => 
        daysOfWeek[i % 7]
      );
      break;
    case '1m':
      volatility = 0.1; // 10% volatility for 1 month
      labels = Array.from({ length: dataPoints }, (_, i) => 
        `Day ${i + 1}`
      );
      break;
    case '1y':
      volatility = 0.3; // 30% volatility for 1 year
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      labels = Array.from({ length: dataPoints }, (_, i) => 
        months[i % 12]
      );
      break;
    default:
      volatility = 0.02;
      labels = Array.from({ length: dataPoints }, (_, i) => `${i + 1}`);
  }
  
  // Generate prices with trend bias and random fluctuations
  const trendBias = Math.random() > 0.5 ? 1 : -1; // Random trend direction
  let currentPrice = basePrice * (1 - (trendBias * volatility * Math.random())); // Start point
  
  return labels.map((label, index) => {
    // Random fluctuation with trend bias
    const randomChange = (Math.random() * 2 - 1 + trendBias * 0.2) * volatility;
    currentPrice = currentPrice * (1 + randomChange);
    
    // Ensure price doesn't go negative and stays within realistic bounds
    currentPrice = Math.max(basePrice * 0.7, Math.min(basePrice * 1.3, currentPrice));
    
    return {
      time: label,
      price: Math.round(currentPrice),
      volume: Math.floor(Math.random() * 1000) + 100
    };
  });
};

// Price indicators for high, low, and average
const PriceIndicators = ({ data }: { data: any[] }) => {
  if (!data.length) return null;
  
  const prices = data.map(d => d.price);
  const high = Math.max(...prices);
  const low = Math.min(...prices);
  const avg = Math.round(prices.reduce((sum, price) => sum + price, 0) / prices.length);
  const last = prices[prices.length - 1];
  const first = prices[0];
  const change = ((last - first) / first) * 100;
  
  return (
    <div className="grid grid-cols-4 gap-4 my-4">
      <div className="flex flex-col">
        <span className="text-xs text-muted-foreground">High</span>
        <span className="font-medium">{high.toLocaleString()} UGX</span>
      </div>
      <div className="flex flex-col">
        <span className="text-xs text-muted-foreground">Low</span>
        <span className="font-medium">{low.toLocaleString()} UGX</span>
      </div>
      <div className="flex flex-col">
        <span className="text-xs text-muted-foreground">Avg</span>
        <span className="font-medium">{avg.toLocaleString()} UGX</span>
      </div>
      <div className="flex flex-col">
        <span className="text-xs text-muted-foreground">Change</span>
        <span className={`font-medium flex items-center ${change >= 0 ? 'text-[#8BC34A]' : 'text-red-500'}`}>
          {change >= 0 ? (
            <ArrowUpRight className="h-3 w-3 mr-1" />
          ) : (
            <ArrowDownRight className="h-3 w-3 mr-1" />
          )}
          {Math.abs(change).toFixed(2)}%
        </span>
      </div>
    </div>
  );
};

interface PriceChartProps {
  className?: string;
}

const PriceChart = ({ className = "" }: PriceChartProps) => {
  const [selectedCommodity, setSelectedCommodity] = useState<Commodity>(commodities[0]);
  const [timeframe, setTimeframe] = useState<'1h' | '1d' | '1w' | '1m' | '1y'>('1d');
  const [chartData, setChartData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [chartType, setChartType] = useState<'area' | 'line'>('area');
  
  // Generate chart data based on selected commodity and timeframe
  const loadChartData = () => {
    setIsLoading(true);
    
    // Simulate API fetch delay
    setTimeout(() => {
      const data = generateHistoricalData(selectedCommodity, timeframe);
      setChartData(data);
      setLastUpdated(new Date());
      setIsLoading(false);
    }, 500);
  };
  
  // Load chart data on mount and when commodity or timeframe changes
  useEffect(() => {
    loadChartData();
    
    // Set up periodic updates for 1h chart
    if (timeframe === '1h') {
      const interval = setInterval(() => {
        loadChartData();
      }, 60000); // Update every minute for 1h view
      
      return () => clearInterval(interval);
    }
  }, [selectedCommodity, timeframe]);
  
  const handleCommodityChange = (value: string) => {
    const commodity = commodities.find(c => c.id === value);
    if (commodity) {
      setSelectedCommodity(commodity);
    }
  };
  
  const handleRefresh = () => {
    loadChartData();
  };
  
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-card border border-border p-3 rounded-md shadow-md">
          <p className="text-sm font-medium">{label}</p>
          <p className="text-[#8BC34A] text-sm">
            Price: {payload[0].value.toLocaleString()} UGX
          </p>
          {payload[1] && (
            <p className="text-muted-foreground text-xs">
              Volume: {payload[1].value} kg
            </p>
          )}
        </div>
      );
    }
    return null;
  };
  
  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-0">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl flex items-center">
              Price Chart
              <TooltipProvider>
                <UITooltip>
                  <TooltipTrigger asChild>
                    <button className="ml-2 text-muted-foreground">
                      <Info className="h-4 w-4" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-[200px] text-xs">
                      Interactive chart showing price history. Data updates automatically 
                      for 1-hour view. For demonstration purposes, this uses simulated data.
                    </p>
                  </TooltipContent>
                </UITooltip>
              </TooltipProvider>
            </CardTitle>
            <CardDescription className="flex items-center mt-1">
              <Clock className="h-3 w-3 mr-1" />
              Last updated {lastUpdated.toLocaleTimeString()}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedCommodity.id} onValueChange={handleCommodityChange}>
              <SelectTrigger className="w-[160px]">
                <SelectValue placeholder="Select commodity" />
              </SelectTrigger>
              <SelectContent>
                {commodities.map(commodity => (
                  <SelectItem key={commodity.id} value={commodity.id}>
                    {commodity.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button 
              variant="outline" 
              size="icon" 
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mt-2">
          <div className="flex justify-between items-center mb-4">
            <Tabs value={timeframe} onValueChange={(value: string) => setTimeframe(value as any)}>
              <TabsList>
                <TabsTrigger value="1h">1H</TabsTrigger>
                <TabsTrigger value="1d">1D</TabsTrigger>
                <TabsTrigger value="1w">1W</TabsTrigger>
                <TabsTrigger value="1m">1M</TabsTrigger>
                <TabsTrigger value="1y">1Y</TabsTrigger>
              </TabsList>
            </Tabs>
            <div>
              <Tabs value={chartType} onValueChange={(value: string) => setChartType(value as any)}>
                <TabsList>
                  <TabsTrigger value="area">Area</TabsTrigger>
                  <TabsTrigger value="line">Line</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>
          
          <PriceIndicators data={chartData} />
          
          <div className="h-[300px] mt-4">
            {isLoading ? (
              <div className="h-full flex items-center justify-center">
                <RefreshCw className="h-8 w-8 text-muted-foreground animate-spin" />
              </div>
            ) : (
              <ResponsiveContainer width="100%" height="100%">
                {chartType === 'area' ? (
                  <AreaChart
                    data={chartData}
                    margin={{ top: 10, right: 10, left: 0, bottom: 5 }}
                  >
                    <defs>
                      <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#8BC34A" stopOpacity={0.8} />
                        <stop offset="95%" stopColor="#8BC34A" stopOpacity={0.1} />
                      </linearGradient>
                      <linearGradient id="colorVolume" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#AED581" stopOpacity={0.8} />
                        <stop offset="95%" stopColor="#AED581" stopOpacity={0.1} />
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="var(--border)" />
                    <XAxis dataKey="time" stroke="var(--muted-foreground)" />
                    <YAxis 
                      yAxisId="left"
                      tickFormatter={(value) => `${value.toLocaleString()}`}
                      stroke="var(--muted-foreground)" 
                    />
                    <YAxis 
                      yAxisId="right" 
                      orientation="right" 
                      stroke="var(--muted-foreground)"
                      tickFormatter={(value) => `${value}kg`}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="price"
                      stroke="#8BC34A"
                      fillOpacity={1}
                      fill="url(#colorPrice)"
                      name="Price (UGX)"
                    />
                    <Area
                      yAxisId="right"
                      type="monotone"
                      dataKey="volume"
                      stroke="#AED581"
                      fillOpacity={1}
                      fill="url(#colorVolume)"
                      name="Volume (kg)"
                    />
                  </AreaChart>
                ) : (
                  <LineChart
                    data={chartData}
                    margin={{ top: 10, right: 10, left: 0, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="var(--border)" />
                    <XAxis dataKey="time" stroke="var(--muted-foreground)" />
                    <YAxis 
                      yAxisId="left"
                      tickFormatter={(value) => `${value.toLocaleString()}`}
                      stroke="var(--muted-foreground)" 
                    />
                    <YAxis 
                      yAxisId="right" 
                      orientation="right" 
                      stroke="var(--muted-foreground)"
                      tickFormatter={(value) => `${value}kg`}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="price"
                      stroke="#8BC34A"
                      name="Price (UGX)"
                      dot={{ r: 2 }}
                      activeDot={{ r: 5 }}
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="volume"
                      stroke="#AED581"
                      name="Volume (kg)"
                      dot={{ r: 2 }}
                    />
                  </LineChart>
                )}
              </ResponsiveContainer>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PriceChart; 