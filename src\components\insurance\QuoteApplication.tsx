
import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog";
import { FileDown, Save, Share, QrCode } from 'lucide-react';
import { generateQuoteId, generateQuotePDF, saveQuote, InsuranceQuote } from "@/utils/pdfGenerator";
import { useToast } from "@/hooks/use-toast";
import { Textarea } from "@/components/ui/textarea";

interface QuoteApplicationProps {
  insuranceType: string;
  coverageLevel: string;
  premium: number;
  details: {
    cropType?: string;
    acreage?: number;
    livestockType?: string;
    numberOfAnimals?: number;
    equipmentType?: string;
    equipmentCount?: number;
  };
}

const QuoteApplication = ({
  insuranceType,
  coverageLevel,
  premium,
  details
}: QuoteApplicationProps) => {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [generatedQuote, setGeneratedQuote] = useState<InsuranceQuote | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    comments: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Generate a unique quote ID
      const quoteId = generateQuoteId();
      
      // Create current date and validity date (30 days from now)
      const currentDate = new Date();
      const validUntil = new Date();
      validUntil.setDate(currentDate.getDate() + 30);
      
      // Format dates
      const formattedCurrent = currentDate.toLocaleDateString('en-US', {
        year: 'numeric', 
        month: 'long', 
        day: 'numeric'
      });
      const formattedValidity = validUntil.toLocaleDateString('en-US', {
        year: 'numeric', 
        month: 'long', 
        day: 'numeric'
      });
      
      // Create coverage items based on insurance type
      let coverageItems = [];
      if (insuranceType === "Crop Insurance") {
        coverageItems = [{
          name: details.cropType || "Coffee",
          quantity: details.acreage || 1,
          description: `${details.acreage || 1} acres of ${details.cropType || "Coffee"}`
        }];
      } else if (insuranceType === "Livestock Insurance") {
        coverageItems = [{
          name: details.livestockType || "Cattle",
          quantity: details.numberOfAnimals || 1,
          description: `${details.numberOfAnimals || 1} ${details.livestockType || "Cattle"}`
        }];
      } else {
        coverageItems = [{
          name: details.equipmentType || "Transport Vehicle",
          quantity: details.equipmentCount || 1,
          description: `${details.equipmentCount || 1} ${details.equipmentType || "Transport Vehicle"}`
        }];
      }
      
      // Create quote object
      const quoteData: InsuranceQuote = {
        quoteId,
        customerName: formData.name,
        customerEmail: formData.email,
        customerPhone: formData.phone,
        insuranceType,
        coverageDetails: {
          type: insuranceType,
          level: coverageLevel,
          coverageItems
        },
        premium,
        timestamp: formattedCurrent,
        validUntil: formattedValidity
      };
      
      // Save quote to localStorage
      saveQuote(quoteData);
      
      // Store the quote data for download/share
      setGeneratedQuote(quoteData);
      
      // Open the confirmation dialog
      setIsDialogOpen(true);
    } catch (error) {
      console.error("Error generating quote:", error);
      toast({
        title: "Error",
        description: "There was a problem generating your quote. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDownloadPDF = async () => {
    if (!generatedQuote) return;
    
    try {
      const pdfBlob = await generateQuotePDF(generatedQuote);
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Insurance_Quote_${generatedQuote.quoteId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: "Success",
        description: "Quote PDF has been downloaded successfully.",
      });
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast({
        title: "Error",
        description: "Failed to download the PDF. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleShareQuote = () => {
    if (!generatedQuote) return;
    
    // Create share data
    const shareData = {
      title: 'Insurance Quote from Fotis Agro Trading',
      text: `My insurance quote (${generatedQuote.quoteId}) is valid until ${generatedQuote.validUntil}.`,
      url: window.location.href
    };
    
    if (navigator.share) {
      navigator.share(shareData)
        .then(() => {
          toast({
            title: "Quote Shared",
            description: "Quote has been shared successfully."
          });
        })
        .catch((err) => {
          console.error("Share error:", err);
          toast({
            title: "Sharing Failed",
            description: "There was an issue sharing your quote.",
            variant: "destructive"
          });
        });
    } else {
      // Fallback for browsers that don't support sharing
      navigator.clipboard.writeText(
        `Insurance Quote from Fotis Agro Trading\n` +
        `Quote ID: ${generatedQuote.quoteId}\n` +
        `Valid Until: ${generatedQuote.validUntil}\n` +
        `Visit our website to retrieve your quote.`
      );
      toast({
        title: "Quote Details Copied",
        description: "Quote details have been copied to your clipboard."
      });
    }
  };

  return (
    <>
      <div className="bg-card border rounded-md p-6">
        <h3 className="text-lg font-semibold mb-4">Apply for Coverage</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input 
                id="name" 
                name="name" 
                placeholder="Enter your full name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input 
                id="email" 
                name="email" 
                type="email" 
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input 
                id="phone" 
                name="phone" 
                placeholder="+256 700 000000"
                value={formData.phone}
                onChange={handleInputChange}
                required
              />
            </div>
            <div>
              <Label htmlFor="address">Address</Label>
              <Input 
                id="address" 
                name="address" 
                placeholder="Enter your address"
                value={formData.address}
                onChange={handleInputChange}
                required
              />
            </div>
          </div>
          
          <div>
            <Label htmlFor="comments">Additional Information (optional)</Label>
            <Textarea 
              id="comments" 
              name="comments"
              placeholder="Please provide any additional information that might be relevant to your insurance application."
              rows={3}
              value={formData.comments}
              onChange={handleInputChange}
            />
          </div>
          
          <div className="pt-4">
            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? "Processing..." : "Apply for Coverage"}
            </Button>
          </div>
        </form>
      </div>
      
      {/* Confirmation Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Quote Generated Successfully</DialogTitle>
            <DialogDescription>
              Your insurance quote has been generated and is valid for 30 days.
              Quote Reference: <span className="font-medium">{generatedQuote?.quoteId}</span>
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="bg-muted p-4 rounded-md">
              <p className="text-sm font-medium">Quote Summary:</p>
              <ul className="text-sm mt-2 space-y-1">
                <li><span className="text-muted-foreground">Insurance Type:</span> {generatedQuote?.insuranceType}</li>
                <li><span className="text-muted-foreground">Coverage Level:</span> {generatedQuote?.coverageDetails.level}</li>
                <li><span className="text-muted-foreground">Annual Premium:</span> {generatedQuote?.premium?.toLocaleString()} UGX</li>
                <li><span className="text-muted-foreground">Valid Until:</span> {generatedQuote?.validUntil}</li>
              </ul>
            </div>
          </div>
          
          <DialogFooter className="sm:justify-between flex-col sm:flex-row gap-2">
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                className="flex-1" 
                onClick={handleDownloadPDF}
              >
                <FileDown className="mr-2 h-4 w-4" />
                Download PDF
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="flex-1"
                onClick={handleShareQuote}
              >
                <Share className="mr-2 h-4 w-4" />
                Share Quote
              </Button>
            </div>
            <Button onClick={() => setIsDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default QuoteApplication;
