
import React, { useState } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { services } from "@/data/servicesData";

const ServicePreferences = () => {
  const [enabledServices, setEnabledServices] = useState(
    services.reduce((acc, service) => {
      acc[service.id] = true;
      return acc;
    }, {} as Record<string, boolean>)
  );

  const [notifications, setNotifications] = useState({
    warehousing: true,
    financing: true,
    insurance: true,
    leasing: true,
    futures: true,
  });

  const handleServiceToggle = (serviceId: string) => {
    setEnabledServices(prev => ({
      ...prev,
      [serviceId]: !prev[serviceId]
    }));
  };

  const handleNotificationToggle = (category: string) => {
    setNotifications(prev => ({
      ...prev,
      [category]: !prev[category]
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl">Service Preferences</CardTitle>
        <CardDescription>
          Manage which services are visible on your dashboard and control related notifications
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h3 className="text-lg font-medium mb-2">Enabled Services</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Control which services appear in your dashboard and navigation
          </p>
          
          <div className="space-y-4">
            {services.map(service => (
              <div key={service.id} className="flex items-center justify-between">
                <div>
                  <Label htmlFor={`service-${service.id}`} className="font-medium">
                    {service.title}
                  </Label>
                  <p className="text-sm text-muted-foreground">{service.description}</p>
                </div>
                <Switch
                  id={`service-${service.id}`}
                  checked={enabledServices[service.id]}
                  onCheckedChange={() => handleServiceToggle(service.id)}
                />
              </div>
            ))}
          </div>
        </div>

        <div>
          <h3 className="text-lg font-medium mb-2">Service Notifications</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Control notifications for different service categories
          </p>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="notif-warehousing" className="font-medium">
                  Warehousing Notifications
                </Label>
                <p className="text-sm text-muted-foreground">Inventory updates, space availability alerts</p>
              </div>
              <Switch
                id="notif-warehousing"
                checked={notifications.warehousing}
                onCheckedChange={() => handleNotificationToggle('warehousing')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="notif-financing" className="font-medium">
                  Financing Notifications
                </Label>
                <p className="text-sm text-muted-foreground">Loan updates, payment reminders, new offers</p>
              </div>
              <Switch
                id="notif-financing"
                checked={notifications.financing}
                onCheckedChange={() => handleNotificationToggle('financing')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="notif-insurance" className="font-medium">
                  Insurance Notifications
                </Label>
                <p className="text-sm text-muted-foreground">Policy updates, claim status, renewal reminders</p>
              </div>
              <Switch
                id="notif-insurance"
                checked={notifications.insurance}
                onCheckedChange={() => handleNotificationToggle('insurance')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="notif-leasing" className="font-medium">
                  Leasing Notifications
                </Label>
                <p className="text-sm text-muted-foreground">New land listings, lease deadlines, payment reminders</p>
              </div>
              <Switch
                id="notif-leasing"
                checked={notifications.leasing}
                onCheckedChange={() => handleNotificationToggle('leasing')}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="notif-futures" className="font-medium">
                  Futures Trading Notifications
                </Label>
                <p className="text-sm text-muted-foreground">Price alerts, position updates, market reports</p>
              </div>
              <Switch
                id="notif-futures"
                checked={notifications.futures}
                onCheckedChange={() => handleNotificationToggle('futures')}
              />
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline">Reset to Defaults</Button>
        <Button>Save Preferences</Button>
      </CardFooter>
    </Card>
  );
};

export default ServicePreferences;
