import Joi from 'joi';

/**
 * Validation schemas for wallet routes
 */
export class WalletValidation {
  /**
   * Validation schema for deposit funds
   */
  static deposit = Joi.object({
    body: Joi.object({
      amount: Joi.number().positive().precision(2).required()
        .messages({
          'number.base': 'Amount must be a number',
          'number.positive': 'Amount must be positive',
          'number.precision': 'Amount cannot have more than 2 decimal places',
          'any.required': 'Amount is required',
        }),
      paymentMethod: Joi.string().valid('credit_card', 'mobile_money', 'bank_transfer').required()
        .messages({
          'any.only': 'Payment method must be one of: credit_card, mobile_money, bank_transfer',
          'any.required': 'Payment method is required',
        }),
      reference: Joi.string().allow('').optional(),
      
      // Mobile Money specific fields
      phoneNumber: Joi.string().when('paymentMethod', {
        is: 'mobile_money',
        then: Joi.string().pattern(/^[0-9+]{8,15}$/).required()
          .messages({
            'string.pattern.base': 'Please provide a valid phone number',
            'any.required': 'Phone number is required for mobile money payments',
          }),
        otherwise: Joi.string().optional(),
      }),
      provider: Joi.string().when('paymentMethod', {
        is: 'mobile_money',
        then: Joi.string().valid('mtn', 'airtel').required()
          .messages({
            'any.only': 'Provider must be one of: mtn, airtel',
            'any.required': 'Provider is required for mobile money payments',
          }),
        otherwise: Joi.string().optional(),
      }),
      
      // Bank Transfer specific fields
      accountNumber: Joi.string().when('paymentMethod', {
        is: 'bank_transfer',
        then: Joi.string().required()
          .messages({
            'any.required': 'Account number is required for bank transfers',
          }),
        otherwise: Joi.string().optional(),
      }),
      bankName: Joi.string().when('paymentMethod', {
        is: 'bank_transfer',
        then: Joi.string().required()
          .messages({
            'any.required': 'Bank name is required for bank transfers',
          }),
        otherwise: Joi.string().optional(),
      }),
    }).required(),
  });

  /**
   * Validation schema for withdraw funds
   */
  static withdraw = Joi.object({
    body: Joi.object({
      amount: Joi.number().positive().precision(2).required()
        .messages({
          'number.base': 'Amount must be a number',
          'number.positive': 'Amount must be positive',
          'number.precision': 'Amount cannot have more than 2 decimal places',
          'any.required': 'Amount is required',
        }),
      paymentMethod: Joi.string().valid('mobile_money', 'bank_transfer').required()
        .messages({
          'any.only': 'Payment method must be one of: mobile_money, bank_transfer',
          'any.required': 'Payment method is required',
        }),
      reference: Joi.string().allow('').optional(),
      
      // Mobile Money specific fields
      phoneNumber: Joi.string().when('paymentMethod', {
        is: 'mobile_money',
        then: Joi.string().pattern(/^[0-9+]{8,15}$/).required()
          .messages({
            'string.pattern.base': 'Please provide a valid phone number',
            'any.required': 'Phone number is required for mobile money withdrawals',
          }),
        otherwise: Joi.string().optional(),
      }),
      provider: Joi.string().when('paymentMethod', {
        is: 'mobile_money',
        then: Joi.string().valid('mtn', 'airtel').required()
          .messages({
            'any.only': 'Provider must be one of: mtn, airtel',
            'any.required': 'Provider is required for mobile money withdrawals',
          }),
        otherwise: Joi.string().optional(),
      }),
      
      // Bank Transfer specific fields
      accountNumber: Joi.string().when('paymentMethod', {
        is: 'bank_transfer',
        then: Joi.string().required()
          .messages({
            'any.required': 'Account number is required for bank transfers',
          }),
        otherwise: Joi.string().optional(),
      }),
      bankName: Joi.string().when('paymentMethod', {
        is: 'bank_transfer',
        then: Joi.string().required()
          .messages({
            'any.required': 'Bank name is required for bank transfers',
          }),
        otherwise: Joi.string().optional(),
      }),
      accountName: Joi.string().when('paymentMethod', {
        is: 'bank_transfer',
        then: Joi.string().required()
          .messages({
            'any.required': 'Account name is required for bank transfers',
          }),
        otherwise: Joi.string().optional(),
      }),
    }).required(),
  });

  /**
   * Validation schema for withdrawal validation
   */
  static withdrawalValidation = Joi.object({
    body: Joi.object({
      amount: Joi.number().positive().precision(2).required()
        .messages({
          'number.base': 'Amount must be a number',
          'number.positive': 'Amount must be positive',
          'number.precision': 'Amount cannot have more than 2 decimal places',
          'any.required': 'Amount is required',
        }),
      paymentMethod: Joi.string().valid('mobile_money', 'bank_transfer').required()
        .messages({
          'any.only': 'Payment method must be one of: mobile_money, bank_transfer',
          'any.required': 'Payment method is required',
        }),
      // Note: For validation, some recipient details might be optional or required based on backend logic.
      // Copying from withdrawal schema for now, adjust if needed based on actual backend requirements.
      phoneNumber: Joi.string().when('paymentMethod', {
        is: 'mobile_money',
        then: Joi.string().pattern(/^[0-9+]{8,15}$/).optional() // Made optional for initial validation
          .messages({
            'string.pattern.base': 'Please provide a valid phone number',
          }),
        otherwise: Joi.string().optional(),
      }),
      provider: Joi.string().when('paymentMethod', {
        is: 'mobile_money',
        then: Joi.string().valid('mtn', 'airtel').optional() // Made optional for initial validation
          .messages({
            'any.only': 'Provider must be one of: mtn, airtel',
          }),
        otherwise: Joi.string().optional(),
      }),
      accountNumber: Joi.string().when('paymentMethod', {
        is: 'bank_transfer',
        then: Joi.string().optional() // Made optional for initial validation
          .messages({
            'any.required': 'Account number is required for bank transfers',
          }),
        otherwise: Joi.string().optional(),
      }),
      bankName: Joi.string().when('paymentMethod', {
        is: 'bank_transfer',
        then: Joi.string().optional() // Made optional for initial validation
          .messages({
            'any.required': 'Bank name is required for bank transfers',
          }),
        otherwise: Joi.string().optional(),
      }),
      accountName: Joi.string().when('paymentMethod', {
        is: 'bank_transfer',
        then: Joi.string().optional() // Made optional for initial validation
          .messages({
            'any.required': 'Account name is required for bank transfers',
          }),
        otherwise: Joi.string().optional(),
      }),
    }).required(),
  });
} 