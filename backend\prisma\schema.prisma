// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model
model User {
  id                String             @id @default(uuid())
  email             String             @unique
  firstName         String
  lastName          String
  passwordHash      String
  role              Role               @default(USER)
  phoneNumber       String?
  country           String?
  city              String?
  address           String?
  isEmailVerified   Boolean            @default(false)
  isPhoneVerified   Boolean            @default(false)
  twoFactorEnabled  Boolean            @default(false)
  twoFactorSecret   String?
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  insurancePolicies InsurancePolicy[]
  loanApplications  LoanApplication[]
  otpCodes          OtpCode[]
  tokens            Token[]
  transportBookings TransportBooking[]
  userAssets        UserAsset[]
  wallet            Wallet?
  warehouseBookings WarehouseBooking[]

  @@index([email])
  @@map("users")
}

// Role enum
enum Role {
  USER
  FARMER
  TRADER
  TRANSPORTER
  WAREHOUSE
  ADMIN
  SUPER_ADMIN
}

// Token model for refresh tokens
model Token {
  id        String    @id @default(uuid())
  token     String    @unique
  type      TokenType
  expiresAt DateTime
  userId    String
  createdAt DateTime  @default(now())
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([token])
  @@map("tokens")
}

model OtpCode {
  id        String   @id @default(uuid())
  code      String
  type      OtpType
  expiresAt DateTime
  used      Boolean  @default(false)
  userId    String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("otp_codes")
}

// Wallet model
model Wallet {
  id           String        @id @default(uuid())
  balance      Float         @default(0)
  userId       String        @unique
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  transactions Transaction[]
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@map("wallets")
}

// UserAsset model
model UserAsset {
  id            String    @id @default(uuid())
  quantity      Float
  purchasePrice Float
  commodityId   String
  userId        String
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  commodity     Commodity @relation(fields: [commodityId], references: [id])
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([commodityId])
  @@map("user_assets")
}

model Commodity {
  id           String      @id @default(uuid())
  name         String
  symbol       String
  description  String?
  currentPrice Float
  priceHistory Json?
  category     String
  unit         String      @default("kg")
  imageUrl     String?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  userAssets   UserAsset[]

  @@index([symbol])
  @@map("commodities")
}

// Transaction model
model Transaction {
  id                String            @id @default(uuid())
  amount            Float
  fee               Float             @default(0)
  type              TransactionType
  status            TransactionStatus
  reference         String?
  description       String?
  walletId          String
  paymentMethod     String?
  paymentDetails    Json?
  externalReference String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  wallet            Wallet            @relation(fields: [walletId], references: [id], onDelete: Cascade)

  @@index([walletId])
  @@index([type])
  @@index([status])
  @@index([createdAt])
  @@map("transactions")
}

// LoanApplication model
model LoanApplication {
  id           String     @id @default(uuid())
  amount       Float
  purpose      String
  tenure       Int
  interestRate Float
  status       LoanStatus @default(PENDING)
  userId       String
  approvedBy   String?
  approvedAt   DateTime?
  disbursedAt  DateTime?
  documents    Json?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  user         User       @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([status])
  @@map("loan_applications")
}

// TransportBooking model
model TransportBooking {
  id                  String           @id @default(uuid())
  origin              String
  destination         String
  commodity           String
  weight              Float
  volume              Float?
  truckType           String
  pickupDate          DateTime
  pickupTime          String?
  specialRequirements String?
  contactName         String?
  contactPhone        String?
  estimatedArrival    DateTime?
  price               Float
  status              TransportStatus  @default(SCHEDULED)
  transactionId       String?
  userId              String
  driverId            String?
  createdAt           DateTime         @default(now())
  updatedAt           DateTime         @updatedAt
  trackingUpdates     TrackingUpdate[]
  driver              Driver?          @relation(fields: [driverId], references: [id])
  user                User             @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([status])
  @@index([pickupDate])
  @@map("transport_bookings")
}

model Driver {
  id                String             @id @default(uuid())
  name              String
  phoneNumber       String
  vehicleReg        String?
  licenseNumber     String?
  isAvailable       Boolean            @default(true)
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  transportBookings TransportBooking[]

  @@index([isAvailable])
  @@map("drivers")
}

model TrackingUpdate {
  id                 String           @id @default(uuid())
  location           String
  statusMessage      String
  transportBookingId String
  createdAt          DateTime         @default(now())
  transportBooking   TransportBooking @relation(fields: [transportBookingId], references: [id], onDelete: Cascade)

  @@index([transportBookingId])
  @@index([createdAt])
  @@map("tracking_updates")
}

model WarehouseBooking {
  id            String        @id @default(uuid())
  warehouseId   String
  userId        String
  commodity     String
  quantity      Float
  startDate     DateTime
  endDate       DateTime
  status        BookingStatus @default(PENDING)
  price         Float
  transactionId String?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt
  user          User          @relation(fields: [userId], references: [id])
  warehouse     Warehouse     @relation(fields: [warehouseId], references: [id])

  @@index([userId])
  @@index([warehouseId])
  @@index([status])
  @@map("warehouse_bookings")
}

model Warehouse {
  id              String             @id @default(uuid())
  name            String
  location        String
  capacity        Float
  availableSpace  Float
  pricePerUnitDay Float
  createdAt       DateTime           @default(now())
  updatedAt       DateTime           @updatedAt
  bookings        WarehouseBooking[]

  @@index([location])
  @@map("warehouses")
}

model InsurancePolicy {
  id             String       @id @default(uuid())
  policyNumber   String       @unique
  type           PolicyType
  coverageAmount Float
  premium        Float
  startDate      DateTime
  endDate        DateTime
  status         PolicyStatus @default(ACTIVE)
  userId         String
  details        Json?
  transactionId  String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  user           User         @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([status])
  @@index([policyNumber])
  @@map("insurance_policies")
}

enum TokenType {
  REFRESH
  PASSWORD_RESET
  EMAIL_VERIFICATION
}

enum OtpType {
  PHONE_VERIFICATION
  EMAIL_VERIFICATION
  TWO_FACTOR
}

enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  TRANSFER
  PAYMENT
  REFUND
}

enum TransactionStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum TransportStatus {
  SCHEDULED
  IN_TRANSIT
  DELIVERED
  CANCELLED
}

enum PolicyType {
  CROP
  LIVESTOCK
  WAREHOUSE
  TRANSPORT
}

enum PolicyStatus {
  ACTIVE
  EXPIRED
  CANCELLED
  CLAIMED
}

enum LoanStatus {
  PENDING
  APPROVED
  DISBURSED
  DECLINED
  REPAID
  DEFAULTED
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
}
