
import React, { useState } from 'react';
import { Card, Card<PERSON><PERSON>er, CardContent, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

// Mock insurance products
const insuranceProducts = [
  { id: "ins-01", name: "Crop Insurance", type: "crop", coverageOptions: ["Basic", "Standard", "Premium"], yearlyRatePerAcre: [50000, 80000, 120000] },
  { id: "ins-02", name: "Livestock Insurance", type: "livestock", coverageOptions: ["Basic", "Standard", "Premium"], yearlyRatePerHead: [20000, 35000, 60000] },
  { id: "ins-03", name: "Equipment Insurance", type: "equipment", coverageOptions: ["Basic", "Comprehensive"], yearlyRatePercent: [3, 5] },
];

interface QuoteCalculatorProps {
  selectedProduct: string;
  onProductChange: (productId: string) => void;
  onShowDetails: (productId: string) => void;
  onShowApplication: () => void;
}

const QuoteCalculator = ({ 
  selectedProduct, 
  onProductChange,
  onShowDetails,
  onShowApplication
}: QuoteCalculatorProps) => {
  const [coverageOption, setCoverageOption] = useState(0);
  const [quantity, setQuantity] = useState(1);

  const product = insuranceProducts.find(p => p.id === selectedProduct) || insuranceProducts[0];
  
  const calculatePremium = () => {
    if (product.type === "crop") {
      return product.yearlyRatePerAcre[coverageOption] * quantity;
    } else if (product.type === "livestock") {
      return product.yearlyRatePerHead[coverageOption] * quantity;
    } else {
      // equipment
      return product.yearlyRatePercent[coverageOption] * (5000000 * quantity) / 100;
    }
  };

  return (
    <>
      <CardHeader>
        <CardTitle>Get a Quote</CardTitle>
        <CardDescription>Calculate insurance premiums based on your needs</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="insurance-type">Insurance Product</Label>
              <Select value={selectedProduct} onValueChange={onProductChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select insurance product" />
                </SelectTrigger>
                <SelectContent>
                  {insuranceProducts.map(product => (
                    <SelectItem key={product.id} value={product.id}>{product.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {product.type === "crop" && (
              <>
                <div>
                  <Label>Crop Type</Label>
                  <Select defaultValue="arabica">
                    <SelectTrigger>
                      <SelectValue placeholder="Select crop type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="arabica">Coffee (Arabica)</SelectItem>
                      <SelectItem value="robusta">Coffee (Robusta)</SelectItem>
                      <SelectItem value="cocoa">Cocoa</SelectItem>
                      <SelectItem value="maize">Maize</SelectItem>
                      <SelectItem value="beans">Beans</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="acreage">Acreage</Label>
                  <Input 
                    id="acreage" 
                    type="number" 
                    value={quantity} 
                    onChange={(e) => setQuantity(Number(e.target.value))}
                    min={1}
                  />
                </div>
              </>
            )}
            
            {product.type === "livestock" && (
              <>
                <div>
                  <Label>Livestock Type</Label>
                  <Select defaultValue="cattle">
                    <SelectTrigger>
                      <SelectValue placeholder="Select livestock type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="cattle">Cattle</SelectItem>
                      <SelectItem value="poultry">Poultry</SelectItem>
                      <SelectItem value="goats">Goats</SelectItem>
                      <SelectItem value="sheep">Sheep</SelectItem>
                      <SelectItem value="pigs">Pigs</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="count">Number of Animals</Label>
                  <Input 
                    id="count" 
                    type="number" 
                    value={quantity} 
                    onChange={(e) => setQuantity(Number(e.target.value))}
                    min={1}
                  />
                </div>
              </>
            )}
            
            {product.type === "equipment" && (
              <>
                <div>
                  <Label>Equipment Type</Label>
                  <Select defaultValue="tractor">
                    <SelectTrigger>
                      <SelectValue placeholder="Select equipment type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tractor">Tractor</SelectItem>
                      <SelectItem value="harvester">Harvester</SelectItem>
                      <SelectItem value="irrigation">Irrigation System</SelectItem>
                      <SelectItem value="processingMachinery">Processing Machinery</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="equipment-count">Number of Items</Label>
                  <Input 
                    id="equipment-count" 
                    type="number" 
                    value={quantity} 
                    onChange={(e) => setQuantity(Number(e.target.value))}
                    min={1}
                  />
                </div>
              </>
            )}

            <div className="space-y-2">
              <Label>Coverage Options</Label>
              <RadioGroup defaultValue="0" onValueChange={(value) => setCoverageOption(parseInt(value))}>
                {product.coverageOptions.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2 border rounded-md p-3">
                    <RadioGroupItem value={index.toString()} id={`coverage-${index}`} />
                    <Label htmlFor={`coverage-${index}`} className="flex-1">
                      <div className="font-medium">{option}</div>
                      {product.type === "crop" && (
                        <div className="text-sm text-muted-foreground">
                          {index === 0 ? "Covers drought and flood damage" : 
                           index === 1 ? "Covers drought, flood, and pest damage" : 
                           "Comprehensive coverage including market price protection"}
                        </div>
                      )}
                      {product.type === "livestock" && (
                        <div className="text-sm text-muted-foreground">
                          {index === 0 ? "Covers death due to disease and accidents" : 
                           index === 1 ? "Covers death and veterinary expenses" : 
                           "Comprehensive coverage including loss of production"}
                        </div>
                      )}
                      {product.type === "equipment" && (
                        <div className="text-sm text-muted-foreground">
                          {index === 0 ? "Covers damage due to accidents only" : 
                           "Covers damage, theft, and breakdown"}
                        </div>
                      )}
                    </Label>
                    <div className="text-sm font-medium">
                      {product.type === "crop" && `${product.yearlyRatePerAcre[index].toLocaleString()} UGX/acre`}
                      {product.type === "livestock" && `${product.yearlyRatePerHead[index].toLocaleString()} UGX/animal`}
                      {product.type === "equipment" && `${product.yearlyRatePercent[index]}% of value`}
                    </div>
                  </div>
                ))}
              </RadioGroup>
            </div>
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => onShowDetails(selectedProduct)}
            >
              View Detailed Coverage Information
            </Button>
          </div>

          <div className="bg-muted p-6 rounded-lg space-y-6">
            <h3 className="text-lg font-medium">Insurance Quote Summary</h3>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Insurance Type:</span>
                <span className="font-medium">{product.name}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-muted-foreground">Coverage Level:</span>
                <span className="font-medium">{product.coverageOptions[coverageOption]}</span>
              </div>
              
              {product.type === "crop" && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Acreage:</span>
                  <span className="font-medium">{quantity} acres</span>
                </div>
              )}
              
              {product.type === "livestock" && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Number of Animals:</span>
                  <span className="font-medium">{quantity}</span>
                </div>
              )}
              
              {product.type === "equipment" && (
                <>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Number of Items:</span>
                    <span className="font-medium">{quantity}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Estimated Value:</span>
                    <span className="font-medium">{(5000000 * quantity).toLocaleString()} UGX</span>
                  </div>
                </>
              )}
              
              <div className="pt-3 border-t flex justify-between">
                <span className="font-semibold">Annual Premium:</span>
                <span className="font-semibold">{calculatePremium().toLocaleString()} UGX</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Monthly Payment (optional):</span>
                <span>{Math.round(calculatePremium() / 12).toLocaleString()} UGX</span>
              </div>
            </div>
            
            <div className="space-y-3">
              <Button 
                className="w-full"
                onClick={onShowApplication}
              >
                Apply for Coverage
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </>
  );
};

export { insuranceProducts };
export default QuoteCalculator;
