import express from 'express';
import { AuthController } from '../../controllers/auth.controller';
import { validateRequest } from '../../middlewares/validateRequest';
import { AuthValidation } from '../../validators/auth.validator';

const router = express.Router();
const authController = new AuthController();

/**
 * @route POST /api/auth/register
 * @desc Register a new user
 * @access Public
 */
router.post(
  '/register',
  validateRequest(AuthValidation.register),
  authController.register
);

/**
 * @route POST /api/auth/login
 * @desc Login user and return JWT tokens
 * @access Public
 */
router.post(
  '/login',
  validateRequest(AuthValidation.login),
  authController.login
);

/**
 * @route POST /api/auth/refresh-token
 * @desc Refresh access token
 * @access Public
 */
router.post(
  '/refresh-token',
  validateRequest(AuthValidation.refreshToken),
  authController.refreshToken
);

/**
 * @route POST /api/auth/logout
 * @desc Logout user and invalidate refresh token
 * @access Public
 */
router.post(
  '/logout',
  validateRequest(AuthValidation.logout),
  authController.logout
);

/**
 * @route POST /api/auth/forgot-password
 * @desc Send password reset email
 * @access Public
 */
router.post(
  '/forgot-password',
  validateRequest(AuthValidation.forgotPassword),
  (req, res, next) => {
    authController.forgotPassword(req, res, next).catch(next);
  }
);

/**
 * @route POST /api/auth/reset-password
 * @desc Reset password with token
 * @access Public
 */
router.post(
  '/reset-password',
  validateRequest(AuthValidation.resetPassword),
  authController.resetPassword
);

/**
 * @route POST /api/auth/verify-email
 * @desc Verify user email with token
 * @access Public
 */
router.post(
  '/verify-email',
  validateRequest(AuthValidation.verifyEmail),
  authController.verifyEmail
);

export default router; 