
import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Mail, MessageSquare } from "lucide-react";

interface ContactSupportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ContactSupportModal = ({ isOpen, onClose }: ContactSupportModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Contact Support</DialogTitle>
          <DialogDescription>
            Our logistics experts are ready to assist you
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="flex items-center gap-4">
            <div className="rounded-full bg-green-100 p-3">
              <MessageSquare className="h-6 w-6 text-green-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium">WhatsApp</h3>
              <p className="text-sm text-muted-foreground">
                Quick response during business hours
              </p>
              <a 
                href="https://wa.me/256757220113" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="mt-1 inline-flex items-center text-sm font-medium text-primary hover:underline"
              >
                +256 757 220 113
              </a>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            <div className="rounded-full bg-blue-100 p-3">
              <Mail className="h-6 w-6 text-blue-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium">Email</h3>
              <p className="text-sm text-muted-foreground">
                24/7 support ticket system
              </p>
              <a 
                href="mailto:<EMAIL>" 
                className="mt-1 inline-flex items-center text-sm font-medium text-primary hover:underline"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>Close</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ContactSupportModal;
