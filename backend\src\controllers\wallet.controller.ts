import { Request, Response, NextFunction } from 'express';
import { WalletService } from '../services/wallet.service';
import logger from '../utils/logger';
import { ApiError } from '../middlewares/errorHandler';

class WalletController {
  private walletService: WalletService;

  constructor(walletService: WalletService) {
    this.walletService = walletService;
  }

  public getWallet = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ message: 'Unauthorized: User not authenticated' });
        return;
      }

      const wallet = await this.walletService.getWalletByUserId(req.user.id);
      res.status(200).json(wallet);
    } catch (error) {
      next(error);
    }
  };

  public creditWallet = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ message: 'Unauthorized: User not authenticated' });
        return;
      }

      const { amount } = req.body;
      const wallet = await this.walletService.depositFunds(
        req.user.id, 
        amount, 
        'system', 
        { provider: 'system', phoneNumber: 'admin' }, 
        'admin-credit'
      );
      res.status(200).json(wallet);
    } catch (error) {
      next(error);
    }
  };

  public debitWallet = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ message: 'Unauthorized: User not authenticated' });
        return;
      }

      const { amount } = req.body;
      const wallet = await this.walletService.processPayment(req.user.id, amount, 'Admin debit');
      res.status(200).json(wallet);
    } catch (error) {
      next(error);
    }
  };

  public getTransactions = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ message: 'Unauthorized: User not authenticated' });
        return;
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;

      const wallet = await this.walletService.getWalletByUserId(req.user.id);
      const transactions = await this.walletService.getTransactions(wallet.id, page, limit);
      
      res.status(200).json(transactions);
    } catch (error) {
      next(error);
    }
  };

  public getWithdrawalHistory = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ message: 'Unauthorized: User not authenticated' });
        return;
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      const result = await this.walletService.getWithdrawalHistory(
        req.user.id,
        page,
        limit
      );
      
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public getDepositHistory = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ message: 'Unauthorized: User not authenticated' });
        return;
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 10;

      const result = await this.walletService.getDepositHistory(
        req.user.id,
        page,
        limit
      );
      
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public deposit = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ message: 'Unauthorized: User not authenticated' });
        return;
      }

      const { amount, paymentMethod, reference } = req.body;
      const paymentDetails = req.body.paymentDetails || {};
      
      const transaction = await this.walletService.depositFunds(
        req.user.id,
        amount,
        paymentMethod,
        paymentDetails,
        reference
      );
      
      res.status(200).json(transaction);
    } catch (error) {
      next(error);
    }
  };

  public withdraw = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ message: 'Unauthorized: User not authenticated' });
        return;
      }

      const { amount, paymentMethod, reference } = req.body;
      const paymentDetails = req.body.paymentDetails || {};
      
      const transaction = await this.walletService.withdrawFunds(
        req.user.id,
        amount,
        paymentMethod,
        paymentDetails,
        reference
      );
      
      res.status(200).json(transaction);
    } catch (error) {
      next(error);
    }
  };

  public validateWithdrawal = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ message: 'Unauthorized: User not authenticated' });
        return;
      }

      const { amount, paymentMethod, recipientDetails } = req.body;
      
      const result = await this.walletService.validateWithdrawal(
        req.user.id,
        amount,
        paymentMethod,
        recipientDetails
      );
      
      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  };

  public getWithdrawalStatus = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ message: 'Unauthorized: User not authenticated' });
        return;
      }

      const withdrawalId = req.params.withdrawalId;
      const transaction = await this.walletService.getWithdrawalStatus(withdrawalId);
      
      if (!transaction) {
        res.status(404).json({ message: 'Withdrawal not found' });
        return;
      }
      
      // Transform to match frontend's expected structure
      const response = {
        id: transaction.id,
        status: transaction.status.toLowerCase(),
        message: transaction.description || 'No additional information available',
        transactionId: transaction.id
      };
      
      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  };

  public getDepositStatus = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({ message: 'Unauthorized: User not authenticated' });
        return;
      }

      const depositId = req.params.depositId;
      const transaction = await this.walletService.getDepositStatus(depositId);
      
      if (!transaction) {
        res.status(404).json({ message: 'Deposit not found' });
        return;
      }
      
      // Transform to match frontend's expected structure
      const response = {
        id: transaction.id,
        status: transaction.status.toLowerCase(),
        message: transaction.description || 'No additional information available',
        transactionId: transaction.id
      };
      
      res.status(200).json(response);
    } catch (error) {
      next(error);
    }
  };
}

export default WalletController;
