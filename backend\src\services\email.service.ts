import nodemailer from 'nodemailer';
import { createTransport } from 'nodemailer';
import config from '../config/env';
import logger from '../utils/logger';

export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = createTransport({
      host: config.SMTP_HOST,
      port: config.SMTP_PORT,
      secure: config.SMTP_PORT === 465, // true for 465, false for other ports
      auth: {
        user: config.SMTP_USER,
        pass: config.SMTP_PASS,
      },
      // For development, if SMTP is not configured, use ethereal email service
      ...(config.NODE_ENV === 'development' && !config.SMTP_HOST && {
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: {
          user: '<EMAIL>', // Update with generated credentials if using ethereal
          pass: 'ethereal_password',
        },
      }),
    });
  }

  /**
   * Send email
   */
  async sendEmail(
    to: string,
    subject: string,
    html: string
  ): Promise<void> {
    try {
      const mailOptions = {
        from: `"Fotis Agro Trading" <${config.EMAIL_FROM}>`,
        to,
        subject,
        html,
      };

      const info = await this.transporter.sendMail(mailOptions);

      if (config.NODE_ENV === 'development') {
        logger.info(`Email sent: ${info.messageId}`);
        // Log preview URL for local development
        if (info.preview) {
          logger.info(`Email preview URL: ${info.preview}`);
        }
      }
    } catch (error) {
      logger.error('Error sending email:', error);
      throw new Error('Failed to send email');
    }
  }

  /**
   * Send email verification email
   */
  async sendVerificationEmail(
    to: string,
    name: string,
    token: string
  ): Promise<void> {
    const subject = 'Verify Your Email for Fotis Agro Trading';
    
    // In a real application, use a proper email template with a link to your frontend
    const verificationUrl = `${config.CORS_ORIGIN}/verify-email?token=${token}`;
    
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Hello, ${name}!</h2>
        <p>Thank you for registering with Fotis Agro Trading. Please verify your email address by clicking the button below:</p>
        <div style="margin: 30px 0;">
          <a href="${verificationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">Verify Email</a>
        </div>
        <p>If the button doesn't work, you can also click on the link below or copy and paste it into your browser:</p>
        <p><a href="${verificationUrl}">${verificationUrl}</a></p>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not create an account, please ignore this email.</p>
        <p>Best regards,<br>The Fotis Agro Trading Team</p>
      </div>
    `;

    await this.sendEmail(to, subject, html);
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(
    to: string,
    name: string,
    token: string
  ): Promise<void> {
    const subject = 'Reset Your Password - Fotis Agro Trading';
    
    // In a real application, use a proper email template with a link to your frontend
    const resetUrl = `${config.CORS_ORIGIN}/reset-password?token=${token}`;
    
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Hello, ${name}!</h2>
        <p>We received a request to reset your Fotis Agro Trading account password. Click the button below to reset your password:</p>
        <div style="margin: 30px 0;">
          <a href="${resetUrl}" style="background-color: #2196F3; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">Reset Password</a>
        </div>
        <p>If the button doesn't work, you can also click on the link below or copy and paste it into your browser:</p>
        <p><a href="${resetUrl}">${resetUrl}</a></p>
        <p>This link will expire in 1 hour.</p>
        <p>If you did not request a password reset, please ignore this email or contact our support team if you have concerns.</p>
        <p>Best regards,<br>The Fotis Agro Trading Team</p>
      </div>
    `;

    await this.sendEmail(to, subject, html);
  }
} 