import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Warehouse, PackageSearch, ClipboardList, CheckCircle, Package } from "lucide-react";
import { getServicesByCategory } from '@/data/servicesData'; // Assuming this exists and works
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"; // Import Select components
import { Checkbox } from "@/components/ui/checkbox"; // Import Checkbox component


// --- Import Toast Components ---
import { Toaster } from "@/components/ui/toaster";
import { useToast } from "@/components/ui/use-toast";
// -----------------------------

// --- Import pdfMake and fonts dynamically ---
// Will import within the handler
// ------------------------------------------


// Mock warehouse data
const warehouseLocations = [
  { id: "wrh-01", name: "Central Agricultural Warehouse", location: "Kampala", availableSpace: 1200, rate: 2.5 },
  { id: "wrh-02", name: "Eastern Grain Storage", location: "Jinja", availableSpace: 850, rate: 2.2 },
  { id: "wrh-03", name: "Northern Produce Warehouse", location: "Gulu", availableSpace: 670, rate: 2.0 },
];

// Mock initial inventory data - Now managed by useState
const initialInventoryItems = [
  {
    id: "inv-101",
    productName: "Coffee (Arabica)",
    quantity: "500", unit: "kg", // Separated quantity and unit
    grade: "Grade A",
    origin: "Rwanda",
    harvestYear: 2023,
    marketPrice: 4.5, // Price per unit (e.g., per kg)
    warehouseId: "wrh-01",
    storedDate: "2023-10-15",
    status: "In Storage",
    batchNumber: "A-BATCH-001",
    expirationDate: "2025-10-15",
    locationDetail: "Aisle 5, Rack 3, Shelf 2",
    certificationStatus: ["Organic"] // Added certification
  },
  {
    id: "inv-102",
    productName: "Coffee (Robusta)",
    quantity: "350", unit: "kg",
    grade: "Grade B",
    origin: "Uganda",
    harvestYear: 2023,
    marketPrice: 3.8,
    warehouseId: "wrh-01",
    storedDate: "2023-11-02",
    status: "In Storage",
    batchNumber: "R-BATCH-007",
    expirationDate: "2025-11-02",
    locationDetail: "Aisle 5, Rack 4, Shelf 1",
    certificationStatus: ["Fair Trade"]
  },
  {
    id: "inv-103",
    productName: "Cocoa",
    quantity: "200", unit: "kg",
    grade: "Fine Grade",
    origin: "Ghana",
    harvestYear: 2022,
    marketPrice: 2.1,
    warehouseId: "wrh-02",
    storedDate: "2023-09-28",
    status: "Ready for Pickup",
    batchNumber: "C-BATCH-012",
    expirationDate: "2024-12-31",
    locationDetail: "Receiving Zone, Bay 1A",
    certificationStatus: ["Organic", "Fair Trade"]
  },
   {
    id: "inv-104",
    productName: "Maize",
    quantity: "1000", unit: "kg",
    grade: "Grade 1",
    origin: "Kenya",
    harvestYear: 2024,
    marketPrice: 0.5,
    warehouseId: "wrh-01",
    storedDate: "2024-01-05",
    status: "In Storage",
    batchNumber: "M-BATCH-045",
    expirationDate: "2025-08-01",
    locationDetail: "Aisle 1, Rack 10, Ground Level",
    certificationStatus: []
    },
   {
    id: "inv-105",
    productName: "Beans",
    quantity: "700", unit: "kg",
    grade: "Sugar Beans",
    origin: "Tanzania",
    harvestYear: 2023,
    marketPrice: 1.2,
    warehouseId: "wrh-03",
    storedDate: "2024-01-10",
    status: "In Storage",
    batchNumber: "B-BATCH-021",
    expirationDate: "2025-09-01",
    locationDetail: "Zone C, Row 5",
    certificationStatus: ["Organic"]
    },
];

const commodityTypes = ["Coffee (Robusta)", "Coffee (Arabica)", "COCOA", "Sesame", "Wheat (Pollard)", "Sunflower"];
const quantityUnits = ["kg", "metric tons"]; // Added units
const certificationOptions = ["Organic", "Fair Trade", "Rainforest Alliance", "UTZ Certified"]; // Example certifications

const WarehousingServices = () => {
  const [activeTab, setActiveTab] = useState("storage");

  // --- Inventory Items State (Refactored from const to state) ---
  const [inventoryItems, setInventoryItems] = useState(initialInventoryItems);
  // ------------------------------------------------------------

  // --- State for Service Configuration Dialog ---
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);
  const [configuringServiceType, setConfiguringServiceType] = useState<'inventory' | null>(null); // Removed 'storage' config type
  const [configDialogFormData, setConfigDialogFormData] = useState<any>({});
  // ------------------------------------------------------------------

  // --- State for Booking Confirmation Dialog ---
  const [isBookingConfirmDialogOpen, setIsBookingConfirmDialogOpen] = useState(false);
  const [bookingConfirmationDetails, setBookingConfirmationDetails] = useState<any>(null);
  // -----------------------------------------------

  // --- State for Inventory Details Dialog ---
  const [isInventoryDetailsDialogOpen, setIsInventoryDetailsDialogOpen] = useState(false);
  const [inventoryDetailsItem, setInventoryDetailsItem] = useState<any>(null);
  // ----------------------------------------------

  // --- New State for Commodity Input Dialog ---
  const [isCommodityInputDialogOpen, setIsCommodityInputDialogOpen] = useState(false);
  const [commodityInputFormData, setCommodityInputFormData] = useState<any>({
    commodityType: '',
    quantity: '',
    unit: 'kg', // Default unit
    grade: '',
    origin: '',
    harvestYear: '',
    marketPrice: '',
    storageLocation: '',
    lotBatchNumber: `LOT-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`, // Auto-generated mock
    certificationStatus: [], // Array for multiple selections
  });
  // --------------------------------------------

  // --- Use the Toast Hook ---
  const { toast } = useToast();
  // --------------------------


  const warehousingServices = getServicesByCategory("warehousing"); // Assuming service IDs match logic below ('warehouse-storage', 'inventory-management')

  // --- Modified Handlers for Service Configuration Dialog ---
  const handleAccessServiceClick = (serviceId: string) => {
    if (serviceId === "warehouse-storage") {
      // Open the NEW commodity input dialog for storage
      setIsCommodityInputDialogOpen(true);
      // Re-generate batch number each time the dialog is opened
       setCommodityInputFormData(prev => ({
           ...prev,
           lotBatchNumber: `LOT-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`,
           // Keep previous input if any, but reset key fields if desired
           commodityType: '', quantity: '', unit: 'kg', grade: '', origin: '',
           harvestYear: '', marketPrice: '', storageLocation: '', certificationStatus: [],
       }));

    } else if (serviceId === "inventory-management") { // Assuming this is the Inventory card ID
      // Open the existing configuration dialog for inventory
      setConfiguringServiceType('inventory');
      setConfigDialogFormData({ integrationApiKey: '', initialSyncMethod: 'API', preferredFormat: 'CSV' });
      setIsConfigDialogOpen(true);
    }
    // If other service types exist, handle them here
  };

  const handleConfigInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { id, value } = e.target;
    setConfigDialogFormData(prev => ({ ...prev, [id]: value }));
  };
   // Handle Select change for config dialog
  const handleConfigSelectChange = (id: string, value: string) => {
     setConfigDialogFormData(prev => ({ ...prev, [id]: value }));
  };


  const handleConfigureSubmit = () => {
    // This now only handles the 'inventory' configuration submission
    console.log(`Attempting to configure Inventory service with data:`, configDialogFormData);

    let isValid = true;
    if (!configDialogFormData.integrationApiKey) {
       toast({ title: "Validation Error", description: "Please provide an Integration API Key.", variant: "destructive" });
       isValid = false;
    }

    if (!isValid) {
        return;
    }

    toast({
        title: "Configuration Successful!",
        description: `Inventory service is now configured.`,
    });

    setActiveTab('inventory'); // Switch to inventory tab after configuration
    setIsConfigDialogOpen(false);
    setConfiguringServiceType(null);
    setConfigDialogFormData({});
  };

  const handleConfigDialogClose = () => {
    setIsConfigDialogOpen(false);
    setConfiguringServiceType(null);
    setConfigDialogFormData({});
  };
  // ------------------------------------------------------------------

  // --- Handler for Booking Space (Remains the same) ---
  const handleBookSpaceClick = (warehouseId: string) => {
    const warehouse = warehouseLocations.find(w => w.id === warehouseId);
    if (warehouse) {
      const simulatedBooking = {
        warehouseName: warehouse.name,
        location: warehouse.location,
        spaceNumber: `Simulated Space in Aisle ${Math.floor(Math.random() * 10 + 1)}, Bay ${Math.floor(Math.random() * 20 + 1)}`,
        bookingDate: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }),
        bookingTime: new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
        simulatedCapacityBooked: "Approx. 500 kg (Example)",
        status: "Pending Confirmation",
      };
      setBookingConfirmationDetails(simulatedBooking);
      setIsBookingConfirmDialogOpen(true);
    } else {
      console.error(`Warehouse with ID ${warehouseId} not found.`);
      toast({
          title: "Booking Failed",
          description: `Could not find details for warehouse ID ${warehouseId}.`,
          variant: "destructive"
      });
    }
  };

  const handleBookingConfirmedOK = () => {
      setIsBookingConfirmDialogOpen(false);
      if (bookingConfirmationDetails) {
          toast({
              title: "Booking Confirmed!",
              description: (
                  <div className="text-sm">
                      Space booked at <span className="font-medium">{bookingConfirmationDetails.warehouseName}</span>.<br/>
                      Assigned Space: <span className="font-medium">{bookingConfirmationDetails.spaceNumber}</span><br/>
                      Date & Time: <span className="font-medium">{bookingConfirmationDetails.bookingDate}</span> at <span className="font-medium">{bookingConfirmationDetails.bookingTime}</span>
                  </div>
              ),
          });
      }
      setBookingConfirmationDetails(null);
  };
  // -----------------------------------

  // --- Handler for View Details Button (Inventory Tab - Remains the same) ---
  const handleViewDetailsClick = (item: any) => {
      setInventoryDetailsItem(item);
      setIsInventoryDetailsDialogOpen(true);
  };

  const handleInventoryDetailsDialogClose = () => {
      setIsInventoryDetailsDialogOpen(false);
      setInventoryDetailsItem(null);
  };
  // ------------------------------------------------------------------------

  // --- Modified Handler for Generate Report Button (Inventory Tab) ---
  const handleGenerateReportClick = async () => { // Function is async
      console.log("Generating PDF report for current inventory and storage data...");

      toast({
          title: "Generating Report...",
          description: "Compiling data and creating PDF.",
      });

      try {
          // --- Dynamically Import pdfMake and fonts ---
          const pdfMake = await import("pdfmake/build/pdfmake");
          const pdfFonts = await import("pdfmake/build/vfs_fonts");
          (pdfMake as any).vfs = (pdfFonts as any).pdfMake.vfs; // Type assertion for vfs

          // --- Define the document structure for pdfMake ---
           const documentDefinition: any = {
               content: [
                   { text: 'Warehouse & Inventory Report', style: 'header' },
                   { text: `Report Date: ${new Date().toLocaleDateString()}`, alignment: 'right', margin: [0, 0, 0, 20] },

                   { text: 'Storage Facilities Overview', style: 'subheader' },
                   {
                       table: {
                           widths: ['auto', '*', 'auto', 'auto'],
                           body: [
                               [
                                   { text: 'Name', style: 'tableHeader' },
                                   { text: 'Location', style: 'tableHeader' },
                                   { text: 'Available Space (m²)', style: 'tableHeader' },
                                   { text: 'Rate (UGX/kg/day)', style: 'tableHeader' }
                               ],
                               ...warehouseLocations.map(warehouse => [
                                   warehouse.name,
                                   warehouse.location,
                                   warehouse.availableSpace.toString(),
                                   warehouse.rate.toString()
                               ])
                           ]
                       },
                       layout: {
                           hLineWidth: function (i: number, node: any) { return (i === 0 || i === node.table.body.length) ? 2 : 1; },
                           vLineWidth: function (i: number, node: any) { return (i === 0 || i === node.table.widths.length) ? 2 : 1; },
                           hLineColor: function (i: number, node: any) { return (i === 0 || i === node.table.body.length) ? '#000' : '#aaa'; },
                           vLineColor: function (i: number, node: any) { return (i === 0 || i === node.table.widths.length) ? '#000' : '#aaa'; },
                           paddingLeft: function(i: number, node: any) { return 8; },
                           paddingRight: function(i: number, node: any) { return 8; },
                           paddingTop: function(i: number, node: any) { return 5; },
                           paddingBottom: function(i: number, node: any) { return 5; },
                       },
                       margin: [0, 10, 0, 20]
                   },

                   { text: 'Current Inventory Items', style: 'subheader' },
                   {
                       table: {
                           // Adjusted widths to fit more columns
                           widths: ['*', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto', 'auto'],
                            body: [
                               [
                                   { text: 'Product', style: 'tableHeader' },
                                   { text: 'Quantity', style: 'tableHeader' },
                                   { text: 'Unit', style: 'tableHeader' }, // Added Unit column
                                   { text: 'Warehouse', style: 'tableHeader' },
                                   { text: 'Location Detail', style: 'tableHeader' },
                                   { text: 'Batch Number', style: 'tableHeader' },
                                   { text: 'Expiration Date', style: 'tableHeader' },
                                   { text: 'Status', style: 'tableHeader' },
                                   // NOTE: Added columns for Grade, Origin, Harvest Year, Price, Certification are possible
                                   // but might make the table too wide. Added to doc structure as example below.
                               ],
                               ...inventoryItems.map(item => [
                                   item.productName,
                                   item.quantity,
                                   item.unit, // Add unit data
                                   warehouseLocations.find(w => w.id === item.warehouseId)?.name || 'N/A',
                                   item.locationDetail || 'N/A',
                                   item.batchNumber || 'N/A',
                                   item.expirationDate || 'N/A',
                                   item.status,
                                    // Add more columns if they fit or use landscape + wider paper
                                    // item.grade || 'N/A',
                                    // item.origin || 'N/A',
                                    // item.harvestYear ? item.harvestYear.toString() : 'N/A',
                                    // item.marketPrice ? item.marketPrice.toString() : 'N/A',
                                    // item.certificationStatus && item.certificationStatus.length > 0 ? item.certificationStatus.join(', ') : 'None'
                               ])
                            ]
                        },
                        layout: 'lightHorizontalLines',
                        margin: [0, 10, 0, 0]
                   }
               ],
                styles: {
                   header: {
                       fontSize: 18,
                       bold: true,
                       margin: [0, 0, 0, 10]
                   },
                   subheader: {
                       fontSize: 14,
                       bold: true,
                       margin: [0, 10, 0, 5]
                   },
                    tableHeader: {
                       bold: true,
                       fontSize: 12,
                       color: 'black'
                   }
               },
               defaultStyle: {
                   fontSize: 10,
               },
               // --- FIXED SYNTAX ERROR: Added comma after defaultStyle ---
               pageOrientation: 'landscape' // Use landscape for more columns
               // --------------------------------------------------------
           };

          // --- Create and Download the PDF ---
          (pdfMake as any).createPdf(documentDefinition).download('YourWMSReport.pdf', () => {
               toast({
                   title: "Report Downloaded",
                   description: "The report has been downloaded to your Downloads folder.",
                   icon: <ClipboardList className="w-5 h-5" />,
               });
           });

      } catch (error) {
          console.error("Error generating PDF:", error);
          toast({
              title: "Report Generation Failed",
              description: `An error occurred while generating the PDF report: ${(error as Error).message}`, // Display error message
              variant: "destructive",
          });
      }
  };
  // -------------------------------------------------------------

  // --- Handlers for Commodity Input Dialog ---
   const handleCommodityInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
       const { id, value } = e.target;
       setCommodityInputFormData(prev => ({ ...prev, [id]: value }));
   };

   const handleCommoditySelectChange = (id: string, value: string) => {
        setCommodityInputFormData(prev => ({ ...prev, [id]: value }));
   };

   const handleCertificationChange = (certification: string, checked: boolean) => {
       setCommodityInputFormData(prev => {
           const currentCerts = prev.certificationStatus || [];
           if (checked) {
               return { ...prev, certificationStatus: [...currentCerts, certification] };
           } else {
               return { ...prev, certificationStatus: currentCerts.filter((c: string) => c !== certification) };
           }
       });
   };

   const handleCommodityInputSubmit = () => {
       console.log("Submitting new commodity data:", commodityInputFormData);

       // --- Basic Validation ---
       const requiredFields = ['commodityType', 'quantity', 'unit', 'grade', 'origin', 'harvestYear', 'marketPrice', 'storageLocation', 'lotBatchNumber'];
       for (const field of requiredFields) {
           if (!commodityInputFormData[field]) {
               toast({
                   title: "Validation Error",
                   description: `Please fill in the '${field}' field.`,
                   variant: "destructive"
               });
               return; // Stop submission if any required field is empty
           }
       }
        // Validate numeric fields
       if (isNaN(parseFloat(commodityInputFormData.quantity)) || parseFloat(commodityInputFormData.quantity) <= 0) {
           toast({ title: "Validation Error", description: "Please enter a valid positive quantity.", variant: "destructive" });
           return;
       }
       if (isNaN(parseInt(commodityInputFormData.harvestYear)) || parseInt(commodityInputFormData.harvestYear) <= 0) {
            toast({ title: "Validation Error", description: "Please enter a valid harvest year.", variant: "destructive" });
            return;
        }
        if (isNaN(parseFloat(commodityInputFormData.marketPrice)) || parseFloat(commodityInputFormData.marketPrice) < 0) {
             toast({ title: "Validation Error", description: "Please enter a valid non-negative market price.", variant: "destructive" });
             return;
         }
       // ------------------------

       // --- Simulate Adding to Inventory State ---
       const newInventoryItem = {
           id: `inv-${Date.now()}`, // Generate unique ID
           productName: commodityInputFormData.commodityType,
           quantity: commodityInputFormData.quantity,
           unit: commodityInputFormData.unit, // Add unit
           grade: commodityInputFormData.grade, // Add grade
           origin: commodityInputFormData.origin, // Add origin
           harvestYear: parseInt(commodityInputFormData.harvestYear), // Convert to number
           marketPrice: parseFloat(commodityInputFormData.marketPrice), // Convert to number
           // Need to map storageLocation input to an actual warehouseId
           // For this mockup, let's just link to a random warehouse or prompt user
           // A real system would validate location against available spaces
           warehouseId: warehouseLocations[Math.floor(Math.random() * warehouseLocations.length)].id, // Link to a random warehouse for mockup
           locationDetail: commodityInputFormData.storageLocation, // Use the input for detailed location
           storedDate: new Date().toISOString().split('T')[0], // Current date
           status: "In Storage", // Default status
           batchNumber: commodityInputFormData.lotBatchNumber, // Use the generated/displayed batch number
           expirationDate: 'N/A', // Placeholder - real system calculates/inputs this
           certificationStatus: commodityInputFormData.certificationStatus, // Add certifications
       };

       setInventoryItems(prevItems => [...prevItems, newInventoryItem]); // Add new item to state

       // --- Success Feedback ---
       toast({
           title: "Commodity Added!",
           description: `Details for ${newInventoryItem.productName} (Lot: ${newInventoryItem.batchNumber}) recorded.`,
           icon: <Package className="w-5 h-5" />,
       });

       // --- Close Dialog and Reset Form ---
       handleCommodityInputDialogClose();

       // --- Switch to Inventory Tab ---
       setActiveTab('inventory');
   };

   const handleCommodityInputDialogClose = () => {
       setIsCommodityInputDialogOpen(false);
       // Reset form data only when closing
       setCommodityInputFormData({
           commodityType: '', quantity: '', unit: 'kg', grade: '', origin: '',
           harvestYear: '', marketPrice: '', storageLocation: '',
           lotBatchNumber: `LOT-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`, // Generate a new one for next time
           certificationStatus: [],
       });
   };
  // -------------------------------------------


  return (
    <div className="container mx-auto py-8">
      {/* --- Toaster container --- */}
      <Toaster />
      {/* ------------------------- */}
      <div className="flex items-center mb-6">
        <Warehouse className="h-8 w-8 mr-2 text-primary" />
        <h1 className="text-3xl font-bold">Warehousing Services</h1>
      </div>

      <p className="text-muted-foreground mb-8">
        Access secure storage facilities for your agricultural products with real-time inventory tracking and management.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {warehousingServices.map((service) => (
          <Card key={service.id} className="bg-card hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle>{service.title}</CardTitle>
              <CardDescription>{service.description}</CardDescription>
            </CardHeader>
            <CardFooter>
              <Button variant="outline" onClick={() => handleAccessServiceClick(service.id)}>
                Access Service
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* --- Service Configuration Dialog (Now only for Inventory config) --- */}
      {/* Only open if configuringServiceType is 'inventory' */}
      <Dialog open={isConfigDialogOpen && configuringServiceType === 'inventory'} onOpenChange={setIsConfigDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Configure Inventory Service</DialogTitle> {/* Specific title */}
            <DialogDescription>
              Please provide the necessary details to set up your inventory service integration.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
              <> {/* Use fragment as only one type of config here */}
                 <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="integrationApiKey" className="text-right">
                    Integration API Key
                  </Label>
                  <Input
                    id="integrationApiKey"
                    value={configDialogFormData.integrationApiKey || ''}
                    onChange={handleConfigInputChange}
                    className="col-span-3"
                    placeholder="Paste your API Key here"
                  />
                </div>
                 <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="initialSyncMethod" className="text-right">
                    Sync Method
                  </Label>
                  <Select onValueChange={(value) => handleConfigSelectChange('initialSyncMethod', value)} value={configDialogFormData.initialSyncMethod || 'API'}>
                     <SelectTrigger id="initialSyncMethod" className="col-span-3">
                       <SelectValue placeholder="Select sync method" />
                     </SelectTrigger>
                     <SelectContent>
                       <SelectItem value="API">Real-time API</SelectItem>
                       <SelectItem value="CSV">CSV Upload (Batch)</SelectItem>
                       <SelectItem value="FTP">FTP Sync</SelectItem>
                     </SelectContent>
                   </Select>
                </div>
                 <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="preferredFormat" className="text-right">
                    Preferred Format
                  </Label>
                   <Input
                    id="preferredFormat"
                    value={configDialogFormData.preferredFormat || ''}
                    onChange={handleConfigInputChange}
                    className="col-span-3"
                    placeholder="e.g., CSV, JSON, XML"
                  />
                </div>
              </>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleConfigDialogClose}>Cancel</Button>
            <Button onClick={handleConfigureSubmit}>Configure & Proceed</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* ------------------------------------------ */}


      {/* --- Booking Confirmation Dialog (Remains the same) --- */}
       <Dialog open={isBookingConfirmDialogOpen} onOpenChange={setIsBookingConfirmDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
             <div className="flex items-center justify-center text-green-500 mb-2">
                <CheckCircle className="w-8 h-8 mr-2" />
                <DialogTitle className="text-center text-xl font-semibold text-green-600">Booking Successful!</DialogTitle>
            </div>
            <DialogDescription className="text-center">
              Your space booking details are confirmed below.
            </DialogDescription>
          </DialogHeader>
          {bookingConfirmationDetails && (
            <div className="grid gap-4 py-4 text-sm">
              <div className="grid grid-cols-2 items-center">
                <div className="font-medium text-muted-foreground">Warehouse:</div>
                <div>{bookingConfirmationDetails.warehouseName}</div>
              </div>
               <div className="grid grid-cols-2 items-center">
                <div className="font-medium text-muted-foreground">Location:</div>
                <div>{bookingConfirmationDetails.location}</div>
              </div>
               <div className="grid grid-cols-2 items-center">
                <div className="font-medium text-muted-foreground">Assigned Space:</div>
                <div>{bookingConfirmationDetails.spaceNumber}</div>
              </div>
               <div className="grid grid-cols-2 items-center">
                <div className="font-medium text-muted-foreground">Booking Date & Time:</div>
                <div>{bookingConfirmationDetails.bookingDate} at {bookingConfirmationDetails.bookingTime}</div>
              </div>
               <div className="grid grid-cols-[120px_auto] items-center gap-2">
                <div className="font-medium text-muted-foreground">Capacity Booked:</div>
                <div>{bookingConfirmationDetails.simulatedCapacityBooked}</div>
              </div>
               <div className="grid grid-cols-[120px_auto] items-center gap-2">
                <div className="font-medium text-muted-foreground">Status:</div>
                <div>
                   <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                      bookingConfirmationDetails.status === "Pending Confirmation" ? "bg-orange-100 text-orange-800" : "bg-green-100 text-green-800"
                    }`}>
                      {bookingConfirmationDetails.status}
                    </span>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="sm:justify-center">
            <Button onClick={handleBookingConfirmedOK}>OK</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* ----------------------------------------- */}

      {/* --- Inventory Details Dialog (Remains the same) --- */}
      <Dialog open={isInventoryDetailsDialogOpen} onOpenChange={handleInventoryDetailsDialogClose}>
        <DialogContent className="sm:max-w-[450px]">
          <DialogHeader>
             <div className="flex items-center gap-2 text-primary mb-2">
                <Package className="w-7 h-7" />
                <DialogTitle className="text-xl font-semibold">Inventory Item Details</DialogTitle>
            </div>
            <DialogDescription>
              Detailed information about the selected inventory item.
            </DialogDescription>
          </DialogHeader>
          {inventoryDetailsItem && (
            <div className="grid gap-4 py-4 text-sm">
              <div className="grid grid-cols-[120px_auto] items-center gap-2">
                <div className="font-medium text-muted-foreground">Product:</div>
                <div className="font-semibold text-base">{inventoryDetailsItem.productName}</div>
              </div>
               <div className="grid grid-cols-[120px_auto] items-center gap-2">
                <div className="font-medium text-muted-foreground">Quantity:</div>
                <div>{inventoryDetailsItem.quantity} {inventoryDetailsItem.unit}</div> {/* Show unit */}
              </div>
              <div className="grid grid-cols-[120px_auto] items-center gap-2">
                 <div className="font-medium text-muted-foreground">Grade:</div>
                 <div>{inventoryDetailsItem.grade || 'N/A'}</div>
               </div>
               <div className="grid grid-cols-[120px_auto] items-center gap-2">
                 <div className="font-medium text-muted-foreground">Origin:</div>
                 <div>{inventoryDetailsItem.origin || 'N/A'}</div>
               </div>
               <div className="grid grid-cols-[120px_auto] items-center gap-2">
                 <div className="font-medium text-muted-foreground">Harvest Year:</div>
                 <div>{inventoryDetailsItem.harvestYear || 'N/A'}</div>
               </div>
               <div className="grid grid-cols-[120px_auto] items-center gap-2">
                  <div className="font-medium text-muted-foreground">Market Price:</div>
                  <div>{inventoryDetailsItem.marketPrice ? `UGX ${inventoryDetailsItem.marketPrice}` : 'N/A'}</div>
                </div>
               <div className="grid grid-cols-[120px_auto] items-center gap-2">
                <div className="font-medium text-muted-foreground">Warehouse:</div>
                <div>{warehouseLocations.find(w => w.id === inventoryDetailsItem.warehouseId)?.name || 'Unknown Warehouse'}</div>
              </div>
              {inventoryDetailsItem.locationDetail && (
                 <div className="grid grid-cols-[120px_auto] items-center gap-2">
                  <div className="font-medium text-muted-foreground">Location Detail:</div>
                  <div>{inventoryDetailsItem.locationDetail}</div>
                </div>
              )}
               {inventoryDetailsItem.batchNumber && (
                 <div className="grid grid-cols-[120px_auto] items-center gap-2">
                  <div className="font-medium text-muted-foreground">Batch Number:</div>
                  <div>{inventoryDetailsItem.batchNumber}</div>
                </div>
              )}
               {inventoryDetailsItem.expirationDate && (
                 <div className="grid grid-cols-[120px_auto] items-center gap-2">
                  <div className="font-medium text-muted-foreground">Expiration Date:</div>
                  <div>{inventoryDetailsItem.expirationDate}</div>
                </div>
              )}
              <div className="grid grid-cols-[120px_auto] items-center gap-2">
                 <div className="font-medium text-muted-foreground">Certification:</div>
                 <div>
                     {inventoryDetailsItem.certificationStatus && inventoryDetailsItem.certificationStatus.length > 0
                       ? inventoryDetailsItem.certificationStatus.join(', ')
                       : 'None'}
                 </div>
               </div>
               <div className="grid grid-cols-[120px_auto] items-center gap-2">
                <div className="font-medium text-muted-foreground">Date Stored:</div>
                <div>{inventoryDetailsItem.storedDate}</div>
              </div>
               <div className="grid grid-cols-[120px_auto] items-center gap-2">
                <div className="font-medium text-muted-foreground">Status:</div>
                <div>
                   <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                      inventoryDetailsItem.status === "In Storage" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800"
                    }`}>
                      {inventoryDetailsItem.status}
                    </span>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="sm:justify-end">
            <Button onClick={handleInventoryDetailsDialogClose}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* ---------------------------------------------- */}

       {/* --- Commodity Input Dialog (NEW) --- */}
        <Dialog open={isCommodityInputDialogOpen} onOpenChange={handleCommodityInputDialogClose}>
             <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto"> {/* Added max height and overflow */}
                <DialogHeader>
                    <div className="flex items-center gap-2 text-primary mb-2">
                        <Package className="w-7 h-7" />
                        <DialogTitle className="text-xl font-semibold">Add New Stored Commodity</DialogTitle>
                    </div>
                    <DialogDescription>
                        Enter details for the commodity you are storing.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                   {/* Commodity Type */}
                   <div className="grid grid-cols-4 items-center gap-4">
                       <Label htmlFor="commodityType" className="text-right">
                           Commodity Type
                       </Label>
                       <Select onValueChange={(value) => handleCommoditySelectChange('commodityType', value)} value={commodityInputFormData.commodityType}>
                           <SelectTrigger id="commodityType" className="col-span-3">
                             <SelectValue placeholder="Select commodity type" />
                           </SelectTrigger>
                           <SelectContent>
                             {commodityTypes.map(type => (
                                 <SelectItem key={type} value={type}>{type}</SelectItem>
                             ))}
                           </SelectContent>
                         </Select>
                   </div>

                   {/* Quantity and Unit */}
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="quantity" className="text-right">
                            Quantity
                        </Label>
                        <div className="col-span-3 flex gap-2">
                            <Input
                              id="quantity"
                              type="number"
                              value={commodityInputFormData.quantity}
                              onChange={handleCommodityInputChange}
                              placeholder="Enter quantity"
                              className="flex-grow"
                            />
                             <Select onValueChange={(value) => handleCommoditySelectChange('unit', value)} value={commodityInputFormData.unit}>
                                <SelectTrigger id="unit" className="w-[120px]">
                                   <SelectValue placeholder="Unit" />
                                 </SelectTrigger>
                                 <SelectContent>
                                   {quantityUnits.map(unit => (
                                     <SelectItem key={unit} value={unit}>{unit}</SelectItem>
                                   ))}
                                 </SelectContent>
                              </Select>
                        </div>
                    </div>

                   {/* Grade/Quality Classification */}
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="grade" className="text-right">
                            Grade/Quality
                        </Label>
                        <Input
                          id="grade"
                          value={commodityInputFormData.grade}
                          onChange={handleCommodityInputChange}
                          className="col-span-3"
                          placeholder="e.g., Grade A, Fine Grade"
                        />
                    </div>

                   {/* Origin/Source Region */}
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="origin" className="text-right">
                            Origin/Source
                        </Label>
                        <Input
                          id="origin"
                          value={commodityInputFormData.origin}
                          onChange={handleCommodityInputChange}
                          className="col-span-3"
                          placeholder="e.g., Uganda, Ethiopia"
                        />
                    </div>

                   {/* Harvest Year */}
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="harvestYear" className="text-right">
                            Harvest Year
                        </Label>
                         {/* Use text input for simplicity, allow numeric validation */}
                        <Input
                          id="harvestYear"
                          type="number" // Changed to number for better input on mobile
                          value={commodityInputFormData.harvestYear}
                          onChange={handleCommodityInputChange}
                          className="col-span-3"
                          placeholder="e.g., 2023"
                        />
                    </div>

                   {/* Current Market Price */}
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="marketPrice" className="text-right">
                            Market Price (UGX)
                        </Label>
                        <Input
                          id="marketPrice"
                          type="number"
                          value={commodityInputFormData.marketPrice}
                          onChange={handleCommodityInputChange}
                          className="col-span-3"
                          placeholder="e.g., 4.5"
                        />
                    </div>

                   {/* Storage Location */}
                    <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="storageLocation" className="text-right">
                            Storage Location Detail
                        </Label>
                        {/* In a real system, this would link to actual warehouse locations */}
                        <Input
                          id="storageLocation"
                          value={commodityInputFormData.storageLocation}
                          onChange={handleCommodityInputChange}
                          className="col-span-3"
                          placeholder="e.g., Aisle 5, Rack 3, Shelf 2"
                        />
                    </div>

                   {/* Lot/Batch Number (Auto-generated, Read-only) */}
                   <div className="grid grid-cols-4 items-center gap-4">
                       <Label htmlFor="lotBatchNumber" className="text-right">
                           Lot/Batch Number
                       </Label>
                       <Input
                         id="lotBatchNumber"
                         value={commodityInputFormData.lotBatchNumber}
                         className="col-span-3 font-mono text-muted-foreground"
                         readOnly // Make it read-only
                       />
                   </div>

                   {/* Certification Status (Multi-select Checkboxes) */}
                   <div className="grid grid-cols-4 items-start gap-4"> {/* Use items-start for alignment */}
                       <Label className="text-right pt-2"> {/* Align label top */}
                           Certification Status
                       </Label>
                       <div className="col-span-3 flex flex-col space-y-2">
                           {certificationOptions.map(cert => (
                               <div key={cert} className="flex items-center space-x-2">
                                   <Checkbox
                                       id={`cert-${cert.replace(/\s/g, '-')}`}
                                       checked={commodityInputFormData.certificationStatus.includes(cert)}
                                       onCheckedChange={(checked) => handleCertificationChange(cert, checked as boolean)}
                                   />
                                   <label
                                       htmlFor={`cert-${cert.replace(/\s/g, '-')}`}
                                       className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                                   >
                                       {cert}
                                   </label>
                               </div>
                           ))}
                       </div>
                   </div>

                </div>
                <DialogFooter>
                   <Button variant="outline" onClick={handleCommodityInputDialogClose}>Cancel</Button>
                   <Button onClick={handleCommodityInputSubmit}>Add Commodity</Button>
                </DialogFooter>
             </DialogContent>
         </Dialog>
       {/* ------------------------------------ */}


      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="storage" className="flex items-center gap-2">
            <Warehouse className="h-4 w-4" /> Storage Facilities
          </TabsTrigger>
          <TabsTrigger value="inventory" className="flex items-center gap-2">
            <PackageSearch className="h-4 w-4" /> Inventory Management
          </TabsTrigger>
        </TabsList>

        {/* Storage Facilities Tab */}
        <TabsContent value="storage">
          <Card>
            <CardHeader>
              <CardTitle>Available Warehouses</CardTitle>
              <CardDescription>View and book storage space at our partner facilities</CardDescription>
            </CardHeader>
            <CardContent>
              {/* ... (Search inputs remain the same) ... */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input id="location" placeholder="Search by location" />
                </div>
                <div>
                  <Label htmlFor="capacity">Minimum Capacity (kg)</Label>
                  <Input id="capacity" type="number" placeholder="Enter minimum capacity" />
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border text-left">
                      <th className="pb-3 text-muted-foreground font-medium">Name</th>
                      <th className="pb-3 text-muted-foreground font-medium">Location</th>
                      <th className="pb-3 text-muted-foreground font-medium">Available Space (m²)</th>
                      <th className="pb-3 text-muted-foreground font-medium">Rate (UGX/kg/day)</th>
                      <th className="pb-3 text-muted-foreground font-medium">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {warehouseLocations.map((warehouse) => (
                      <tr key={warehouse.id} className="border-b border-border">
                        <td className="py-3 text-sm font-medium">{warehouse.name}</td>
                        <td className="py-3">{warehouse.location}</td>
                        <td className="py-3">{warehouse.availableSpace}</td>
                        <td className="py-3">{warehouse.rate}</td>
                        <td className="py-3">
                          <Button variant="outline" size="sm" onClick={() => handleBookSpaceClick(warehouse.id)}>
                            Book Space
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Inventory Management Tab */}
        <TabsContent value="inventory">
          <Card>
            <CardHeader>
              <CardTitle>Your Inventory</CardTitle>
              <CardDescription>Track and manage your stored products</CardDescription>
               {/* Note: In a real system, this data would be fetched via API and kept
                   in sync via WebSockets/Event Bus based on changes in Storage Facilities
                   and operations (receiving, picking, etc.). This mockup uses static data
                   linked by warehouseId to simulate the relationship. Reports would also
                   typically be generated from the backend/analytics service which has access to the
                   full, potentially large dataset and server-side PDF generation capabilities.
                   Generating large reports client-side can lead to performance issues. */}
            </CardHeader>
            <CardContent>
               {/* ... (Search inputs remain the same) ... */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <Label htmlFor="product">Product</Label>
                  <Input id="product" placeholder="Search by product name" />
                </div>
                <div>
                  <Label htmlFor="warehouse">Warehouse</Label>
                  <Input id="warehouse" placeholder="Filter by warehouse" />
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-border text-left">
                      <th className="pb-3 text-muted-foreground font-medium">Product</th>
                      <th className="pb-3 text-muted-foreground font-medium">Quantity</th>
                      <th className="pb-3 text-muted-foreground font-medium">Unit</th>{/* Added Unit Header */}
                      <th className="pb-3 text-muted-foreground font-medium">Warehouse</th>
                      <th className="pb-3 text-muted-foreground font-medium">Date Stored</th>
                      <th className="pb-3 text-muted-foreground font-medium">Status</th>
                      <th className="pb-3 text-muted-foreground font-medium">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {inventoryItems.map((item) => (
                      <tr key={item.id} className="border-b border-border">
                        <td className="py-3 text-sm font-medium">{item.productName}</td>
                        <td className="py-3">{item.quantity}</td>
                        <td className="py-3">{item.unit || 'N/A'}</td> {/* Display Unit */}
                        <td className="py-3">{warehouseLocations.find(w => w.id === item.warehouseId)?.name || 'Unknown Warehouse'}</td>
                        <td className="py-3">{item.storedDate}</td>
                        <td className="py-3">
                          <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                            item.status === "In Storage" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800"
                          }`}>
                            {item.status}
                          </span>
                        </td>
                        <td className="py-3">
                           {/* Modified Button: Call the view details handler */}
                          <Button variant="ghost" size="sm" onClick={() => handleViewDetailsClick(item)}>
                            View Details
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
            <CardFooter>
               {/* Modified Button: Call the generate report handler */}
              <Button className="mr-2" onClick={handleGenerateReportClick}>
                <ClipboardList className="mr-2 h-4 w-4" /> Generate Report
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WarehousingServices;