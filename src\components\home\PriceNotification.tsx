import { useState } from "react";
import { Bell, ArrowUp, ArrowDown, X, Info } from "lucide-react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { commodities } from "@/data/commodityData";
import { toast } from "@/hooks/use-toast";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";

// Alert type definition
interface PriceAlert {
  id: string;
  commodityId: string;
  commodityName: string;
  targetPrice: number;
  direction: 'above' | 'below';
  isActive: boolean;
  createdAt: Date;
}

interface PriceNotificationProps {
  className?: string;
}

const PriceNotification = ({ className }: PriceNotificationProps) => {
  const [selectedCommodity, setSelectedCommodity] = useState(commodities[0].id);
  const [alertDirection, setAlertDirection] = useState<'above' | 'below'>('above');
  const [targetPrice, setTargetPrice] = useState('');
  const [alerts, setAlerts] = useState<PriceAlert[]>([
    // Sample alerts for demonstration
    {
      id: '1',
      commodityId: 'coffee-arabica',
      commodityName: 'Coffee (Arabica)',
      targetPrice: 17000,
      direction: 'above',
      isActive: true,
      createdAt: new Date()
    },
    {
      id: '2',
      commodityId: 'cocoa',
      commodityName: 'Cocoa',
      targetPrice: 19000,
      direction: 'below',
      isActive: true,
      createdAt: new Date()
    }
  ]);
  
  // Get commodity name by ID
  const getCommodityName = (id: string) => {
    const commodity = commodities.find(c => c.id === id);
    return commodity?.name || '';
  };
  
  // Get commodity current price by ID
  const getCommodityPrice = (id: string) => {
    const commodity = commodities.find(c => c.id === id);
    return commodity?.currentPrice || 0;
  };
  
  // Add new price alert
  const handleAddAlert = () => {
    // Validate input
    if (!targetPrice || isNaN(parseFloat(targetPrice))) {
      toast({
        title: "Invalid price",
        description: "Please enter a valid price",
        variant: "destructive"
      });
      return;
    }
    
    const price = parseFloat(targetPrice);
    const currentPrice = getCommodityPrice(selectedCommodity);
    
    // Check if price makes sense based on direction
    if (alertDirection === 'above' && price <= currentPrice) {
      toast({
        title: "Invalid target price",
        description: `Target price must be above the current price (${currentPrice.toLocaleString()} UGX)`,
        variant: "destructive"
      });
      return;
    }
    
    if (alertDirection === 'below' && price >= currentPrice) {
      toast({
        title: "Invalid target price",
        description: `Target price must be below the current price (${currentPrice.toLocaleString()} UGX)`,
        variant: "destructive"
      });
      return;
    }
    
    // Add new alert
    const newAlert: PriceAlert = {
      id: Date.now().toString(),
      commodityId: selectedCommodity,
      commodityName: getCommodityName(selectedCommodity),
      targetPrice: price,
      direction: alertDirection,
      isActive: true,
      createdAt: new Date()
    };
    
    setAlerts(prev => [newAlert, ...prev]);
    setTargetPrice('');
    
    toast({
      title: "Alert created",
      description: `You'll be notified when ${getCommodityName(selectedCommodity)} goes ${alertDirection} ${price.toLocaleString()} UGX`,
    });
  };
  
  // Toggle alert status
  const toggleAlertStatus = (id: string) => {
    setAlerts(prev => 
      prev.map(alert => 
        alert.id === id ? { ...alert, isActive: !alert.isActive } : alert
      )
    );
  };
  
  // Delete alert
  const deleteAlert = (id: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== id));
  };
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center text-xl">
          <Bell className="h-5 w-5 mr-2 text-[#8BC34A]" />
          Price Alerts
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <button className="ml-2 text-muted-foreground">
                  <Info className="h-4 w-4" />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="w-[200px] text-xs">
                  Set alerts for price changes and get notified when commodities reach your target prices. 
                  For demo purposes, these notifications will appear in the browser.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
        <CardDescription>Get notified when prices change</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="commodity">Commodity</Label>
            <Select 
              value={selectedCommodity} 
              onValueChange={setSelectedCommodity}
            >
              <SelectTrigger id="commodity">
                <SelectValue placeholder="Select commodity" />
              </SelectTrigger>
              <SelectContent>
                {commodities.map(commodity => (
                  <SelectItem key={commodity.id} value={commodity.id}>
                    {commodity.name} ({commodity.currentPrice.toLocaleString()} UGX)
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="direction">Alert when price is</Label>
            <Select 
              value={alertDirection} 
              onValueChange={(value: string) => setAlertDirection(value as 'above' | 'below')}
            >
              <SelectTrigger id="direction">
                <SelectValue placeholder="Select direction" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="above">
                  <div className="flex items-center">
                    <ArrowUp className="h-4 w-4 mr-2 text-[#8BC34A]" />
                    Above target
                  </div>
                </SelectItem>
                <SelectItem value="below">
                  <div className="flex items-center">
                    <ArrowDown className="h-4 w-4 mr-2 text-red-500" />
                    Below target
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="targetPrice">Target Price (UGX)</Label>
            <div className="flex space-x-2">
              <Input
                id="targetPrice"
                type="number"
                value={targetPrice}
                onChange={(e) => setTargetPrice(e.target.value)}
                placeholder={`e.g. ${Math.round(getCommodityPrice(selectedCommodity) * (alertDirection === 'above' ? 1.1 : 0.9)).toLocaleString()}`}
              />
              <Button onClick={handleAddAlert}>Set</Button>
            </div>
          </div>
        </div>
        
        <div className="border rounded-md overflow-hidden">
          {alerts.length === 0 ? (
            <div className="p-6 text-center">
              <p className="text-muted-foreground">No alerts set. Create your first alert above.</p>
            </div>
          ) : (
            <div className="divide-y">
              {alerts.map(alert => (
                <div key={alert.id} className="flex items-center justify-between p-4 hover:bg-muted/40">
                  <div className="flex items-center space-x-4">
                    <Switch
                      checked={alert.isActive}
                      onCheckedChange={() => toggleAlertStatus(alert.id)}
                    />
                    <div>
                      <div className="font-medium">{alert.commodityName}</div>
                      <div className="text-sm text-muted-foreground flex items-center">
                        {alert.direction === 'above' ? (
                          <ArrowUp className="h-3 w-3 mr-1 text-[#8BC34A]" />
                        ) : (
                          <ArrowDown className="h-3 w-3 mr-1 text-red-500" />
                        )}
                        {alert.direction === 'above' ? 'Above' : 'Below'} {alert.targetPrice.toLocaleString()} UGX
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className={!alert.isActive ? 'bg-muted text-muted-foreground' : ''}>
                      {alert.isActive ? 'Active' : 'Disabled'}
                    </Badge>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => deleteAlert(alert.id)}
                    >
                      <X className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="text-xs text-muted-foreground">
        Configure how you receive notifications in your account settings
      </CardFooter>
    </Card>
  );
};

export default PriceNotification; 