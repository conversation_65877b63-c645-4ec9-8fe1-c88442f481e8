
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { 
  BanknoteIcon, 
  CreditCardIcon, 
  SmartphoneIcon, 
  BuildingIcon,
  PhoneIcon,
  CheckCircle2Icon,
  AlertCircleIcon
} from "lucide-react";

export type PaymentMethodType = 
  | "mtn" 
  | "airtel" 
  | "safaricom" 
  | "visa" 
  | "virtualCard"
  | "bankAccount";

interface PaymentMethod {
  id: PaymentMethodType;
  name: string;
  icon: React.ReactNode;
  description: string;
  availability?: {
    available: boolean;
    message?: string;
  };
}

interface PaymentMethodSelectorProps {
  selectedMethod: PaymentMethodType | null;
  onSelect: (method: PaymentMethodType) => void;
  type: "deposit" | "withdraw";
}

export const PaymentMethodSelector = ({ 
  selectedMethod, 
  onSelect,
  type
}: PaymentMethodSelectorProps) => {
  const depositMethods: PaymentMethod[] = [
    {
      id: "visa",
      name: "Visa Card",
      icon: <CreditCardIcon className="h-5 w-5 text-blue-600" />,
      description: "Pay using Visa credit or debit card",
      availability: {
        available: true
      }
    },
    {
      id: "virtualCard",
      name: "Virtual Card",
      icon: <CreditCardIcon className="h-5 w-5 text-purple-600" />,
      description: "Use virtual card for payment",
      availability: {
        available: true
      }
    },
    {
      id: "mtn",
      name: "MTN Mobile Money",
      icon: <SmartphoneIcon className="h-5 w-5 text-yellow-500" />,
      description: "Fast mobile money transfers",
      availability: {
        available: true
      }
    },
    {
      id: "airtel",
      name: "Airtel Money",
      icon: <SmartphoneIcon className="h-5 w-5 text-red-500" />,
      description: "Secure mobile payments",
      availability: {
        available: true
      }
    },
    {
      id: "safaricom",
      name: "Safaricom",
      icon: <PhoneIcon className="h-5 w-5 text-green-600" />,
      description: "M-Pesa and more",
      availability: {
        available: false,
        message: "Coming soon to Uganda"
      }
    }
  ];

  const withdrawMethods: PaymentMethod[] = [
    {
      id: "bankAccount",
      name: "Bank Account",
      icon: <BuildingIcon className="h-5 w-5 text-blue-600" />,
      description: "Transfer to your bank account",
      availability: {
        available: true
      }
    },
    {
      id: "mtn",
      name: "MTN Mobile Money",
      icon: <SmartphoneIcon className="h-5 w-5 text-yellow-500" />,
      description: "Fast mobile money transfers",
      availability: {
        available: true
      }
    },
    {
      id: "airtel",
      name: "Airtel Money",
      icon: <SmartphoneIcon className="h-5 w-5 text-red-500" />,
      description: "Secure mobile payments",
      availability: {
        available: true
      }
    },
    {
      id: "safaricom",
      name: "Safaricom",
      icon: <PhoneIcon className="h-5 w-5 text-green-600" />,
      description: "M-Pesa and more",
      availability: {
        available: false,
        message: "Coming soon to Uganda"
      }
    }
  ];

  const methods = type === "deposit" ? depositMethods : withdrawMethods;

  return (
    <div className="space-y-4 mt-3">
      <div className="text-sm text-muted-foreground mb-1">
        {type === "deposit" ? "Select how you want to deposit funds" : "Select withdrawal method"}
      </div>
      
      <div className="grid grid-cols-1 gap-3">
        {methods.map((method) => (
          <Card 
            key={method.id}
            className={cn(
              "p-3 transition-all flex items-start relative overflow-hidden",
              method.availability?.available ? "cursor-pointer" : "opacity-70 cursor-not-allowed",
              selectedMethod === method.id 
                ? "border-2 border-primary bg-primary/5" 
                : method.availability?.available ? "hover:border-primary/50" : "",
            )}
            onClick={() => method.availability?.available && onSelect(method.id)}
            role="button"
            tabIndex={method.availability?.available ? 0 : -1}
            aria-pressed={selectedMethod === method.id}
            aria-disabled={!method.availability?.available}
          >
            <div className="mr-3 mt-1">{method.icon}</div>
            <div className="flex-1">
              <div className="font-medium flex items-center">
                {method.name}
                {selectedMethod === method.id && (
                  <CheckCircle2Icon className="h-4 w-4 text-primary ml-2" />
                )}
              </div>
              <div className="text-xs text-muted-foreground">{method.description}</div>
              
              {!method.availability?.available && method.availability?.message && (
                <div className="text-xs flex items-center mt-1 text-orange-500">
                  <AlertCircleIcon className="h-3 w-3 mr-1" />
                  {method.availability.message}
                </div>
              )}
            </div>
            
            {selectedMethod === method.id && (
              <div className="absolute inset-0 pointer-events-none border-2 border-primary rounded-lg" />
            )}
          </Card>
        ))}
      </div>
    </div>
  );
};
