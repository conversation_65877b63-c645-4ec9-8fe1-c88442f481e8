
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { PiggyBank, Coins, FileText, Upload, CheckCircle2, AlertCircle, Clock } from "lucide-react";
import { getServicesByCategory } from '@/data/servicesData';
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import DocumentUploader from "@/components/financing/DocumentUploader";
import LoanTracker from "@/components/financing/LoanTracker";

// Mock loan data
const loanProducts = [
  { id: "loan-01", name: "Seasonal Crop Loan", minAmount: 500000, maxAmount: 5000000, interestRate: 12, termMonths: [3, 6, 9] },
  { id: "loan-02", name: "Equipment Financing", minAmount: 2000000, maxAmount: ********, interestRate: 15, termMonths: [12, 24, 36, 48] },
  { id: "loan-03", name: "Trade Finance", minAmount: 1000000, maxAmount: 10000000, interestRate: 14, termMonths: [1, 3, 6] },
];

// Mock active loans
const activeLoans = [
  { id: "al-101", productId: "loan-01", amount: 2000000, interestRate: 12, dateApproved: "2023-09-15", termMonths: 6, remainingMonths: 4, status: "Active" },
  { id: "al-102", productId: "loan-02", amount: 15000000, interestRate: 15, dateApproved: "2023-07-22", termMonths: 24, remainingMonths: 20, status: "Active" },
];

// Define schema for loan application form
const loanApplicationSchema = z.object({
  loanType: z.string().min(1, "Loan type is required"),
  loanAmount: z.number()
    .min(500000, "Minimum loan amount is 500,000 UGX")
    .max(********, "Maximum loan amount is 20,000,000 UGX"),
  loanTerm: z.string().min(1, "Loan term is required"),
  purpose: z.string().min(1, "Purpose is required"),
  businessName: z.string().min(2, "Business name is required"),
  businessType: z.string().min(1, "Business type is required"),
  yearsOperation: z.string().min(1, "Years in operation is required"),
  annualRevenue: z.number().min(1, "Annual revenue is required"),
  additionalInfo: z.string().optional(),
});

type LoanApplicationFormValues = z.infer<typeof loanApplicationSchema>;

// List of required documents based on loan type
const requiredDocuments = {
  "loan-01": ["National ID", "Proof of Land Ownership/Lease", "Farm Records"],
  "loan-02": ["National ID", "Business Registration", "Equipment Quote", "Collateral Documentation"],
  "loan-03": ["National ID", "Trading License", "Purchase Orders", "Invoices"]
};

// Application statuses for tracking
const applicationStatuses = {
  DRAFT: "draft",
  SUBMITTED: "submitted",
  UNDER_REVIEW: "under_review",
  PENDING_DOCUMENTS: "pending_documents",
  CREDIT_ASSESSMENT: "credit_assessment",
  APPROVED: "approved",
  REJECTED: "rejected",
};

const FinancingServices = () => {
  const [activeTab, setActiveTab] = useState("apply");
  const [loanAmount, setLoanAmount] = useState(1000000);
  const [loanTerm, setLoanTerm] = useState("6");
  const [selectedLoan, setSelectedLoan] = useState(loanProducts[0]);
  const [uploadedDocuments, setUploadedDocuments] = useState<Record<string, File | null>>({});
  const [applicationStatus, setApplicationStatus] = useState(applicationStatuses.DRAFT);
  const [applicationId, setApplicationId] = useState<string | null>(null);
  const financingServices = getServicesByCategory("financing");
  const { toast } = useToast();

  // Initialize form with default values
  const form = useForm<LoanApplicationFormValues>({
    resolver: zodResolver(loanApplicationSchema),
    defaultValues: {
      loanType: selectedLoan.id,
      loanAmount: loanAmount,
      loanTerm: loanTerm,
      purpose: "production",
      businessName: "",
      businessType: "individual",
      yearsOperation: "0-2",
      annualRevenue: 0,
      additionalInfo: "",
    },
  });

  const calculateMonthlyPayment = (principal: number, interestRate: number, termMonths: number) => {
    const monthlyRate = interestRate / 100 / 12;
    return principal * monthlyRate * Math.pow(1 + monthlyRate, termMonths) / (Math.pow(1 + monthlyRate, termMonths) - 1);
  };

  const handleLoanTypeChange = (loanId: string) => {
    const loan = loanProducts.find(l => l.id === loanId) || loanProducts[0];
    setSelectedLoan(loan);
    form.setValue("loanType", loanId);
    form.setValue("loanTerm", loan.termMonths[0].toString());
    setLoanTerm(loan.termMonths[0].toString());
    
    // Reset documents when loan type changes
    setUploadedDocuments({});
  };

  const handleLoanAmountChange = (amount: number) => {
    setLoanAmount(amount);
    form.setValue("loanAmount", amount);
  };

  const handleLoanTermChange = (term: string) => {
    setLoanTerm(term);
    form.setValue("loanTerm", term);
  };

  const handleDocumentUpload = (documentType: string, file: File) => {
    setUploadedDocuments(prev => ({
      ...prev,
      [documentType]: file
    }));
  };

  const areAllRequiredDocumentsUploaded = () => {
    const required = requiredDocuments[selectedLoan.id as keyof typeof requiredDocuments] || [];
    return required.every(doc => !!uploadedDocuments[doc]);
  };

  const submitLoanApplication = (values: LoanApplicationFormValues) => {
    // Validate that all required documents are uploaded
    if (!areAllRequiredDocumentsUploaded()) {
      toast({
        title: "Missing Documents",
        description: "Please upload all required documents before submitting your application.",
        variant: "destructive"
      });
      return;
    }

    // Generate a mock application ID
    const newApplicationId = `APP-${Date.now().toString().slice(-6)}`;
    setApplicationId(newApplicationId);
    
    // In a real app, this would be an API call to submit the application
    // Simulate API call with timeout
    toast({
      title: "Processing",
      description: "Submitting your loan application...",
    });

    setTimeout(() => {
      setApplicationStatus(applicationStatuses.SUBMITTED);
      toast({
        title: "Application Submitted",
        description: `Your loan application #${newApplicationId} has been submitted successfully.`,
      });

      // Simulate moving to review status after a delay
      setTimeout(() => {
        setApplicationStatus(applicationStatuses.UNDER_REVIEW);
        setActiveTab("track");
      }, 2000);
    }, 1500);
  };

  const monthlyPayment = calculateMonthlyPayment(loanAmount, selectedLoan.interestRate, parseInt(loanTerm));

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <PiggyBank className="h-8 w-8 mr-2 text-primary" />
        <h1 className="text-3xl font-bold">Financing Services</h1>
      </div>
      
      <p className="text-muted-foreground mb-8">
        Access affordable financing options tailored for agricultural businesses, from crop production to equipment purchases.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {financingServices.map((service) => (
          <Card key={service.id} className="bg-card hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle>{service.title}</CardTitle>
              <CardDescription>{service.description}</CardDescription>
            </CardHeader>
            <CardFooter>
              <Button variant="outline" onClick={() => {
                setActiveTab("apply");
                if (service.title.includes("Crop")) {
                  handleLoanTypeChange("loan-01");
                } else if (service.title.includes("Equipment")) {
                  handleLoanTypeChange("loan-02");
                } else if (service.title.includes("Trade")) {
                  handleLoanTypeChange("loan-03");
                }
              }}>
                Apply Now
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="apply" className="flex items-center gap-2">
            <FileText className="h-4 w-4" /> Apply for Financing
          </TabsTrigger>
          <TabsTrigger value="track" className="flex items-center gap-2">
            <Clock className="h-4 w-4" /> Track Application
          </TabsTrigger>
          <TabsTrigger value="manage" className="flex items-center gap-2">
            <Coins className="h-4 w-4" /> Manage Active Loans
          </TabsTrigger>
        </TabsList>

        {/* Apply for Financing Tab */}
        <TabsContent value="apply">
          <Card>
            <CardHeader>
              <CardTitle>Loan Application</CardTitle>
              <CardDescription>Apply for agricultural financing based on your needs</CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(submitLoanApplication)} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-6">
                      <FormField
                        control={form.control}
                        name="loanType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Loan Type</FormLabel>
                            <Select 
                              defaultValue={field.value} 
                              onValueChange={(value) => {
                                field.onChange(value);
                                handleLoanTypeChange(value);
                              }}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select loan type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {loanProducts.map(loan => (
                                  <SelectItem key={loan.id} value={loan.id}>{loan.name}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="loanAmount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Loan Amount (UGX)</FormLabel>
                            <FormControl>
                              <div className="space-y-2">
                                <Input 
                                  type="number" 
                                  value={field.value} 
                                  onChange={(e) => {
                                    const value = Number(e.target.value);
                                    field.onChange(value);
                                    handleLoanAmountChange(value);
                                  }}
                                  min={selectedLoan.minAmount}
                                  max={selectedLoan.maxAmount}
                                />
                                <div className="flex justify-between text-xs text-muted-foreground">
                                  <span>Min: {selectedLoan.minAmount.toLocaleString()} UGX</span>
                                  <span>Max: {selectedLoan.maxAmount.toLocaleString()} UGX</span>
                                </div>
                                <Progress value={(loanAmount / selectedLoan.maxAmount) * 100} className="h-2" />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="loanTerm"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Loan Term</FormLabel>
                            <Select 
                              value={field.value} 
                              onValueChange={(value) => {
                                field.onChange(value);
                                handleLoanTermChange(value);
                              }}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select loan term" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {selectedLoan.termMonths.map(term => (
                                  <SelectItem key={term} value={term.toString()}>
                                    {term} {term === 1 ? 'month' : 'months'}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="purpose"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Purpose of Loan</FormLabel>
                            <Select 
                              defaultValue={field.value} 
                              onValueChange={field.onChange}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select purpose" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="production">Crop Production</SelectItem>
                                <SelectItem value="equipment">Equipment Purchase</SelectItem>
                                <SelectItem value="storage">Storage Facility</SelectItem>
                                <SelectItem value="transport">Transportation</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="businessName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Business/Farm Name</FormLabel>
                            <FormControl>
                              <Input {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="businessType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Business Type</FormLabel>
                            <Select 
                              defaultValue={field.value} 
                              onValueChange={field.onChange}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select business type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="individual">Individual Farmer</SelectItem>
                                <SelectItem value="cooperative">Farming Cooperative</SelectItem>
                                <SelectItem value="company">Registered Company</SelectItem>
                                <SelectItem value="partnership">Partnership</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-6">
                      <FormField
                        control={form.control}
                        name="yearsOperation"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Years in Operation</FormLabel>
                            <Select 
                              defaultValue={field.value} 
                              onValueChange={field.onChange}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select years" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="0-2">0-2 years</SelectItem>
                                <SelectItem value="3-5">3-5 years</SelectItem>
                                <SelectItem value="6-10">6-10 years</SelectItem>
                                <SelectItem value="10+">10+ years</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="annualRevenue"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Annual Revenue (UGX)</FormLabel>
                            <FormControl>
                              <Input 
                                type="number"
                                {...field}
                                onChange={e => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="additionalInfo"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Additional Information</FormLabel>
                            <FormControl>
                              <Textarea 
                                placeholder="Any additional information about your business or financing needs"
                                className="h-24"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="space-y-4">
                        <div>
                          <h3 className="text-base font-medium mb-2">Required Documents</h3>
                          <p className="text-sm text-muted-foreground mb-3">
                            Please upload the following documents to complete your application
                          </p>
                          
                          <div className="space-y-4">
                            {(requiredDocuments[selectedLoan.id as keyof typeof requiredDocuments] || []).map(doc => (
                              <DocumentUploader 
                                key={doc}
                                documentType={doc}
                                onFileUpload={handleDocumentUpload}
                                uploadedFile={uploadedDocuments[doc]}
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-muted p-6 rounded-lg space-y-6">
                    <h3 className="text-lg font-medium">Loan Summary</h3>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Loan Type:</span>
                        <span className="font-medium">{selectedLoan.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Principal Amount:</span>
                        <span className="font-medium">{loanAmount.toLocaleString()} UGX</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Interest Rate:</span>
                        <span className="font-medium">{selectedLoan.interestRate}% p.a.</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Term:</span>
                        <span className="font-medium">{loanTerm} months</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Processing Fee:</span>
                        <span className="font-medium">{(loanAmount * 0.02).toLocaleString()} UGX (2%)</span>
                      </div>
                      <div className="border-t pt-2 mt-2 flex justify-between">
                        <span className="font-semibold">Monthly Payment:</span>
                        <span className="font-semibold">{Math.round(monthlyPayment).toLocaleString()} UGX</span>
                      </div>
                      <div className="border-t pt-2 mt-2 flex justify-between">
                        <span className="font-semibold">Total Repayment:</span>
                        <span className="font-semibold">{Math.round(monthlyPayment * parseInt(loanTerm)).toLocaleString()} UGX</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end">
                    <Button 
                      type="submit"
                      className="px-8"
                      disabled={applicationStatus !== applicationStatuses.DRAFT}
                    >
                      Submit Loan Application
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Track Application Tab */}
        <TabsContent value="track">
          <Card>
            <CardHeader>
              <CardTitle>Track Your Application</CardTitle>
              <CardDescription>Monitor the status of your loan application</CardDescription>
            </CardHeader>
            <CardContent>
              {applicationId ? (
                <LoanTracker
                  applicationId={applicationId}
                  status={applicationStatus}
                  loanType={selectedLoan.name}
                  loanAmount={loanAmount}
                  submissionDate={new Date().toLocaleDateString()}
                />
              ) : (
                <div className="text-center py-12">
                  <div className="mx-auto w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
                    <FileText className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No Active Applications</h3>
                  <p className="text-muted-foreground mb-4">You don't have any active loan applications at the moment.</p>
                  <Button onClick={() => setActiveTab("apply")}>Start New Application</Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Manage Active Loans Tab */}
        <TabsContent value="manage">
          <Card>
            <CardHeader>
              <CardTitle>Your Active Loans</CardTitle>
              <CardDescription>Manage and track your current financing</CardDescription>
            </CardHeader>
            <CardContent>
              {activeLoans.length > 0 ? (
                <div className="space-y-6">
                  {activeLoans.map(loan => {
                    const loanProduct = loanProducts.find(product => product.id === loan.productId) || loanProducts[0];
                    const progressPercent = Math.round(((loan.termMonths - loan.remainingMonths) / loan.termMonths) * 100);
                    
                    return (
                      <Card key={loan.id} className="bg-card border">
                        <CardHeader className="pb-2">
                          <div className="flex justify-between items-center">
                            <CardTitle className="text-lg">{loanProduct.name}</CardTitle>
                            <div className="px-2 py-1 rounded bg-primary/10 text-primary text-sm">
                              {loan.status}
                            </div>
                          </div>
                          <CardDescription>Approved on {loan.dateApproved}</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div className="flex justify-between items-center">
                              <span className="font-medium">Loan Amount:</span>
                              <span>{loan.amount.toLocaleString()} UGX</span>
                            </div>
                            
                            <div className="space-y-1">
                              <div className="flex justify-between text-sm">
                                <span>Repayment Progress:</span>
                                <span>{progressPercent}% Complete</span>
                              </div>
                              <Progress value={progressPercent} className="h-2" />
                              <div className="flex justify-between text-xs text-muted-foreground">
                                <span>0%</span>
                                <span>{loan.remainingMonths} months remaining</span>
                                <span>100%</span>
                              </div>
                            </div>
                            
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="text-muted-foreground block">Next Payment:</span>
                                <span className="font-medium">120,000 UGX</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground block">Due Date:</span>
                                <span className="font-medium">May 15, 2024</span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                        <CardFooter className="flex justify-between">
                          <Button variant="outline">View Details</Button>
                          <Button>Make Payment</Button>
                        </CardFooter>
                      </Card>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="mx-auto w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
                    <Coins className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No Active Loans</h3>
                  <p className="text-muted-foreground mb-4">You don't have any active loans at the moment.</p>
                  <Button onClick={() => setActiveTab("apply")}>Apply for Financing</Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FinancingServices;
