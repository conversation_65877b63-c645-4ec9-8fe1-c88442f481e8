
import React from 'react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { FileText } from "lucide-react";

interface InsuranceProductCardProps {
  id: string;
  title: string;
  description: string;
  onLearnMore: (id: string) => void;
}

const InsuranceProductCard = ({ id, title, description, onLearnMore }: InsuranceProductCardProps) => {
  return (
    <Card className="bg-card hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{title}</CardTitle>
          <FileText className="h-5 w-5 text-muted-foreground" />
        </div>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardFooter>
        <Button variant="outline" onClick={() => onLearnMore(id)}>
          Learn More
        </Button>
      </CardFooter>
    </Card>
  );
};

export default InsuranceProductCard;
