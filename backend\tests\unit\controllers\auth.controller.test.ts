import { Request, Response, NextFunction } from 'express';
import { AuthController } from '../../../src/controllers/auth.controller';
import { AuthService } from '../../../src/services/auth.service';
import { EmailService } from '../../../src/services/email.service';
import { PrismaClient } from '@prisma/client';
import { ApiError } from '../../../src/middlewares/errorHandler';

// Mock dependencies
jest.mock('../../../src/services/auth.service');
jest.mock('../../../src/services/email.service');
jest.mock('@prisma/client');

describe('AuthController', () => {
  let authController: AuthController;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: NextFunction;

  beforeEach(() => {
    // Create new instances for each test
    authController = new AuthController();
    
    // Reset mocks and initialize test data
    mockRequest = {
      body: {},
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        role: 'USER'
      }
    };
    
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };
    
    nextFunction = jest.fn();
  });

  describe('login', () => {
    it('should return 401 with invalid credentials', async () => {
      // Arrange
      mockRequest.body = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };
      
      // Setup mock
      const mockPrismaClient = new PrismaClient() as jest.Mocked<PrismaClient>;
      (mockPrismaClient.user.findUnique as jest.Mock).mockResolvedValue(null);
      
      // Mock private property
      (authController as any).prisma = mockPrismaClient;
      
      // Act
      await authController.login(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );
      
      // Assert
      expect(nextFunction).toHaveBeenCalledWith(expect.any(ApiError));
      const error = (nextFunction as jest.Mock).mock.calls[0][0];
      expect(error.statusCode).toBe(401);
      expect(error.message).toBe('Invalid email or password');
    });

    it('should return tokens when login is successful', async () => {
      // Arrange
      mockRequest.body = {
        email: '<EMAIL>',
        password: 'correctpassword'
      };
      
      // Setup mock user
      const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        passwordHash: 'hashedPassword',
        role: 'USER',
        isEmailVerified: true
      };
      
      // Setup mock client
      const mockPrismaClient = new PrismaClient() as jest.Mocked<PrismaClient>;
      (mockPrismaClient.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
      
      // Mock bcrypt.compare to return true (password match)
      const bcrypt = require('bcrypt');
      jest.spyOn(bcrypt, 'compare').mockResolvedValue(true);
      
      // Mock auth service
      const mockAuthService = new AuthService() as jest.Mocked<AuthService>;
      (mockAuthService.generateTokens as jest.Mock).mockReturnValue({
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token'
      });
      
      // Set private properties
      (authController as any).prisma = mockPrismaClient;
      (authController as any).authService = mockAuthService;
      
      // Act
      await authController.login(
        mockRequest as Request,
        mockResponse as Response,
        nextFunction
      );
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith(expect.objectContaining({
        status: 'success',
        message: 'Login successful',
        data: expect.objectContaining({
          tokens: {
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token'
          }
        })
      }));
    });
  });

  // Additional tests would follow the same pattern
}); 