
import { commodities as localCommodities, Commodity } from "@/data/commodityData";
import { toast } from "@/hooks/use-toast";

// Define the API response type
export interface ApiCommodity {
  id: string;
  name: string;
  price: number;
  previous_price: number;
  price_change_percent: number;
  trending: "up" | "down" | "neutral";
  volume: string;
  last_updated: string;
}

// Cache mechanism to prevent excessive API calls
let cachedCommodities: Commodity[] | null = null;
let lastFetchTime: number | null = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Function to map API data to our local Commodity type
const mapApiToCommodity = (apiData: ApiCommodity): Commodity => {
  return {
    id: apiData.id,
    name: apiData.name,
    currentPrice: apiData.price,
    previousPrice: apiData.previous_price,
    priceChange: apiData.price_change_percent,
    trending: apiData.trending,
    color: getCommodityColor(apiData.id),
    data: generateHistoricalData(apiData.price),
    volume: apiData.volume,
    lastUpdated: apiData.last_updated
  };
};

// Helper function to generate color based on commodity ID
const getCommodityColor = (id: string): string => {
  const existingCommodity = localCommodities.find(c => c.id === id);
  if (existingCommodity) return existingCommodity.color;
  
  // Default colors based on commodity types
  if (id.includes('coffee')) return "#8B4513";
  if (id.includes('wheat')) return "#F5DEB3";
  if (id.includes('sun')) return "#FFD700";
  if (id.includes('maize')) return "#F0E68C";
  return "#" + Math.floor(Math.random() * 16777215).toString(16);
};

// Helper function to generate historical data for charting
const generateHistoricalData = (currentPrice: number) => {
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"];
  const volatility = 0.05; // 5% volatility
  
  return months.map((month, index) => {
    // Generate prices with a slight upward trend
    const randomFactor = 1 + (Math.random() * volatility) - (volatility / 2);
    const basePrice = currentPrice * (0.9 + (0.02 * index));
    return { 
      month, 
      price: Math.round(basePrice * randomFactor) 
    };
  });
};

// Function to filter out beans commodities
const filterBeansFromCommodities = (commodities: Commodity[]): Commodity[] => {
  return commodities.filter(commodity => 
    !commodity.id.toLowerCase().includes('bean') && 
    !commodity.name.toLowerCase().includes('bean')
  );
};

// Main function to fetch commodities from API
export const fetchCommodities = async (): Promise<Commodity[]> => {
  // Check cache first
  const now = Date.now();
  if (cachedCommodities && lastFetchTime && now - lastFetchTime < CACHE_DURATION) {
    return cachedCommodities;
  }
  
  try {
    // In a real implementation, this would be an actual API call
    // For now, we'll simulate an API response with a delay
    const response = await simulateApiCall();
    
    // Map API response to our local type
    const mappedCommodities = response.map(mapApiToCommodity);
    
    // Filter out beans
    const filteredCommodities = filterBeansFromCommodities(mappedCommodities);
    
    // Update cache
    cachedCommodities = filteredCommodities;
    lastFetchTime = now;
    
    return filteredCommodities;
  } catch (error) {
    console.error("Failed to fetch commodity data:", error);
    toast({
      title: "Error fetching commodity data",
      description: "Using locally stored data instead",
      variant: "destructive"
    });
    
    // Fallback to local data, filtered for beans
    return filterBeansFromCommodities(localCommodities);
  }
};

// Function to simulate API call with local data
// In a real implementation, this would be replaced with fetch() to a real API
const simulateApiCall = async (): Promise<ApiCommodity[]> => {
  // Add a slight delay to simulate network request
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // 20% chance of simulated error for testing error handling
  if (Math.random() < 0.2) {
    throw new Error("Simulated API error");
  }
  
  // Convert local data to API format and filter out beans
  const filteredCommodities = filterBeansFromCommodities(localCommodities);
  
  return filteredCommodities.map(commodity => ({
    id: commodity.id,
    name: commodity.name,
    price: commodity.currentPrice,
    previous_price: commodity.previousPrice,
    price_change_percent: commodity.priceChange,
    trending: commodity.trending,
    volume: commodity.volume || `${(Math.floor(Math.random() * 5) + 1).toFixed(1)}M`,
    last_updated: commodity.lastUpdated || new Date().toISOString()
  }));
};

// Function to get a specific commodity by ID
export const getCommodityById = async (id: string): Promise<Commodity | undefined> => {
  const commodities = await fetchCommodities();
  return commodities.find(commodity => commodity.id === id);
};

// Function to refresh the cache and fetch new data
export const refreshCommodityData = (): void => {
  lastFetchTime = null;
  cachedCommodities = null;
};
