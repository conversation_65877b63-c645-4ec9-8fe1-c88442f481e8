
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 4%;
    --foreground: 0 0% 98%;

    --card: 240 10% 8%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 8%;
    --popover-foreground: 0 0% 98%;

    --primary: 174 100% 42%;
    --primary-foreground: 240 10% 4%;

    --secondary: 240 10% 12%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 10% 15%;
    --muted-foreground: 240 5% 65%;

    --accent: 36 100% 55%;
    --accent-foreground: 240 10% 4%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 10% 15%;
    --input: 240 10% 15%;
    --ring: 174 100% 42%;

    --radius: 0.5rem;

    --sidebar-background: 240 10% 10%;
    --sidebar-foreground: 240 5% 85%;
    --sidebar-primary: 174 100% 42%;
    --sidebar-primary-foreground: 240 10% 4%;
    --sidebar-accent: 240 10% 15%;
    --sidebar-accent-foreground: 240 5% 85%;
    --sidebar-border: 240 10% 15%;
    --sidebar-ring: 174 100% 42%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--secondary));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground));
  }
}

/* Market-specific utility classes */
.text-up {
  @apply text-green-500;
}

.text-down {
  @apply text-red-500;
}

.text-stable {
  @apply text-yellow-500;
}
