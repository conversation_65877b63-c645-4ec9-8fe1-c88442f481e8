
export type Commodity = {
  id: string;
  name: string;
  currentPrice: number;
  previousPrice: number;
  priceChange: number;
  trending: "up" | "down" | "neutral";
  color: string;
  data: {
    month: string;
    price: number;
  }[];
};

export const commodities: Commodity[] = [
  {
    id: "coffee-arabica",
    name: "Coffee (Arabica)",
    currentPrice: 18000,
    previousPrice: 17730,
    priceChange: 1.5,
    trending: "up",
    color: "#8B4513",
    data: [
      { month: "Jan", price: 16500 },
      { month: "Feb", price: 17000 },
      { month: "Mar", price: 17200 },
      { month: "Apr", price: 17500 },
      { month: "May", price: 17730 },
      { month: "Jun", price: 18000 }
    ]
  },
  {
    id: "coffee-robusta",
    name: "Coffee (Robusta)",
    currentPrice: 14000,
    previousPrice: 13650,
    priceChange: 2.56,
    trending: "up",
    color: "#654321",
    data: [
      { month: "Jan", price: 13200 },
      { month: "Feb", price: 13300 },
      { month: "Mar", price: 13450 },
      { month: "Apr", price: 13500 },
      { month: "May", price: 13650 },
      { month: "Jun", price: 14000 }
    ]
  },
  {
    id: "cocoa",
    name: "Cocoa",
    currentPrice: 27000,
    previousPrice: 27360,
    priceChange: -1.32,
    trending: "down",
    color: "#4B371C",
    data: [
      { month: "Jan", price: 26000 },
      { month: "Feb", price: 26500 },
      { month: "Mar", price: 27200 },
      { month: "Apr", price: 27360 },
      { month: "May", price: 27100 },
      { month: "Jun", price: 27000 }
    ]
  },
  {
    id: "sesame",
    name: "Sesame",
    currentPrice: 5000,
    previousPrice: 4920,
    priceChange: 1.62,
    trending: "up",
    color: "#D2B48C",
    data: [
      { month: "Jan", price: 4600 },
      { month: "Feb", price: 4700 },
      { month: "Mar", price: 4750 },
      { month: "Apr", price: 4800 },
      { month: "May", price: 4920 },
      { month: "Jun", price: 5000 }
    ]
  },
  {
    id: "wheat-pollard",
    name: "Wheat (Pollard)",
    currentPrice: 1000,
    previousPrice: 980,
    priceChange: 2.04,
    trending: "up",
    color: "#F5DEB3",
    data: [
      { month: "Jan", price: 950 },
      { month: "Feb", price: 955 },
      { month: "Mar", price: 960 },
      { month: "Apr", price: 970 },
      { month: "May", price: 980 },
      { month: "Jun", price: 1000 }
    ]
  },
  {
    id: "sunflower",
    name: "Sunflower",
    currentPrice: 1300,
    previousPrice: 1350,
    priceChange: -3.70,
    trending: "down",
    color: "#FFD700",
    data: [
      { month: "Jan", price: 1380 },
      { month: "Feb", price: 1370 },
      { month: "Mar", price: 1360 },
      { month: "Apr", price: 1350 },
      { month: "May", price: 1320 },
      { month: "Jun", price: 1300 }
    ]
  },
  {
    id: "beans-yellow",
    name: "Beans (Yellow)",
    currentPrice: 4450,
    previousPrice: 4300,
    priceChange: 3.49,
    trending: "up",
    color: "#DAA520",
    data: [
      { month: "Jan", price: 4100 },
      { month: "Feb", price: 4150 },
      { month: "Mar", price: 4200 },
      { month: "Apr", price: 4250 },
      { month: "May", price: 4300 },
      { month: "Jun", price: 4450 }
    ]
  },
  {
    id: "beans-kidney",
    name: "Beans (Red Kidney)",
    currentPrice: 2750,
    previousPrice: 2800,
    priceChange: -1.79,
    trending: "down",
    color: "#800000",
    data: [
      { month: "Jan", price: 2900 },
      { month: "Feb", price: 2880 },
      { month: "Mar", price: 2850 },
      { month: "Apr", price: 2820 },
      { month: "May", price: 2800 },
      { month: "Jun", price: 2750 }
    ]
  },
  {
    id: "maize",
    name: "Maize",
    currentPrice: 1200,
    previousPrice: 1250,
    priceChange: -4.00,
    trending: "down",
    color: "#F0E68C",
    data: [
      { month: "Jan", price: 1300 },
      { month: "Feb", price: 1350 },
      { month: "Mar", price: 1300 },
      { month: "Apr", price: 1250 },
      { month: "May", price: 1220 },
      { month: "Jun", price: 1200 }
    ]
  },
  {
    id: "rice",
    name: "Rice",
    currentPrice: 4000,
    previousPrice: 4480,
    priceChange: 2.86,
    trending: "up",
    color: "#FFDAB9",
    data: [
      { month: "Jan", price: 1650 },
      { month: "Feb", price: 1680 },
      { month: "Mar", price: 1700 },
      { month: "Apr", price: 1750 },
      { month: "May", price: 1780 },
      { month: "Jun", price: 1800 }
    ]
  },
  {
    id: "cassava",
    name: "Cassava",
    currentPrice: 950,
    previousPrice: 1000,
    priceChange: -5.00,
    trending: "down",
    color: "#D2B48C",
    data: [
      { month: "Jan", price: 1050 },
      { month: "Feb", price: 1030 },
      { month: "Mar", price: 1020 },
      { month: "Apr", price: 1000 },
      { month: "May", price: 980 },
      { month: "Jun", price: 950 }
    ]
  },
  {
    id: "soybean",
    name: "Soybean",
    currentPrice: 2800,
    previousPrice: 2800,
    priceChange: 0.00,
    trending: "neutral",
    color: "#DEB887",
    data: [
      { month: "Jan", price: 2750 },
      { month: "Feb", price: 2780 },
      { month: "Mar", price: 2800 },
      { month: "Apr", price: 2790 },
      { month: "May", price: 2800 },
      { month: "Jun", price: 2800 }
    ]
  },
  {
    id: "beans-nambaale",
    name: "Beans (Nambaale)",
    currentPrice: 3800,
    previousPrice: 3800,
    priceChange: 0.00,
    trending: "neutral",
    color: "#DEB878",
    data: [
      { month: "Jan", price: 3050 },
      { month: "Feb", price: 3080 },
      { month: "Mar", price: 3800 },
      { month: "Apr", price: 3990 },
      { month: "May", price: 3800 },
      { month: "Jun", price: 3780 }
    ]
  }
];

export default commodities;