
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";

const AboutUs = () => {
  const teamMembers = [
    {
      name: "<PERSON> Asasira <PERSON>",
      title: "Chief Operating Officer",
      bio: "<PERSON> has over 8 years of experience in agricultural economics and founded Fotis Agro with the vision of transforming East African agriculture through technology and accessible financial services.",
      image: "https://picsum.photos/seed/ceo/300/300"
    },
    {
      name: "<PERSON>",
      title: "Chief Executive Officer",
      bio: "With a background in finance and supply chain management and agricultural logistics, <PERSON> oversees day-to-day operations and strategic partnerships across the region.",
      image: "https://picsum.photos/seed/coo/300/300"
    },
    {
      name: "<PERSON>",
      title: "Chief Technology Officer",
      bio: "<PERSON> leads our technical innovations, bringing over 15 years of experience in developing agricultural technology solutions for emerging markets.",
      image: "https://picsum.photos/seed/cto/300/300"
    },
    {
      name: "<PERSON> Naky<PERSON> Ddamulira",
      title: "Head of Agronomy Services",
      bio: "<PERSON> is an expert in sustainable farming practices with a focus on climate-smart agriculture for small and medium-scale farmers.",
      image: "https://picsum.photos/seed/agro/300/300"
    }
  ];

  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-2">About Fotis Agro</h1>
        <p className="text-muted-foreground">Building a sustainable agricultural future for Uganda and East Africa</p>
      </div>

      {/* Hero Image */}
      <div className="relative h-80 mb-12 rounded-lg overflow-hidden">
        <img 
          src="https://picsum.photos/seed/office/1200/400" 
          alt="Fotis Agro Headquarters" 
          className="w-full h-full object-cover"
          loading="lazy"
        />
        <div className="absolute inset-0 bg-black/30 flex items-end">
          <div className="p-6 text-white">
            <h2 className="text-2xl font-bold">Our Headquarters</h2>
            <p>Kampala, Uganda</p>
          </div>
        </div>
      </div>

      {/* Mission, Vision, Values */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <Card className="bg-card">
          <CardContent className="pt-6">
            <h2 className="text-xl font-bold mb-4 text-primary">Our Mission</h2>
            <p>To empower farmers and agricultural businesses across East Africa by providing innovative financial solutions, market access, and technological support that drive sustainable growth and prosperity.</p>
          </CardContent>
        </Card>

        <Card className="bg-card">
          <CardContent className="pt-6">
            <h2 className="text-xl font-bold mb-4 text-primary">Our Vision</h2>
            <p>A thriving, resilient agricultural sector where farmers of all sizes have the resources, knowledge, and market opportunities needed to build sustainable livelihoods and contribute to food security across the region.</p>
          </CardContent>
        </Card>

        <Card className="bg-card">
          <CardContent className="pt-6">
            <h2 className="text-xl font-bold mb-4 text-primary">Our Values</h2>
            <ul className="space-y-2">
              <li className="flex items-start">
                <div className="rounded-full bg-primary/10 p-1 mr-2 mt-1">
                  <svg className="h-3 w-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <span>Innovation in agricultural solutions</span>
              </li>
              <li className="flex items-start">
                <div className="rounded-full bg-primary/10 p-1 mr-2 mt-1">
                  <svg className="h-3 w-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <span>Integrity in all business dealings</span>
              </li>
              <li className="flex items-start">
                <div className="rounded-full bg-primary/10 p-1 mr-2 mt-1">
                  <svg className="h-3 w-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <span>Sustainability in farming practices</span>
              </li>
              <li className="flex items-start">
                <div className="rounded-full bg-primary/10 p-1 mr-2 mt-1">
                  <svg className="h-3 w-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <span>Empowerment of local communities</span>
              </li>
              <li className="flex items-start">
                <div className="rounded-full bg-primary/10 p-1 mr-2 mt-1">
                  <svg className="h-3 w-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <span>Accessibility of financial services</span>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Company History */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Our Story</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <div>
            <p className="mb-4">
              Founded in 2020, Fotis Agro began with a simple mission: to address the critical gaps in agricultural financing and market access for Ugandan farmers. What started as a small team sourcing from farmers and cooperatives, supplying to exporters and aggregators has grown into a comprehensive platform serving thousands of agricultural businesses across East Africa.
            </p>
            <p className="mb-4">
              Our founders and partners witnessed firsthand the challenges faced by smallholder farmers, traders, truck operators and other staker holders in the value-chain. They recognized that access to capital, insuarance, modern farming technologies, and reliable markets were the key barriers preventing stakeholders from achieving their full potential.
            </p>
            <p>
              Today, Fotis Agro combines financial services, technological innovation,insuarance and market connections to create a holistic ecosystem that supports stakeholders at every stage of production. From pre-planting financing to harvest logistics and beyond, we're committed to building a more resilient and profitable agricultural sector for all stakeholders.
            </p>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <img 
              src="https://picsum.photos/seed/farm1/300/300" 
              alt="Fotis Agro early days working with farmers" 
              className="rounded-lg object-cover w-full h-full"
              loading="lazy"
            />
            <img 
              src="https://picsum.photos/seed/farm2/300/300" 
              alt="Fotis Agro community outreach program" 
              className="rounded-lg object-cover w-full h-full"
              loading="lazy"
            />
            <img 
              src="https://picsum.photos/seed/farm3/300/300" 
              alt="Fotis Agro technology implementation" 
              className="rounded-lg object-cover w-full h-full"
              loading="lazy"
            />
            <img 
              src="https://picsum.photos/seed/farm4/300/300" 
              alt="Fotis Agro team in the field" 
              className="rounded-lg object-cover w-full h-full"
              loading="lazy"
            />
          </div>
        </div>
      </div>

      {/* Leadership Team */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Our Leadership Team</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {teamMembers.map((member, index) => (
            <div key={index} className="bg-card border rounded-lg overflow-hidden">
              <img 
                src={member.image} 
                alt={member.name} 
                className="w-full h-48 object-cover"
                loading="lazy"
              />
              <div className="p-4">
                <h3 className="font-bold">{member.name}</h3>
                <p className="text-sm text-primary mb-2">{member.title}</p>
                <p className="text-sm text-muted-foreground">{member.bio}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Impact */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Our Impact</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
          <div className="bg-primary/10 p-8 rounded-lg">
            <div className="text-4xl font-bold text-primary mb-2">15,000+</div>
            <div className="text-lg">Farmers Supported</div>
          </div>
          <div className="bg-primary/10 p-8 rounded-lg">
            <div className="text-4xl font-bold text-primary mb-2">UGX 8B+</div>
            <div className="text-lg">In Financing Provided</div>
          </div>
          <div className="bg-primary/10 p-8 rounded-lg">
            <div className="text-4xl font-bold text-primary mb-2">30%</div>
            <div className="text-lg">Average Yield Increase</div>
          </div>
        </div>
      </div>

      {/* Office Images */}
      <div>
        <h2 className="text-2xl font-bold mb-6">Our Offices</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <img 
            src="https://picsum.photos/seed/office1/600/400" 
            alt="Fotis Agro Kampala headquarters reception" 
            className="rounded-lg object-cover w-full h-64"
            loading="lazy"
          />
          <img 
            src="https://picsum.photos/seed/office2/600/400" 
            alt="Fotis Agro team working environment" 
            className="rounded-lg object-cover w-full h-64"
            loading="lazy"
          />
          <img 
            src="https://picsum.photos/seed/office3/600/400" 
            alt="Fotis Agro innovation center" 
            className="rounded-lg object-cover w-full h-64"
            loading="lazy"
          />
        </div>
      </div>
    </div>
  );
};

export default AboutUs;
