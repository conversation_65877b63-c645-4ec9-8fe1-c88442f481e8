import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { commodities } from "@/data/commodityData";
import { cn } from "@/lib/utils";
import CommodityDetailsModal from "./CommodityDetailsModal";
import { PieChart, Pie, Cell, ResponsiveContainer } from "recharts";

// Updated color palette for commodities
const assetDistribution = [
  { id: "coffee-arabica", name: "Coffee (Arabica)", value: 35, color: "#8B5CF6" },
  { id: "coffee-robusta", name: "Coffee (Robusta)", value: 20, color: "#D946EF" },
  { id: "cocoa", name: "Cocoa", value: 25, color: "#F97316" },
  { id: "soybean", name: "Soybean", value: 12, color: "#0EA5E9" },
  { id: "sunflower", name: "Sunflower", value: 8, color: "#10B981" },
];

// Dummy user portfolio details and data
const holdingsMap: Record<string, { userHoldings: number; tradingVolume: number; recentTrades: any[]; news: string[] }> = {
  "coffee-arabica": {
    userHoldings: 230,
    tradingVolume: 7000,
    recentTrades: [
      { id: "TRD-01", type: "Buy", amount: 90, price: 35500, date: "2025-04-19" },
      { id: "TRD-02", type: "Sell", amount: 50, price: 35300, date: "2025-04-18" },
      { id: "TRD-03", type: "Buy", amount: 40, price: 35400, date: "2025-04-15" },
    ],
    news: [
      "Market sees high demand for Arabica beans.",
      "Price expected to rise following harvest shortages.",
    ]
  },
  "coffee-robusta": {
    userHoldings: 110,
    tradingVolume: 3200,
    recentTrades: [
      { id: "TRD-04", type: "Buy", amount: 40, price: 28800, date: "2025-04-18" },
      { id: "TRD-05", type: "Sell", amount: 30, price: 28650, date: "2025-04-16" },
    ],
    news: [
      "Robusta futures slightly down after rainy season.",
      "Export volumes remain stable in Q2.",
    ]
  },
  "cocoa": {
    userHoldings: 60,
    tradingVolume: 1500,
    recentTrades: [
      { id: "TRD-06", type: "Buy", amount: 20, price: 125000, date: "2025-04-10" },
    ],
    news: [
      "Cocoa prices hit annual peak.",
      "New regional agreements promising stable growth.",
    ]
  },
  "soybean": {
    userHoldings: 400,
    tradingVolume: 5000,
    recentTrades: [
      { id: "TRD-07", type: "Sell", amount: 70, price: 53000, date: "2025-04-12" }
    ],
    news: [
      "Harvest yields top forecasts.",
      "Increased demand from local processors.",
    ]
  },
  "sunflower": {
    userHoldings: 145,
    tradingVolume: 1400,
    recentTrades: [
      { id: "TRD-08", type: "Buy", amount: 50, price: 19600, date: "2025-04-14" }
    ],
    news: [
      "Market expects moderate growth in Q3.",
      "Farmers urged to insure their crops.",
    ]
  }
};

const AssetDistributionCard: React.FC = () => {
  const [selectedCommodityId, setSelectedCommodityId] = useState<string | null>(null);

  // Gather all data for pop-up based on id
  const getCommodityDetail = (id: string) => {
    const meta = assetDistribution.find((d) => d.id === id);
    const commodity = commodities.find((c) => c.id === id);
    if (!meta || !commodity) return null;
    const holdingData = holdingsMap[id];
    return {
      id,
      name: meta.name,
      color: meta.color,
      currentPrice: commodity.currentPrice,
      priceChange: commodity.priceChange,
      userHoldings: holdingData?.userHoldings ?? 0,
      tradingVolume: holdingData?.tradingVolume ?? 0,
      recentTrades: holdingData?.recentTrades ?? [],
      news: holdingData?.news ?? [],
    };
  };

  // Pie chart slice click handler
  const handleSliceClick = (data: any, index: number) => {
    setSelectedCommodityId(data.id);
  };

  // Modal close handler
  const handleModalClose = () => setSelectedCommodityId(null);

  const chartTotal = assetDistribution.reduce((acc, entry) => acc + entry.value, 0);

  return (
    <Card className="h-full">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-medium flex items-center">
          <span className="inline-block w-5 h-5 rounded-full mr-2 bg-gradient-to-br from-[#8B5CF6] via-[#D946EF] to-[#F97316]" />
          Asset Distribution
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="w-full flex flex-col md:flex-row items-center justify-center gap-8">
          <div className="w-96 md:w-80 h-96 md:h-80 flex-shrink-0 relative mx-auto">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={assetDistribution}
                  cx="50%"
                  cy="50%"
                  innerRadius={55}
                  outerRadius={90}
                  paddingAngle={1}
                  dataKey="value"
                  onClick={undefined}
                >
                  {assetDistribution.map((entry, index) => (
                    <Cell
                      key={`cell-${entry.id}`}
                      fill={entry.color}
                      className="cursor-pointer transition-all duration-200"
                      onClick={() => handleSliceClick(entry, index)}
                    />
                  ))}
                </Pie>
              </PieChart>
            </ResponsiveContainer>
            <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
              <span className="text-3xl font-bold">{chartTotal}</span>
              <span className="text-sm text-muted-foreground">Total units</span>
            </div>
          </div>
          <div className="flex flex-col gap-5 w-full max-w-xs">
            {assetDistribution.map((item) => (
              <div
                key={item.id}
                className={cn(
                  "flex items-center cursor-pointer rounded px-2 py-2 hover:bg-muted transition-colors",
                  selectedCommodityId === item.id ? "ring-2 ring-primary" : ""
                )}
                onClick={() => handleSliceClick(item, 0)}
                tabIndex={0}
                aria-label={`Show details for ${item.name}`}
                role="button"
              >
                <span
                  className="inline-block h-4 w-4 rounded-full mr-2"
                  style={{ background: item.color }}
                />
                <span className="font-medium">{item.name}</span>
                <span className="ml-auto text-sm text-muted-foreground">{item.value} units</span>
              </div>
            ))}
          </div>
        </div>
        <CommodityDetailsModal
          open={!!selectedCommodityId}
          onClose={handleModalClose}
          commodity={
            selectedCommodityId
              ? getCommodityDetail(selectedCommodityId)
              : null
          }
        />
      </CardContent>
    </Card>
  );
};

export default AssetDistributionCard;
