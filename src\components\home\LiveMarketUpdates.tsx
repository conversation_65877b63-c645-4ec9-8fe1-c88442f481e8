
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Newspaper } from "lucide-react";
import { useState } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";

// Mock data for market updates with additional entries
const marketUpdates = [
  {
    id: 1,
    title: "Uganda Coffee Exports Rise 20% in March",
    category: "Coffee (Arabica)",
    timestamp: "2 hours ago",
    region: "East Africa",
    impact: "positive"
  },
  {
    id: 2,
    title: "Drought Conditions Affect Cocoa Production in West Africa",
    category: "Cocoa",
    timestamp: "4 hours ago",
    region: "West Africa",
    impact: "negative"
  },
  {
    id: 3,
    title: "New Trade Agreement to Boost Sesame Exports",
    category: "Sesame",
    timestamp: "6 hours ago",
    region: "North Africa",
    impact: "positive"
  },
  {
    id: 4,
    title: "Supply Chain Disruptions Impact Soybean Deliveries",
    category: "Soybean",
    timestamp: "8 hours ago",
    region: "Central Africa",
    impact: "negative"
  },
  {
    id: 5,
    title: "Coffee Bean Prices Surge Amid Labor Shortage",
    category: "Coffee (Arabica)",
    timestamp: "12 hours ago",
    region: "East Africa",
    impact: "positive"
  },
  {
    id: 6,
    title: "New Cocoa Processing Facility Opens in Ghana",
    category: "Cocoa",
    timestamp: "16 hours ago",
    region: "West Africa",
    impact: "positive"
  },
  {
    id: 7,
    title: "Sesame Market Experiences Volatility Due to Weather Concerns",
    category: "Sesame",
    timestamp: "20 hours ago",
    region: "North Africa",
    impact: "negative"
  },
  {
    id: 8,
    title: "Soybean Harvest Forecast Shows 15% Growth",
    category: "Soybean",
    timestamp: "1 day ago",
    region: "Central Africa",
    impact: "positive"
  },
  {
    id: 9,
    title: "Robusta Coffee Production Expected to Increase by 15%",
    category: "Coffee (Robusta)",
    timestamp: "1 day ago",
    region: "East Africa",
    impact: "positive"
  },
  {
    id: 10,
    title: "Wheat Prices Stabilize After Recent Fluctuations",
    category: "Wheat (Pollard)",
    timestamp: "2 days ago",
    region: "East Africa",
    impact: "neutral"
  },
  {
    id: 11,
    title: "Sunflower Oil Exports Face Logistical Challenges",
    category: "Sunflower",
    timestamp: "2 days ago",
    region: "North Africa",
    impact: "negative"
  },
  {
    id: 12,
    title: "Yellow Beans in High Demand for Export Markets",
    category: "Beans (Yellow)",
    timestamp: "3 days ago",
    region: "Central Africa",
    impact: "positive"
  },
  {
    id: 13,
    title: "Kidney Beans Face Price Pressure Due to Increased Supply",
    category: "Beans (Kidney)",
    timestamp: "3 days ago",
    region: "West Africa",
    impact: "negative"
  }
];

const categories = [
  "All", 
  "Coffee (Arabica)", 
  "Coffee (Robusta)", 
  "Cocoa", 
  "Sesame", 
  "Wheat (Pollard)",
  "Sunflower",
  "Beans (Yellow)",
  "Beans (Kidney)",
  "Soybean"
];

const LiveMarketUpdates = () => {
  const [selectedCategory, setSelectedCategory] = useState("All");

  const filteredUpdates = marketUpdates.filter(
    update => selectedCategory === "All" || update.category === selectedCategory
  );

  // Calculate dynamic height based on selected category and number of items
  const getScrollHeight = () => {
    if (selectedCategory === "All") {
      return "500px";
    }
    // For filtered views with fewer items
    return filteredUpdates.length <= 3 ? "300px" : "400px";
  };

  return (
    <Card className="h-full overflow-hidden">
      <CardHeader>
        <CardTitle className="text-lg font-medium flex items-center">
          <Newspaper className="h-5 w-5 mr-2" />
          Live Market Updates
        </CardTitle>
        <div className="flex gap-2 flex-wrap mt-2">
          {categories.map((category) => (
            <Badge
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              className="cursor-pointer hover:bg-primary/90 transition-colors"
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Badge>
          ))}
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[350px]" style={{ height: getScrollHeight() }}>
          <div className="space-y-4">
            {filteredUpdates.map((update) => (
              <div
                key={update.id}
                className="p-3 bg-secondary/50 rounded-md border border-border/50 hover:border-primary/50 transition-colors transform hover:scale-[1.02] hover:shadow-md"
              >
                <div className="flex items-start justify-between gap-2">
                  <h4 className="text-sm font-medium">{update.title}</h4>
                  <Badge
                    variant="outline"
                    className={`shrink-0 ${
                      update.impact === "positive" ? "text-green-500" : update.impact === "negative" ? "text-red-500" : "text-yellow-500"
                    }`}
                  >
                    {update.category}
                  </Badge>
                </div>
                <div className="mt-2 flex items-center justify-between text-xs text-muted-foreground">
                  <span>{update.region}</span>
                  <span>{update.timestamp}</span>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default LiveMarketUpdates;
