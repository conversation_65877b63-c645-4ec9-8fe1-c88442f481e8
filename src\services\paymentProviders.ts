
// Payment provider types and utilities

export enum PaymentProvider {
  BANK_TRANSFER = 'bank_transfer',
  MOBILE_MONEY = 'mobile_money',
  CREDIT_CARD = 'credit_card',
  CRYPTO = 'crypto',
  CASH = 'cash'
}

export interface PaymentMethod {
  id: string;
  provider: PaymentProvider;
  name: string;
  description: string;
  fees: string;
  processingTime: string;
  isActive: boolean;
}

// Sample payment providers data
export const availablePaymentMethods: PaymentMethod[] = [
  {
    id: 'bank-transfer',
    provider: PaymentProvider.BANK_TRANSFER,
    name: 'Bank Transfer',
    description: 'Standard bank transfer to our account',
    fees: '0.5%',
    processingTime: '1-3 business days',
    isActive: true
  },
  {
    id: 'mobile-money',
    provider: PaymentProvider.MOBILE_MONEY,
    name: 'Mobile Money',
    description: 'Transfer using your mobile money account',
    fees: '1%',
    processingTime: 'Instant',
    isActive: true
  },
  {
    id: 'credit-card',
    provider: PaymentProvider.CREDIT_CARD,
    name: 'Credit/Debit Card',
    description: 'Pay with Visa, Mastercard, or other card',
    fees: '2.5%',
    processingTime: 'Instant',
    isActive: true
  },
  {
    id: 'crypto',
    provider: PaymentProvider.CRYPTO,
    name: 'Cryptocurrency',
    description: 'Pay with Bitcoin, Ethereum or other crypto',
    fees: '1%',
    processingTime: '10-60 minutes',
    isActive: false
  },
  {
    id: 'cash',
    provider: PaymentProvider.CASH,
    name: 'Cash Deposit',
    description: 'Visit our offices to make a cash payment',
    fees: 'Free',
    processingTime: 'Same day processing',
    isActive: true
  }
];

// Helper functions for payment processing
export const getPaymentMethodById = (id: string): PaymentMethod | undefined => {
  return availablePaymentMethods.find(method => method.id === id);
};

export const getActivePaymentMethods = (): PaymentMethod[] => {
  return availablePaymentMethods.filter(method => method.isActive);
};

export const calculateFees = (amount: number, provider: PaymentProvider): number => {
  const method = availablePaymentMethods.find(m => m.provider === provider);
  if (!method) return 0;
  
  if (method.fees === 'Free') return 0;
  
  // Parse percentage fee
  const feePercentage = parseFloat(method.fees);
  return (amount * feePercentage) / 100;
};

// Payment processing functions needed by walletService.ts
interface WithdrawalDetails {
  phoneNumber?: string;
  bankName?: string;
  accountNumber?: string;
  accountName?: string;
}

interface PayerDetails {
  phoneNumber?: string;
  email?: string;
  cardDetails?: {
    cardNumber?: string;
    expiryMonth?: string;
    expiryYear?: string;
    cvv?: string;
  };
}

interface ProcessResponse {
  success: boolean;
  transactionId: string;
  message?: string;
  paymentUrl?: string;
}

// Process withdrawal requests
export const processWithdrawal = async (
  paymentMethod: string,
  amount: number,
  recipientDetails: WithdrawalDetails,
  reference: string
): Promise<ProcessResponse> => {
  // In a real app, this would connect to payment provider APIs
  await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
  
  const transactionId = `TX-${Date.now()}-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;
  
  return {
    success: true,
    transactionId,
    message: `Withdrawal of ${amount} initiated successfully via ${paymentMethod}`
  };
};

// Process deposit requests
export const processDeposit = async (
  paymentMethod: string,
  amount: number,
  payerDetails: PayerDetails,
  reference: string
): Promise<ProcessResponse> => {
  // In a real app, this would connect to payment provider APIs
  await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
  
  const transactionId = `TX-${Date.now()}-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;
  
  // For mobile money methods, return a mock payment URL
  const paymentUrl = paymentMethod === 'mtn' || paymentMethod === 'airtel' || paymentMethod === 'safaricom' 
    ? `https://pay.example.com/${reference}` 
    : undefined;
    
  return {
    success: true,
    transactionId,
    message: `Deposit of ${amount} initiated successfully via ${paymentMethod}`,
    paymentUrl
  };
};
