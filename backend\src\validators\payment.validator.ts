import Joi from 'joi';

/**
 * Validation schemas for payment routes
 */
export class PaymentValidation {
  /**
   * Validation schema for creating a Stripe payment intent
   */
  static createStripePaymentIntent = Joi.object({
    body: Joi.object({
      amount: Joi.number().positive().required()
        .messages({
          'number.base': 'Amount must be a number',
          'number.positive': 'Amount must be positive',
          'any.required': 'Amount is required',
        }),
      metadata: Joi.object().optional(),
    }).required(),
  });

  /**
   * Validation schema for Stripe webhook events
   * This is minimal since we trust the incoming webhook from Stripe
   */
  static stripeWebhook = Joi.object({
    body: Joi.object({
      type: Joi.string().required(),
      data: Joi.object({
        object: Joi.object().required(),
      }).required(),
    }).unknown(true).required(),
  });

  /**
   * Validation schema for initiating a mobile money payment
   */
  static initiateMobileMoneyPayment = Joi.object({
    body: Joi.object({
      amount: Joi.number().positive().required()
        .messages({
          'number.base': 'Amount must be a number',
          'number.positive': 'Amount must be positive',
          'any.required': 'Amount is required',
        }),
      phoneNumber: Joi.string().pattern(/^[0-9+]{8,15}$/).required()
        .messages({
          'string.pattern.base': 'Please provide a valid phone number',
          'any.required': 'Phone number is required',
        }),
      provider: Joi.string().valid('mtn', 'airtel').required()
        .messages({
          'any.only': 'Provider must be one of: mtn, airtel',
          'any.required': 'Provider is required',
        }),
      reference: Joi.string().optional(),
      description: Joi.string().optional(),
    }).required(),
  });

  /**
   * Validation schema for checking mobile money payment status
   */
  static checkMobileMoneyStatus = Joi.object({
    params: Joi.object({
      transactionId: Joi.string().required()
        .messages({
          'any.required': 'Transaction ID is required',
        }),
      provider: Joi.string().valid('mtn', 'airtel').required()
        .messages({
          'any.only': 'Provider must be one of: mtn, airtel',
          'any.required': 'Provider is required',
        }),
    }).required(),
  });
} 