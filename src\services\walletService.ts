import { PaymentMethodType } from "@/components/wallet/PaymentMethodSelector";
import { processWithdrawal, processDeposit } from "./paymentProviders";

// Types for the withdrawal request
export interface WithdrawalRequest {
  amount: number;
  paymentMethod: PaymentMethodType;
  recipientDetails: {
    phoneNumber?: string;
    bankName?: string;
    accountNumber?: string;
    accountName?: string;
  };
}

export interface WithdrawalResponse {
  id: string;
  status: "pending" | "processing" | "completed" | "failed";
  amount: number;
  fee: number;
  dateInitiated: string;
  estimatedCompletionTime: string;
  reference: string;
  transactionId?: string;
  providerTransactionId?: string;
  currency?: string;
}

// Status polling interface
export interface WithdrawalStatusResponse {
  id: string;
  status: "pending" | "processing" | "completed" | "failed";
  message?: string;
  transactionId?: string;
}

// Deposit request interface
export interface DepositRequest {
  amount: number;
  paymentMethod: PaymentMethodType;
  payerDetails: {
    phoneNumber?: string;
    email?: string;
    cardDetails?: {
      cardNumber?: string;
      expiryMonth?: string;
      expiryYear?: string;
      cvv?: string;
    };
  };
}

export interface DepositResponse {
  id: string;
  status: "pending" | "processing" | "completed" | "failed";
  amount: number;
  fee: number;
  dateInitiated: string;
  reference: string;
  transactionId?: string;
  paymentUrl?: string;
  currency?: string;
}

export interface TransportPayment {
  amount: number;
  description: string;
  origin: string;
  destination: string;
  referenceNumber: string;
}

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "https://api.fotisagro.com";

// Local storage key for wallet balance
const WALLET_BALANCE_KEY = 'walletBalance';

/**
 * Get the current wallet balance
 */
export const getWalletBalance = (): number => {
  const storedBalance = localStorage.getItem(WALLET_BALANCE_KEY);
  return storedBalance ? parseFloat(storedBalance) : 150000; // Default balance
};

/**
 * Update the wallet balance
 */
export const updateWalletBalance = (newBalance: number): void => {
  localStorage.setItem(WALLET_BALANCE_KEY, newBalance.toString());
  // Dispatch custom event for balance changes
  window.dispatchEvent(new CustomEvent('walletBalanceChanged', { 
    detail: { balance: newBalance } 
  }));
};

/**
 * Make a payment for transportation services
 */
export const makeTransportPayment = async (payment: TransportPayment): Promise<boolean> => {
  const currentBalance = getWalletBalance();
  
  // Check if balance is sufficient
  if (currentBalance < payment.amount) {
    return false;
  }
  
  // Deduct payment from wallet
  const newBalance = currentBalance - payment.amount;
  updateWalletBalance(newBalance);
  
  // In a real app, we would make an API call to record the transaction
  console.log(`Payment processed: ${payment.amount} UGX for transport from ${payment.origin} to ${payment.destination}`);
  
  return true;
};

/**
 * Initiates a withdrawal request
 */
export const initiateWithdrawal = async (
  withdrawalRequest: WithdrawalRequest
): Promise<WithdrawalResponse> => {
  try {
    // Generate a unique reference for this transaction
    const reference = `WD-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
    
    // In a production environment, you would first validate the request with your backend
    // const validationResponse = await fetch(`${API_BASE_URL}/wallet/withdraw/validate`, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${localStorage.getItem('token')}`
    //   },
    //   body: JSON.stringify(withdrawalRequest)
    // });
    // 
    // if (!validationResponse.ok) {
    //   const errorData = await validationResponse.json();
    //   throw new Error(errorData.message || 'Failed to validate withdrawal');
    // }
    
    // Process the withdrawal with the appropriate payment provider
    const providerResponse = await processWithdrawal(
      withdrawalRequest.paymentMethod,
      withdrawalRequest.amount,
      withdrawalRequest.recipientDetails,
      reference
    );
    
    // In production, you would call your backend to record the transaction
    // const recordResponse = await fetch(`${API_BASE_URL}/wallet/withdraw`, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${localStorage.getItem('token')}`
    //   },
    //   body: JSON.stringify({
    //     ...withdrawalRequest,
    //     reference,
    //     providerTransactionId: providerResponse.transactionId
    //   })
    // });
    // const data = await recordResponse.json();
    // if (!recordResponse.ok) throw new Error(data.message || 'Failed to record withdrawal');
    // return data;

    // For demo purposes, create a mock response
    const mockResponse: WithdrawalResponse = {
      id: `WD-${Math.random().toString(36).substr(2, 9)}`,
      status: "pending", // Initial status is always pending
      amount: withdrawalRequest.amount,
      fee: withdrawalRequest.amount * 0.01, // 1% fee
      dateInitiated: new Date().toISOString(),
      estimatedCompletionTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24h later
      reference: reference,
      transactionId: reference,
      providerTransactionId: providerResponse.transactionId,
      currency: "UGX" // Using UGX for Uganda Shillings
    };

    return mockResponse;
  } catch (error) {
    console.error("Error initiating withdrawal:", error);
    throw error;
  }
};

/**
 * Checks the status of a withdrawal with the payment provider
 */
export const checkWithdrawalStatus = async (withdrawalId: string): Promise<WithdrawalStatusResponse> => {
  try {
    // In production, you would call your backend which would check with the payment provider
    // const response = await fetch(`${API_BASE_URL}/wallet/withdraw/${withdrawalId}/status`, {
    //   method: 'GET',
    //   headers: {
    //     'Authorization': `Bearer ${localStorage.getItem('token')}`
    //   }
    // });
    // const data = await response.json();
    // if (!response.ok) throw new Error(data.message || 'Failed to check withdrawal status');
    // return data;

    // For demo purposes, simulate a status check with a more realistic progression
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Simulate status progression - in a real app this would come from the provider
    // Get the last status from localStorage if available
    const storageKey = `withdrawal_status_${withdrawalId}`;
    const savedStatusData = localStorage.getItem(storageKey);
    
    let currentStatus: "pending" | "processing" | "completed" | "failed";
    let count = 0;
    
    if (savedStatusData) {
      const { status, checkCount } = JSON.parse(savedStatusData);
      currentStatus = status;
      count = checkCount + 1;
      
      // Progress the status based on previous checks
      if (currentStatus === "pending" && count >= 2) {
        currentStatus = "processing";
      } else if (currentStatus === "processing" && count >= 4) {
        // 80% chance of success, 20% chance of failure
        currentStatus = Math.random() < 0.8 ? "completed" : "failed";
      }
    } else {
      currentStatus = "pending";
    }
    
    // Save the updated status
    localStorage.setItem(storageKey, JSON.stringify({
      status: currentStatus,
      checkCount: count
    }));
    
    return {
      id: withdrawalId,
      status: currentStatus,
      message: currentStatus === "failed" ? "Payment provider rejected the transaction." : undefined,
      transactionId: withdrawalId
    };
  } catch (error) {
    console.error("Error checking withdrawal status:", error);
    throw error;
  }
};

/**
 * Gets withdrawal history for the user
 */
export const getWithdrawalHistory = async (
  page = 1, 
  limit = 10
): Promise<{ withdrawals: WithdrawalResponse[], total: number }> => {
  try {
    // In production, you would call your backend to get the real history
    // const response = await fetch(`${API_BASE_URL}/wallet/withdraw/history?page=${page}&limit=${limit}`, {
    //   method: 'GET',
    //   headers: {
    //     'Authorization': `Bearer ${localStorage.getItem('token')}`
    //   }
    // });
    // const data = await response.json();
    // if (!response.ok) throw new Error(data.message || 'Failed to fetch withdrawal history');
    // return data;

    // Mock API delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Mock data
    const mockWithdrawals: WithdrawalResponse[] = [];
    const statuses: ("pending" | "processing" | "completed" | "failed")[] = [
      "pending", "processing", "completed", "completed", "completed", "failed"
    ];

    for (let i = 0; i < limit; i++) {
      const mockDate = new Date();
      mockDate.setDate(mockDate.getDate() - i);
      const amount = Math.floor(Math.random() * 1000) + 100;

      mockWithdrawals.push({
        id: `WD-${Math.random().toString(36).substr(2, 9)}`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        amount: amount,
        fee: amount * 0.01,
        dateInitiated: mockDate.toISOString(),
        estimatedCompletionTime: new Date(mockDate.getTime() + 24 * 60 * 60 * 1000).toISOString(),
        reference: `REF-${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
        currency: "UGX"
      });
    }

    return {
      withdrawals: mockWithdrawals,
      total: 25
    };
  } catch (error) {
    console.error("Error fetching withdrawal history:", error);
    throw error;
  }
};

/**
 * Initiates a deposit request
 */
export const initiateDeposit = async (
  depositRequest: DepositRequest
): Promise<DepositResponse> => {
  try {
    // Generate a unique reference for this transaction
    const reference = `DEP-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
    
    // Process the deposit with the appropriate payment provider
    const providerResponse = await processDeposit(
      depositRequest.paymentMethod,
      depositRequest.amount,
      depositRequest.payerDetails,
      reference
    );
    
    // In production, you would call your backend to record the transaction
    // const recordResponse = await fetch(`${API_BASE_URL}/wallet/deposit`, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${localStorage.getItem('token')}`
    //   },
    //   body: JSON.stringify({
    //     ...depositRequest,
    //     reference,
    //     providerTransactionId: providerResponse.transactionId
    //   })
    // });
    // const data = await recordResponse.json();
    // if (!recordResponse.ok) throw new Error(data.message || 'Failed to record deposit');
    // return data;

    // For demo purposes, create a mock response
    const mockResponse: DepositResponse = {
      id: `DEP-${Math.random().toString(36).substr(2, 9)}`,
      status: "pending", // Initial status is always pending
      amount: depositRequest.amount,
      fee: 0, // No fee for deposits
      dateInitiated: new Date().toISOString(),
      reference: reference,
      transactionId: reference,
      paymentUrl: providerResponse.paymentUrl,
      currency: "UGX" // Using UGX for Uganda Shillings
    };

    return mockResponse;
  } catch (error) {
    console.error("Error initiating deposit:", error);
    throw error;
  }
};

/**
 * Checks the status of a deposit
 */
export const checkDepositStatus = async (depositId: string): Promise<{ 
  id: string;
  status: "pending" | "processing" | "completed" | "failed";
  message?: string;
}> => {
  try {
    // In production, you would check with your backend which would then check with the payment provider
    // Simulate API call with mock progression logic similar to withdrawals
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const storageKey = `deposit_status_${depositId}`;
    const savedStatusData = localStorage.getItem(storageKey);
    
    let currentStatus: "pending" | "processing" | "completed" | "failed";
    let count = 0;
    
    if (savedStatusData) {
      const { status, checkCount } = JSON.parse(savedStatusData);
      currentStatus = status;
      count = checkCount + 1;
      
      if (currentStatus === "pending" && count >= 2) {
        currentStatus = "processing";
      } else if (currentStatus === "processing" && count >= 3) {
        // 90% chance of success for deposits
        currentStatus = Math.random() < 0.9 ? "completed" : "failed";
      }
    } else {
      currentStatus = "pending";
    }
    
    localStorage.setItem(storageKey, JSON.stringify({
      status: currentStatus,
      checkCount: count
    }));
    
    return {
      id: depositId,
      status: currentStatus,
      message: currentStatus === "failed" ? "Payment was not completed." : undefined
    };
  } catch (error) {
    console.error("Error checking deposit status:", error);
    throw error;
  }
};

/**
 * Gets deposit history for the user
 */
export const getDepositHistory = async (
  page = 1, 
  limit = 10
): Promise<{ deposits: DepositResponse[], total: number }> => {
  try {
    // In production, you would call your backend
    // Mock implementation similar to withdrawal history
    await new Promise(resolve => setTimeout(resolve, 1500));

    const mockDeposits: DepositResponse[] = [];
    const statuses: ("pending" | "processing" | "completed" | "failed")[] = [
      "pending", "processing", "completed", "completed", "completed", "failed"
    ];

    for (let i = 0; i < limit; i++) {
      const mockDate = new Date();
      mockDate.setDate(mockDate.getDate() - i);
      const amount = Math.floor(Math.random() * 2000) + 500;

      mockDeposits.push({
        id: `DEP-${Math.random().toString(36).substr(2, 9)}`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        amount: amount,
        fee: 0, // No fees for deposits
        dateInitiated: mockDate.toISOString(),
        reference: `DEP-REF-${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
        currency: "UGX"
      });
    }

    return {
      deposits: mockDeposits,
      total: 30
    };
  } catch (error) {
    console.error("Error fetching deposit history:", error);
    throw error;
  }
};
