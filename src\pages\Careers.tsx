
import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";

type JobPosition = {
  id: string;
  title: string;
  department: string;
  location: string;
  type: "Full-time" | "Part-time" | "Contract";
  description: string;
  requirements: string[];
  responsibilities: string[];
  posted: string;
};

const jobOpenings: JobPosition[] = [
  {
    id: "job-001",
    title: "Senior Agronomist",
    department: "Agronomy Services",
    location: "Kampala, Uganda",
    type: "Full-time",
    description: "We're looking for an experienced Senior Agronomist to provide expert guidance to farmers and lead our agricultural advisory services across Uganda.",
    requirements: [
      "Master's degree in Agronomy, Agricultural Sciences, or related field",
      "Minimum 7 years of experience in agricultural consulting or research",
      "Deep understanding of East African farming systems and crops",
      "Experience in implementing sustainable farming practices",
      "Strong communication skills and ability to translate technical knowledge for farmers",
      "Valid driver's license and willingness to travel to rural areas regularly"
    ],
    responsibilities: [
      "Develop and implement agricultural advisory programs for client farmers",
      "Conduct soil analysis and provide recommendations for soil health improvement",
      "Design crop rotation plans and integrated pest management strategies",
      "Train farmers and agricultural extension workers on best farming practices",
      "Document case studies and success stories from the field",
      "Collaborate with the technology team to enhance our digital farming tools"
    ],
    posted: "2025-05-01"
  },
  {
    id: "job-002",
    title: "Agricultural Finance Officer",
    department: "Financing Services",
    location: "Kampala, Uganda",
    type: "Full-time",
    description: "Join our financing team to help evaluate and process agricultural loan applications, ensuring farmers get the capital they need while maintaining strong portfolio performance.",
    requirements: [
      "Bachelor's degree in Finance, Economics, Agricultural Economics, or related field",
      "3+ years experience in credit analysis, preferably in agricultural or rural finance",
      "Understanding of farming cycles and agricultural business models",
      "Strong analytical skills and attention to detail",
      "Proficiency in financial modeling and risk assessment",
      "Experience with loan management software"
    ],
    responsibilities: [
      "Evaluate loan applications from farmers and agricultural businesses",
      "Conduct financial analysis and risk assessments of potential borrowers",
      "Structure appropriate financing solutions based on farming cycles and cash flows",
      "Monitor loan performance and maintain client relationships",
      "Collaborate with the field team to verify agricultural operations",
      "Prepare reports on portfolio performance and risk metrics"
    ],
    posted: "2025-04-25"
  },
  {
    id: "job-003",
    title: "Agricultural Technology Developer",
    department: "Technology",
    location: "Kampala, Uganda",
    type: "Full-time",
    description: "Help build the next generation of digital tools for farmers and agricultural businesses across East Africa.",
    requirements: [
      "Bachelor's degree in Computer Science, Software Engineering, or related field",
      "3+ years of experience in full-stack web or mobile application development",
      "Proficiency in JavaScript/TypeScript, React, and Node.js",
      "Experience with database design and management",
      "Knowledge of geospatial data and mapping technologies is a plus",
      "Understanding of agricultural systems or willingness to learn"
    ],
    responsibilities: [
      "Develop and maintain web and mobile applications for farmers and internal teams",
      "Design and implement features for farm management, market access, and financial services",
      "Integrate external data sources like weather APIs and satellite imagery",
      "Collaborate with the product team to translate user needs into technical solutions",
      "Optimize applications for offline use in rural areas with limited connectivity",
      "Implement data security protocols and ensure compliance with regulations"
    ],
    posted: "2025-05-03"
  },
  {
    id: "job-004",
    title: "Logistics Coordinator",
    department: "Transportation Services",
    location: "Kampala, Uganda",
    type: "Full-time",
    description: "Coordinate transportation and logistics operations to ensure efficient movement of agricultural products from farms to markets.",
    requirements: [
      "Bachelor's degree in Logistics, Supply Chain Management, or related field",
      "3+ years experience in logistics or transportation management",
      "Strong organizational and problem-solving skills",
      "Experience with logistics planning software",
      "Understanding of agricultural supply chains in East Africa",
      "Valid driver's license"
    ],
    responsibilities: [
      "Coordinate transportation schedules for agricultural products",
      "Optimize routes and logistics operations for efficiency",
      "Manage relationships with transportation service providers",
      "Track shipments and ensure timely delivery",
      "Handle documentation and compliance requirements",
      "Resolve logistics issues and implement continuous improvements"
    ],
    posted: "2025-04-10"
  }
];

const Careers = () => {
  const [selectedJob, setSelectedJob] = useState<JobPosition | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [applicationForm, setApplicationForm] = useState({
    fullName: "",
    email: "",
    phone: "",
    resume: null as File | null,
    coverLetter: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleApply = (job: JobPosition) => {
    setSelectedJob(job);
    setIsDialogOpen(true);
    // Reset form
    setApplicationForm({
      fullName: "",
      email: "",
      phone: "",
      resume: null,
      coverLetter: "",
    });
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setApplicationForm({
        ...applicationForm,
        resume: e.target.files[0]
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target;
    setApplicationForm({
      ...applicationForm,
      [id]: value,
    });
  };

  const handleSubmitApplication = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      setIsDialogOpen(false);
      toast({
        title: "Application Submitted",
        description: `Thank you for applying to the ${selectedJob?.title} position. We'll review your application and contact you soon.`,
      });
    }, 1500);
  };

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const calculateDaysAgo = (dateString: string) => {
    const postedDate = new Date(dateString);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - postedDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const benefitsList = [
    { title: "Competitive Salary", description: "Market-leading compensation packages for all positions" },
    { title: "Health Insurance", description: "Comprehensive medical coverage for you and your dependents" },
    { title: "Professional Development", description: "Regular training opportunities and career growth paths" },
    { title: "Flexible Work Options", description: "Remote work opportunities and flexible scheduling" },
    { title: "Retirement Benefits", description: "Company-matched retirement savings plan" },
    { title: "Paid Time Off", description: "Generous vacation and sick leave policies" },
    { title: "Agriculture Education", description: "Learn about modern farming and sustainable agriculture" },
    { title: "Community Impact", description: "Be part of transforming East African agriculture" },
  ];
  
  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-2">Careers at Fotis Agro</h1>
        <p className="text-muted-foreground">Join our team in transforming East African agriculture</p>
      </div>

      {/* Hero Banner */}
      <div className="relative h-80 mb-12 rounded-lg overflow-hidden">
        <img 
          src="https://picsum.photos/seed/team/1200/400" 
          alt="Fotis Agro team working together" 
          className="w-full h-full object-cover"
          loading="lazy"
        />
        <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
          <div className="text-center text-white p-6 max-w-xl">
            <h2 className="text-3xl font-bold mb-2">Make an Impact</h2>
            <p className="text-lg">Join our mission to empower farmers and transform agriculture across East Africa</p>
          </div>
        </div>
      </div>

      <Tabs defaultValue="openings" className="mb-12">
        <TabsList className="grid w-full grid-cols-3 mb-8">
          <TabsTrigger value="openings">Job Openings</TabsTrigger>
          <TabsTrigger value="benefits">Benefits & Culture</TabsTrigger>
          <TabsTrigger value="environment">Work Environment</TabsTrigger>
        </TabsList>

        {/* Job Openings Tab */}
        <TabsContent value="openings" className="space-y-6">
          <div className="grid grid-cols-1 gap-6">
            {jobOpenings.map((job) => (
              <Card key={job.id} className="overflow-hidden border-l-4 border-l-primary">
                <div className="grid md:grid-cols-4">
                  <div className="md:col-span-3 p-6">
                    <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                      <h3 className="text-xl font-bold">{job.title}</h3>
                      <div className="flex items-center space-x-2 mt-2 md:mt-0">
                        <Badge type={job.type} />
                        <span className="text-sm text-muted-foreground">Posted {calculateDaysAgo(job.posted)} days ago</span>
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-4 mb-4">
                      <div className="flex items-center">
                        <svg className="h-4 w-4 mr-1 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <span>{job.department}</span>
                      </div>
                      <div className="flex items-center">
                        <svg className="h-4 w-4 mr-1 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        <span>{job.location}</span>
                      </div>
                    </div>
                    <p className="text-muted-foreground mb-4">{job.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <h4 className="font-semibold mb-2">Requirements</h4>
                        <ul className="list-disc pl-5 space-y-1 text-sm">
                          {job.requirements.slice(0, 3).map((req, index) => (
                            <li key={index}>{req}</li>
                          ))}
                          {job.requirements.length > 3 && (
                            <li className="text-primary cursor-pointer" onClick={() => handleApply(job)}>
                              +{job.requirements.length - 3} more...
                            </li>
                          )}
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2">Responsibilities</h4>
                        <ul className="list-disc pl-5 space-y-1 text-sm">
                          {job.responsibilities.slice(0, 3).map((resp, index) => (
                            <li key={index}>{resp}</li>
                          ))}
                          {job.responsibilities.length > 3 && (
                            <li className="text-primary cursor-pointer" onClick={() => handleApply(job)}>
                              +{job.responsibilities.length - 3} more...
                            </li>
                          )}
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div className="border-t md:border-t-0 md:border-l border-border flex items-center justify-center p-6">
                    <Button onClick={() => handleApply(job)} className="w-full">Apply Now</Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Benefits & Culture Tab */}
        <TabsContent value="benefits">
          <div className="grid grid-cols-1 gap-12">
            <div>
              <h2 className="text-2xl font-bold mb-6">Why Join Fotis Agro?</h2>
              <p className="mb-6">
                At Fotis Agro, we believe that our team is our greatest asset. We're building a diverse, passionate workforce dedicated to transforming agriculture across East Africa. When you join our team, you become part of a mission to empower farmers and create sustainable food systems for generations to come.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-primary/10 p-6 rounded-lg text-center">
                  <div className="text-3xl font-bold text-primary mb-2">Purpose-Driven</div>
                  <p>Make a tangible impact on food security and farmer livelihoods across East Africa</p>
                </div>
                <div className="bg-primary/10 p-6 rounded-lg text-center">
                  <div className="text-3xl font-bold text-primary mb-2">Innovation</div>
                  <p>Work at the cutting edge of agri-tech and financial innovation in emerging markets</p>
                </div>
                <div className="bg-primary/10 p-6 rounded-lg text-center">
                  <div className="text-3xl font-bold text-primary mb-2">Growth</div>
                  <p>Develop your career in a rapidly expanding organization with diverse opportunities</p>
                </div>
              </div>
            </div>
            
            <div>
              <h2 className="text-2xl font-bold mb-6">Benefits & Perks</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
                {benefitsList.map((benefit, index) => (
                  <Card key={index} className="h-full">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">{benefit.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">{benefit.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
            
            <div>
              <h2 className="text-2xl font-bold mb-6">Our Culture</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <p className="mb-4">
                    Our workplace culture is built on collaboration, innovation, and impact. We believe in creating an environment where diverse perspectives are valued and where everyone has the opportunity to contribute meaningfully to our mission.
                  </p>
                  <p>
                    We celebrate achievements together, learn from challenges, and constantly seek ways to improve both as individuals and as an organization. Regular team building activities, knowledge sharing sessions, and community engagement initiatives help foster a sense of belonging and purpose among our team members.
                  </p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <img 
                    src="https://picsum.photos/seed/culture1/400/400" 
                    alt="Team collaboration session" 
                    className="rounded-lg object-cover w-full h-full"
                    loading="lazy"
                  />
                  <img 
                    src="https://picsum.photos/seed/culture2/400/400" 
                    alt="Company retreat" 
                    className="rounded-lg object-cover w-full h-full"
                    loading="lazy"
                  />
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        {/* Work Environment Tab */}
        <TabsContent value="environment">
          <div className="grid grid-cols-1 gap-12">
            <div>
              <h2 className="text-2xl font-bold mb-6">Our Workplace</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="md:col-span-2">
                  <p className="mb-4">
                    Our headquarters in Kampala features modern workspaces designed to foster collaboration and innovation. With bright, open areas for teamwork as well as quiet zones for focused tasks, our office environment supports various working styles and needs.
                  </p>
                  <p className="mb-4">
                    We also embrace flexible working arrangements, understanding that talent isn't confined to a single location. Many roles offer options for remote work or hybrid schedules, allowing team members to balance productivity with personal wellbeing.
                  </p>
                  <p>
                    Regular field visits connect our office-based team members with the agricultural communities we serve, ensuring everyone stays grounded in our mission and understands the real-world impact of our work.
                  </p>
                </div>
                <div>
                  <img 
                    src="https://picsum.photos/seed/workplace/600/800" 
                    alt="Fotis Agro office environment" 
                    className="rounded-lg w-full h-auto"
                    loading="lazy"
                  />
                </div>
              </div>
            </div>
            
            <div>
              <h2 className="text-2xl font-bold mb-6">A Day at Fotis Agro</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center mb-2">
                      <svg className="h-6 w-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                    <CardTitle>Flexible Scheduling</CardTitle>
                    <CardDescription>Core hours with flexibility around your life</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      We maintain core working hours when teams can collaborate, but offer flexibility around these hours to accommodate personal schedules and peak productivity times.
                    </p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center mb-2">
                      <svg className="h-6 w-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                      </svg>
                    </div>
                    <CardTitle>Collaborative Teams</CardTitle>
                    <CardDescription>Cross-functional work on impactful projects</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      Our project teams bring together expertise across departments, allowing you to collaborate with colleagues from different backgrounds and learn new perspectives.
                    </p>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center mb-2">
                      <svg className="h-6 w-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                      </svg>
                    </div>
                    <CardTitle>Innovation Time</CardTitle>
                    <CardDescription>Dedicated hours for creative solutions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      We dedicate time for innovation and creative thinking, encouraging team members to explore new ideas that could improve our services or internal processes.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h2 className="text-2xl font-bold mb-6">Learning & Development</h2>
                <p className="mb-4">
                  We're committed to helping our team members grow professionally. Through a combination of structured training programs, conference attendance, online learning resources, and mentorship opportunities, we support continuous skill development and career advancement.
                </p>
                <p>
                  Each team member works with their manager to create a personalized development plan aligned with both individual career goals and organizational needs.
                </p>
              </div>
              
              <div>
                <h2 className="text-2xl font-bold mb-6">Community Engagement</h2>
                <p className="mb-4">
                  Giving back is an integral part of our company culture. We organize regular volunteer opportunities with local farming communities and agricultural education programs.
                </p>
                <p>
                  Team members are encouraged to participate in community service initiatives, with dedicated paid volunteer time available annually.
                </p>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Application Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <form onSubmit={handleSubmitApplication}>
            <DialogHeader>
              <DialogTitle>Apply for {selectedJob?.title}</DialogTitle>
              <DialogDescription>
                Complete the form below to submit your application. Fields marked with * are required.
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="fullName" className="required">Full Name *</Label>
                <Input
                  id="fullName"
                  value={applicationForm.fullName}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="email" className="required">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={applicationForm.email}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="phone" className="required">Phone Number *</Label>
                <Input
                  id="phone"
                  value={applicationForm.phone}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="resume" className="required">Resume/CV (PDF format) *</Label>
                <Input
                  id="resume"
                  type="file"
                  accept=".pdf"
                  onChange={handleFileChange}
                  required
                />
              </div>
              
              <div className="grid grid-cols-1 gap-2">
                <Label htmlFor="coverLetter">Cover Letter</Label>
                <Textarea
                  id="coverLetter"
                  placeholder="Tell us why you're interested in this position and what you bring to the role"
                  value={applicationForm.coverLetter}
                  onChange={handleInputChange}
                  rows={5}
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Submitting..." : "Submit Application"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

// Badge Component
const Badge = ({ type }: { type: "Full-time" | "Part-time" | "Contract" }) => {
  let bgColor;
  
  switch (type) {
    case "Full-time":
      bgColor = "bg-green-100 text-green-800";
      break;
    case "Part-time":
      bgColor = "bg-blue-100 text-blue-800";
      break;
    case "Contract":
      bgColor = "bg-orange-100 text-orange-800";
      break;
    default:
      bgColor = "bg-gray-100 text-gray-800";
  }
  
  return (
    <span className={`px-2 py-1 rounded text-xs font-medium ${bgColor}`}>
      {type}
    </span>
  );
};

export default Careers;
