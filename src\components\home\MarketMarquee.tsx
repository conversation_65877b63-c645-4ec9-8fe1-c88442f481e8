
import { ArrowDownRight, <PERSON>RightIcon, ArrowUpR<PERSON>, HelpCircle } from "lucide-react";
import { useEffect, useState, useRef, useCallback } from "react";
import { Commodity } from "@/data/commodityData";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { fetchCommodities } from "@/services/commoditiesService";

export const MarketMarquee = () => {
  const [scrollPosition, setScrollPosition] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [commodityData, setCommodityData] = useState<Commodity[]>([]);
  const [lastUpdateTime, setLastUpdateTime] = useState(new Date());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Function to load commodity data
  const loadCommodityData = useCallback(async () => {
    try {
      setIsLoading(true);
      const data = await fetchCommodities();
      setCommodityData(data);
      setLastUpdateTime(new Date());
      setError(null);
    } catch (err) {
      console.error("Error loading commodity data:", err);
      setError("Failed to load market data");
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Effect for initial data loading and periodic refresh
  useEffect(() => {
    // Load data initially
    loadCommodityData();
    
    // Set up refresh interval
    const refreshInterval = setInterval(() => {
      loadCommodityData();
    }, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(refreshInterval);
  }, [loadCommodityData]);

  // Effect for scrolling animation
  useEffect(() => {
    let animationId: number;
    const scrollSpeed = 1; // Pixels per frame

    const animate = () => {
      if (!isPaused && scrollRef.current) {
        setScrollPosition(prevPosition => {
          // Reset position when scrolled beyond content width
          const contentWidth = scrollRef.current?.scrollWidth || 3000;
          const containerWidth = scrollRef.current?.clientWidth || 1000;
          
          if (prevPosition < -(contentWidth / 2)) {
            return 0;
          }
          return prevPosition - scrollSpeed;
        });
      }
      animationId = requestAnimationFrame(animate);
    };

    animationId = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationId);
  }, [isPaused]);

  // Format timestamp for better readability
  const formatUpdateTime = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleTimeString();
  };

  return (
    <div className="bg-card shadow-md border-y border-border py-3 overflow-hidden relative w-[95%] mx-auto rounded-xl my-2 transform-gpu">
      {/* 3D styling with perspective and shadow */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent rounded-xl shadow-lg transform-gpu perspective-1000"></div>
      
      {/* Latest update timestamp */}
      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-card/90 backdrop-blur-sm px-3 py-1 rounded-full border border-border flex items-center">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <div className="flex items-center text-xs text-muted-foreground">
                <div className={`h-2 w-2 rounded-full mr-2 ${isLoading ? 'bg-yellow-400' : error ? 'bg-red-500' : 'bg-[#8BC34A] animate-pulse'}`}></div>
                <span>{isLoading ? "Updating" : error ? "Error" : "Live"}</span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">
                {error 
                  ? "Failed to load latest data" 
                  : `Last updated: ${lastUpdateTime.toLocaleTimeString()}`}
              </p>
              {error && (
                <button 
                  onClick={() => loadCommodityData()} 
                  className="text-xs text-primary hover:underline mt-1"
                >
                  Try again
                </button>
              )}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      {/* Main ticker content */}
      <div 
        ref={scrollRef}
        className="whitespace-nowrap transition-transform duration-200 ml-16"
        style={{ transform: `translateX(${scrollPosition}px)` }}
        onMouseEnter={() => setIsPaused(true)}
        onMouseLeave={() => setIsPaused(false)}
      >
        {isLoading && commodityData.length === 0 ? (
          <div className="inline-flex items-center px-6">
            <span className="text-muted-foreground">Loading market data...</span>
          </div>
        ) : error && commodityData.length === 0 ? (
          <div className="inline-flex items-center px-6">
            <span className="text-destructive">{error}</span>
          </div>
        ) : (
          <div className="inline-flex items-center">
            {commodityData.map((item, index) => (
              <TooltipProvider key={`${item.id}-${index}`}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div 
                      className="inline-flex items-center mx-6 hover:bg-card/80 rounded-full px-4 py-2 transition-colors border border-transparent hover:border-border transform hover:scale-105 hover:shadow-md relative overflow-hidden cursor-pointer"
                      style={{
                        transform: 'translateZ(0)', // 3D transform for performance
                      }}
                    >
                      <div className="flex flex-col mr-3">
                        <span className="font-medium">{item.name}</span>
                        <span className="text-xs text-muted-foreground">UGX/kg</span>
                      </div>

                      <div className="flex flex-col items-end">
                        <span className="font-bold tabular-nums">{item.currentPrice.toLocaleString()}</span>
                        <div 
                          className={`flex items-center text-xs ${
                            item.priceChange > 0 
                              ? "text-[#8BC34A]" 
                              : item.priceChange < 0 
                              ? "text-red-500" 
                              : "text-yellow-500"
                          }`}
                        >
                          {item.priceChange > 0 ? (
                            <ArrowUpRight className="h-3 w-3 mr-1" />
                          ) : item.priceChange < 0 ? (
                            <ArrowDownRight className="h-3 w-3 mr-1" />
                          ) : (
                            <ArrowRightIcon className="h-3 w-3 mr-1" />
                          )}
                          <span className="tabular-nums">
                            {item.priceChange > 0 ? "+" : ""}{item.priceChange}%
                          </span>
                        </div>
                      </div>

                      {/* Price flash animation */}
                      {item.trending === "up" && (
                        <div className="absolute inset-0 bg-[#8BC34A]/10 rounded-full animate-flash pointer-events-none"></div>
                      )}
                      {item.trending === "down" && (
                        <div className="absolute inset-0 bg-red-500/10 rounded-full animate-flash pointer-events-none"></div>
                      )}
                      
                      {/* 3D lighting effect */}
                      <div className="absolute inset-0 bg-gradient-to-tr from-transparent to-white/10 pointer-events-none"></div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="w-56 p-0">
                    <div className="p-3">
                      <h4 className="font-medium mb-1">{item.name}</h4>
                      <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs">
                        <span className="text-muted-foreground">Current:</span>
                        <span className="font-medium">{item.currentPrice.toLocaleString()} UGX</span>
                        
                        <span className="text-muted-foreground">Previous:</span>
                        <span className="font-medium">{item.previousPrice.toLocaleString()} UGX</span>
                        
                        <span className="text-muted-foreground">Change:</span>
                        <span className={`font-medium ${
                          item.priceChange > 0 
                            ? "text-[#8BC34A]" 
                            : item.priceChange < 0 
                            ? "text-red-500" 
                            : ""
                        }`}>
                          {item.priceChange > 0 ? "+" : ""}{item.priceChange}%
                        </span>
                        
                        {item.volume && (
                          <>
                            <span className="text-muted-foreground">Volume:</span>
                            <span className="font-medium">{item.volume}</span>
                          </>
                        )}
                        
                        {item.lastUpdated && (
                          <>
                            <span className="text-muted-foreground">Updated:</span>
                            <span className="font-medium">{formatUpdateTime(item.lastUpdated)}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
            
            {/* Duplicate items for continuous flow */}
            {commodityData.map((item, index) => (
              <TooltipProvider key={`${item.id}-repeat-${index}`}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div 
                      className="inline-flex items-center mx-6 hover:bg-card/80 rounded-full px-4 py-2 transition-colors border border-transparent hover:border-border transform hover:scale-105 hover:shadow-md relative overflow-hidden cursor-pointer"
                      style={{
                        transform: 'translateZ(0)', // 3D transform for performance
                      }}
                    >
                      <div className="flex flex-col mr-3">
                        <span className="font-medium">{item.name}</span>
                        <span className="text-xs text-muted-foreground">UGX/kg</span>
                      </div>

                      <div className="flex flex-col items-end">
                        <span className="font-bold tabular-nums">{item.currentPrice.toLocaleString()}</span>
                        <div 
                          className={`flex items-center text-xs ${
                            item.priceChange > 0 
                              ? "text-[#8BC34A]" 
                              : item.priceChange < 0 
                              ? "text-red-500" 
                              : "text-yellow-500"
                          }`}
                        >
                          {item.priceChange > 0 ? (
                            <ArrowUpRight className="h-3 w-3 mr-1" />
                          ) : item.priceChange < 0 ? (
                            <ArrowDownRight className="h-3 w-3 mr-1" />
                          ) : (
                            <ArrowRightIcon className="h-3 w-3 mr-1" />
                          )}
                          <span className="tabular-nums">
                            {item.priceChange > 0 ? "+" : ""}{item.priceChange}%
                          </span>
                        </div>
                      </div>
                      
                      {/* 3D lighting effect */}
                      <div className="absolute inset-0 bg-gradient-to-tr from-transparent to-white/10 pointer-events-none"></div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="w-56 p-0">
                    <div className="p-3">
                      <h4 className="font-medium mb-1">{item.name}</h4>
                      <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs">
                        <span className="text-muted-foreground">Current:</span>
                        <span className="font-medium">{item.currentPrice.toLocaleString()} UGX</span>
                        
                        <span className="text-muted-foreground">Previous:</span>
                        <span className="font-medium">{item.previousPrice.toLocaleString()} UGX</span>
                        
                        <span className="text-muted-foreground">Change:</span>
                        <span className={`font-medium ${
                          item.priceChange > 0 
                            ? "text-[#8BC34A]" 
                            : item.priceChange < 0 
                            ? "text-red-500" 
                            : ""
                        }`}>
                          {item.priceChange > 0 ? "+" : ""}{item.priceChange}%
                        </span>
                        
                        {item.volume && (
                          <>
                            <span className="text-muted-foreground">Volume:</span>
                            <span className="font-medium">{item.volume}</span>
                          </>
                        )}
                        
                        {item.lastUpdated && (
                          <>
                            <span className="text-muted-foreground">Updated:</span>
                            <span className="font-medium">{formatUpdateTime(item.lastUpdated)}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ))}
          </div>
        )}
      </div>

      {/* Help tooltip */}
      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10">
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <button className="text-muted-foreground hover:text-foreground">
                <HelpCircle className="h-4 w-4" />
              </button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p className="text-xs">Hover over items to see details and pause scrolling.<br />Data updates every 30 seconds.</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
};

// Add this to your global CSS or add inline styles
const styles = `
@keyframes flash {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

.animate-flash {
  animation: flash 0.5s ease-in-out;
}

.perspective-1000 {
  perspective: 1000px;
}
`;

// Append styles to head if they don't already exist
if (!document.head.querySelector('#market-marquee-styles')) {
  const styleSheet = document.createElement("style");
  styleSheet.id = 'market-marquee-styles';
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}

export default MarketMarquee;