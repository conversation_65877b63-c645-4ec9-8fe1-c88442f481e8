
import React from 'react';
import { Card, CardHeader, CardContent, CardDescription, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ShieldCheck } from "lucide-react";
import { insuranceProducts } from './QuoteCalculator';

// Mock active policies
const activePolicies = [
  { 
    id: "pol-101", 
    productId: "ins-01", 
    coverageType: "Standard", 
    startDate: "2023-11-01", 
    endDate: "2024-10-31", 
    premium: 400000,
    paidAmount: 400000,
    status: "Active", 
    details: {
      cropType: "Coffee (Arabica)",
      acreage: 5,
      location: "Eastern Region"
    }
  },
  { 
    id: "pol-102", 
    productId: "ins-02", 
    coverageType: "Basic", 
    startDate: "2023-09-15", 
    endDate: "2024-09-14", 
    premium: 120000,
    paidAmount: 60000,
    status: "Active", 
    details: {
      livestockType: "Cattle",
      numberOfAnimals: 6,
      location: "Northern Region"
    }
  },
];

interface PolicyListProps {
  onFileClaimClick: () => void;
  onExploreClick: () => void;
}

const PolicyList = ({ onFileClaimClick, onExploreClick }: PolicyListProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Your Insurance Policies</CardTitle>
        <CardDescription>Manage your active insurance coverage</CardDescription>
      </CardHeader>
      <CardContent>
        {activePolicies.length > 0 ? (
          <div className="space-y-6">
            {activePolicies.map(policy => {
              const insuranceProduct = insuranceProducts.find(product => product.id === policy.productId) || insuranceProducts[0];
              
              return (
                <Card key={policy.id} className="bg-card border">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-lg">{insuranceProduct.name}</CardTitle>
                      <div className={`px-2 py-1 rounded text-xs ${
                        policy.status === "Active" ? "bg-green-100 text-green-800" : 
                        policy.status === "Pending" ? "bg-yellow-100 text-yellow-800" : 
                        "bg-red-100 text-red-800"
                      }`}>
                        {policy.status}
                      </div>
                    </div>
                    <CardDescription>Policy #{policy.id} | {policy.coverageType} Coverage</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground block">Coverage Period:</span>
                          <span className="font-medium">{policy.startDate} to {policy.endDate}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground block">Annual Premium:</span>
                          <span className="font-medium">{policy.premium.toLocaleString()} UGX</span>
                        </div>
                        
                        {policy.details.cropType && (
                          <>
                            <div>
                              <span className="text-muted-foreground block">Crop Type:</span>
                              <span className="font-medium">{policy.details.cropType}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground block">Acreage:</span>
                              <span className="font-medium">{policy.details.acreage} acres</span>
                            </div>
                          </>
                        )}
                        
                        {policy.details.livestockType && (
                          <>
                            <div>
                              <span className="text-muted-foreground block">Livestock Type:</span>
                              <span className="font-medium">{policy.details.livestockType}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground block">Number of Animals:</span>
                              <span className="font-medium">{policy.details.numberOfAnimals}</span>
                            </div>
                          </>
                        )}
                        
                        <div>
                          <span className="text-muted-foreground block">Location:</span>
                          <span className="font-medium">{policy.details.location}</span>
                        </div>
                        <div>
                          <span className="text-muted-foreground block">Payment Status:</span>
                          <span className={`font-medium ${policy.paidAmount === policy.premium ? "text-green-600" : "text-yellow-600"}`}>
                            {policy.paidAmount === policy.premium ? "Fully Paid" : `${policy.paidAmount.toLocaleString()} UGX / ${policy.premium.toLocaleString()} UGX`}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline">View Details</Button>
                    <Button onClick={onFileClaimClick}>File a Claim</Button>
                  </CardFooter>
                </Card>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
              <ShieldCheck className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">No Active Policies</h3>
            <p className="text-muted-foreground mb-4">You don't have any active insurance policies at the moment.</p>
            <Button onClick={onExploreClick}>Explore Insurance Products</Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PolicyList;
