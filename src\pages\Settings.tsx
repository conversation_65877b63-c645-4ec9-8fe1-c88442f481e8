
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>nt, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { CreditCard, Key, User, Settings, Link as LinkIcon, Bell, MonitorSmartphone, Languages } from "lucide-react";
import ServicePreferences from "@/components/settings/ServicePreferences";

const dummyProfile = {
  username: "traderjoe",
  email: "<EMAIL>",
  contact: "+**********",
};

const dummyLinkedAccounts = [
  { platform: "Google", status: "Linked", icon: "/placeholder.svg" },
  { platform: "Facebook", status: "Unlinked", icon: "/placeholder.svg" },
];

const dummyPaymentMethods = [
  { type: "Visa", last4: "4242", expiry: "12/27" },
  { type: "MasterCard", last4: "2424", expiry: "08/26" },
];

const dummyApiKeys = [
  { key: "api_**********abcdef", label: "Trading Bot", created: "2024-04-01" },
  { key: "api_abcdef**********", label: "Mobile App", created: "2024-01-15" },
];

export default function SettingsPage() {
  const [profile, setProfile] = useState(dummyProfile);
  const [apiKeys] = useState(dummyApiKeys);
  const [paymentMethods] = useState(dummyPaymentMethods);
  const [theme, setTheme] = useState("system");
  const [language, setLanguage] = useState("en");
  const [emailNotifs, setEmailNotifs] = useState(true);
  const [inAppNotifs, setInAppNotifs] = useState(true);
  const [mfa, setMfa] = useState(false);

  // TODO: Replace with real update logic
  const handleProfileUpdate = (e: React.FormEvent) => {
    e.preventDefault();
    alert("Profile updated!");
  };

  return (
    <div className="container max-w-3xl mx-auto py-12">
      <Card className="mb-8 shadow-md bg-card">
        <CardHeader className="flex flex-col gap-2">
          <CardTitle className="flex gap-2 items-center text-2xl">
            <Settings className="text-primary" /> Platform Settings
          </CardTitle>
          <p className="text-muted-foreground text-base">
            Manage your account preferences, security, and integrations.
          </p>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="account" className="w-full">
            <TabsList className="flex flex-wrap gap-2 mb-6 border-b border-muted w-full justify-start">
              <TabsTrigger value="account" className="px-4 py-2 rounded-t bg-secondary data-[state=active]:bg-background text-sm flex items-center gap-1 cursor-pointer select-none"> <User size={18}/> Account </TabsTrigger>
              <TabsTrigger value="security" className="px-4 py-2 rounded-t bg-secondary data-[state=active]:bg-background text-sm flex items-center gap-1 cursor-pointer select-none"> <Key size={18}/> Security </TabsTrigger>
              <TabsTrigger value="notifications" className="px-4 py-2 rounded-t bg-secondary data-[state=active]:bg-background text-sm flex items-center gap-1 cursor-pointer select-none"> <Bell size={18}/> Notifications </TabsTrigger>
              <TabsTrigger value="payment" className="px-4 py-2 rounded-t bg-secondary data-[state=active]:bg-background text-sm flex items-center gap-1 cursor-pointer select-none"> <CreditCard size={18}/> Payment </TabsTrigger>
              <TabsTrigger value="apikey" className="px-4 py-2 rounded-t bg-secondary data-[state=active]:bg-background text-sm flex items-center gap-1 cursor-pointer select-none"> <Key size={18}/> API Keys </TabsTrigger>
              <TabsTrigger value="theme" className="px-4 py-2 rounded-t bg-secondary data-[state=active]:bg-background text-sm flex items-center gap-1 cursor-pointer select-none"> <MonitorSmartphone size={18}/> Theme </TabsTrigger>
              <TabsTrigger value="language" className="px-4 py-2 rounded-t bg-secondary data-[state=active]:bg-background text-sm flex items-center gap-1 cursor-pointer select-none"> <Languages size={18}/> Language </TabsTrigger>
              <TabsTrigger value="linked" className="px-4 py-2 rounded-t bg-secondary data-[state=active]:bg-background text-sm flex items-center gap-1 cursor-pointer select-none"> <LinkIcon size={18}/> Linked Accounts </TabsTrigger>
              <TabsTrigger value="services" className="px-4 py-2 rounded-t bg-secondary data-[state=active]:bg-background text-sm flex items-center gap-1 cursor-pointer select-none"> <Settings size={18}/> Services </TabsTrigger>
            </TabsList>
            
            {/* Account Information */}
            <TabsContent value="account" className="py-6">
              <form onSubmit={handleProfileUpdate} className="space-y-4">
                <div>
                  <Label htmlFor="username">Username</Label>
                  <Input id="username" type="text" value={profile.username} onChange={e => setProfile({ ...profile, username: e.target.value })} />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" value={profile.email} readOnly className="bg-muted-foreground/10" />
                </div>
                <div>
                  <Label htmlFor="contact">Contact</Label>
                  <Input id="contact" type="tel" value={profile.contact} onChange={e => setProfile({ ...profile, contact: e.target.value })} />
                </div>
                <Button type="submit" className="mt-2">Update Profile</Button>
              </form>
            </TabsContent>

            {/* Security Settings */}
            <TabsContent value="security" className="py-6">
              <form className="space-y-4">
                <div>
                  <Label htmlFor="password">Change Password</Label>
                  <Input id="password" type="password" placeholder="New Password" />
                </div>
                <div className="flex items-center gap-4">
                  <Switch checked={mfa} onCheckedChange={setMfa} id="mfa" />
                  <Label htmlFor="mfa" className="cursor-pointer">Enable Multi-factor Authentication</Label>
                </div>
                <Button type="button" variant="outline" className="mt-2">Update Security Settings</Button>
              </form>
              <div className="mt-6">
                <CardTitle className="text-base mb-2">Active Sessions</CardTitle>
                <p className="text-sm text-muted-foreground">Session management coming soon.</p>
              </div>
            </TabsContent>

            {/* Notifications */}
            <TabsContent value="notifications" className="py-6">
              <form className="space-y-4">
                <div className="flex items-center gap-4">
                  <Switch checked={emailNotifs} onCheckedChange={setEmailNotifs} id="emailnotifs" />
                  <Label htmlFor="emailnotifs" className="cursor-pointer">Email Notifications (Trade updates, Alerts)</Label>
                </div>
                <div className="flex items-center gap-4">
                  <Switch checked={inAppNotifs} onCheckedChange={setInAppNotifs} id="inappnotifs" />
                  <Label htmlFor="inappnotifs" className="cursor-pointer">In-App Notifications</Label>
                </div>
                <Button type="button" variant="outline" className="mt-2">Save Preferences</Button>
              </form>
            </TabsContent>

            {/* Payment Methods */}
            <TabsContent value="payment" className="py-6">
              <CardTitle className="text-base mb-4">Manage Payment Methods</CardTitle>
              <div className="space-y-4">
                {paymentMethods.map((pm, i) => (
                  <div key={i} className="flex items-center justify-between bg-muted rounded px-4 py-3">
                    <span className="flex items-center gap-2">{pm.type} ****{pm.last4} <span className="text-xs text-gray-400 ml-2">{pm.expiry}</span></span>
                    <div className="flex gap-2">
                      <Button size="sm" variant="ghost">Edit</Button>
                      <Button size="sm" variant="destructive">Delete</Button>
                    </div>
                  </div>
                ))}
                <Button className="mt-2">Add Payment Method</Button>
              </div>
              <p className="text-xs text-muted-foreground mt-3">Payment integrations coming soon.</p>
            </TabsContent>

            {/* API Keys */}
            <TabsContent value="apikey" className="py-6">
              <CardTitle className="text-base mb-4">API Keys</CardTitle>
              <div className="space-y-3">
                {apiKeys.map((key, i) => (
                  <div key={i} className="flex items-center justify-between bg-muted rounded px-4 py-3">
                    <span className="truncate font-mono">{key.key}</span>
                    <span className="text-xs text-muted-foreground ml-2">{key.label} — {key.created}</span>
                    <Button size="sm" variant="destructive">Delete</Button>
                  </div>
                ))}
                <Button className="mt-2">Generate API Key</Button>
              </div>
            </TabsContent>

            {/* Theme Customization */}
            <TabsContent value="theme" className="py-6">
              <CardTitle className="text-base mb-4">Select Theme</CardTitle>
              <Select value={theme} onValueChange={setTheme}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Theme" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="system">System</SelectItem>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                </SelectContent>
              </Select>
            </TabsContent>

            {/* Language Preferences */}
            <TabsContent value="language" className="py-6">
              <CardTitle className="text-base mb-4">Preferred Language</CardTitle>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="fr">French</SelectItem>
                  <SelectItem value="es">Spanish</SelectItem>
                </SelectContent>
              </Select>
            </TabsContent>

            {/* Linked Accounts */}
            <TabsContent value="linked" className="py-6">
              <CardTitle className="text-base mb-4">Linked Accounts</CardTitle>
              <div className="space-y-4">
                {dummyLinkedAccounts.map((a, i) => (
                  <div key={i} className="flex items-center justify-between bg-muted rounded px-4 py-3">
                    <div className="flex items-center gap-2">
                      <img src={a.icon} alt={a.platform} className="h-5 w-5" />
                      {a.platform}
                    </div>
                    <span className={a.status === "Linked" ? "text-green-600 text-xs font-semibold" : "text-muted-foreground text-xs"}>{a.status}</span>
                    <Button size="sm" variant={a.status === "Linked" ? "destructive" : "default"}>
                      {a.status === "Linked" ? "Unlink" : "Link"}
                    </Button>
                  </div>
                ))}
              </div>
            </TabsContent>
            
            {/* Services Preferences */}
            <TabsContent value="services" className="py-6">
              <ServicePreferences />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
