
import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { getServicesByCategory } from "@/data/servicesData";
import { Calendar, Users, Leaf, Mountain, SprayCan } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from '@/utils/currencyFormatter';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

type ConsultantType = {
  id: string;
  name: string;
  title: string;
  specialization: string;
  experience: number;
  rate: number;
  image: string;
  availability: "available" | "limited" | "unavailable";
  rating: number;
  reviewCount: number;
};

const consultants: ConsultantType[] = [
  {
    id: "cons-1",
    name: "Dr. <PERSON>",
    title: "Senior Agronomist",
    specialization: "Crop Rotation & Soil Management",
    experience: 15,
    rate: 120000,
    image: "https://picsum.photos/seed/agro1/300/300",
    availability: "available",
    rating: 4.9,
    reviewCount: 42,
  },
  {
    id: "cons-2",
    name: "Michael Rodriguez",
    title: "Pest Management Specialist",
    specialization: "Integrated Pest Management",
    experience: 12,
    rate: 100000,
    image: "https://picsum.photos/seed/agro2/300/300",
    availability: "limited",
    rating: 4.7,
    reviewCount: 36,
  },
  {
    id: "cons-3",
    name: "Emma Chen",
    title: "Irrigation Expert",
    specialization: "Water Management & Conservation",
    experience: 8,
    rate: 90000,
    image: "https://picsum.photos/seed/agro3/300/300",
    availability: "available",
    rating: 4.8,
    reviewCount: 28,
  },
  {
    id: "labor-1",
    name: "John's Labor Team",
    title: "Seasonal Workers",
    specialization: "Harvesting & Planting",
    experience: 10,
    rate: 25000,
    image: "https://picsum.photos/seed/labor1/300/300",
    availability: "available",
    rating: 4.6,
    reviewCount: 52,
  }
];

type ServicePackage = {
  id: string;
  title: string;
  description: string;
  price: number;
  features: string[];
  popular: boolean;
};

const servicePackages: ServicePackage[] = [
  {
    id: "basic",
    title: "Basic Consultation",
    description: "Essential agronomic services for small farms",
    price: 500000,
    features: [
      "Initial soil assessment",
      "Basic crop planning",
      "Quarterly field visits",
      "Email support"
    ],
    popular: false
  },
  {
    id: "standard",
    title: "Standard Package",
    description: "Comprehensive support for medium-sized operations",
    price: 1200000,
    features: [
      "Detailed soil analysis",
      "Seasonal crop planning",
      "Monthly field visits",
      "Pest management advice",
      "Priority email & phone support"
    ],
    popular: true
  },
  {
    id: "premium",
    title: "Premium Service",
    description: "Full-scale agronomic management for large farms",
    price: 2500000,
    features: [
      "Advanced soil & water analysis",
      "Strategic crop rotation planning",
      "Weekly field visits",
      "Comprehensive pest & disease management",
      "Irrigation optimization",
      "24/7 support with dedicated agronomist"
    ],
    popular: false
  }
];

const Agronomy = () => {
  const agronomyServices = getServicesByCategory("agronomy");
  const { toast } = useToast();
  const [isBookingOpen, setIsBookingOpen] = useState(false);
  const [isPackageDialogOpen, setIsPackageDialogOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<string | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<ServicePackage | null>(null);
  const [bookingDate, setBookingDate] = useState("");
  const [contactDetails, setContactDetails] = useState({
    name: "",
    email: "",
    phone: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleBookService = (serviceTitle: string) => {
    setSelectedService(serviceTitle);
    setIsBookingOpen(true);
  };

  const handleSelectPackage = (pkg: ServicePackage) => {
    setSelectedPackage(pkg);
    setIsPackageDialogOpen(true);
  };

  const handleSubmitBooking = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setIsBookingOpen(false);
      toast({
        title: "Booking Successful",
        description: `Your ${selectedService} service has been booked for ${bookingDate}. A confirmation has been sent to your email.`,
      });
      // Reset form
      setSelectedService(null);
      setBookingDate("");
      setContactDetails({
        name: "",
        email: "",
        phone: "",
      });
    }, 1500);
  };

  const handlePackagePurchase = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setIsPackageDialogOpen(false);
      toast({
        title: "Package Selected",
        description: `Your ${selectedPackage?.title} package has been selected. Our team will contact you shortly to finalize the details.`,
      });
      // Reset
      setSelectedPackage(null);
    }, 1500);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col space-y-2 mb-8">
        <h1 className="text-3xl font-bold">Agronomy Services</h1>
        <p className="text-muted-foreground">
          Professional agricultural consulting and labor services to optimize your farm operations
        </p>
      </div>

      {/* Service Packages */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Service Packages</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {servicePackages.map((pkg) => (
            <Card key={pkg.id} className={`relative ${pkg.popular ? "border-primary" : ""}`}>
              {pkg.popular && (
                <div className="absolute top-0 right-0 transform translate-x-1/3 -translate-y-1/3">
                  <Badge className="bg-primary">Most Popular</Badge>
                </div>
              )}
              <CardHeader>
                <CardTitle>{pkg.title}</CardTitle>
                <CardDescription>{pkg.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <span className="text-3xl font-bold">{formatCurrency(pkg.price)}</span>
                  <span className="text-muted-foreground">/season</span>
                </div>

                <ul className="space-y-2">
                  {pkg.features.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <div className="rounded-full bg-primary/10 p-1 mr-2">
                        <svg className="h-3 w-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M5 13l4 4L19 7"></path>
                        </svg>
                      </div>
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button 
                  className={`w-full ${pkg.popular ? "bg-primary" : ""}`}
                  onClick={() => handleSelectPackage(pkg)}
                >
                  Select Package
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>

      {/* Consultants Section */}
      <div className="mb-12">
        <h2 className="text-2xl font-bold mb-6">Our Specialists</h2>
        <Tabs defaultValue="consultants">
          <TabsList className="mb-6">
            <TabsTrigger value="consultants">Agronomists</TabsTrigger>
            <TabsTrigger value="labor">Labor Teams</TabsTrigger>
          </TabsList>
          
          <TabsContent value="consultants">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {consultants
                .filter(c => !c.id.includes('labor'))
                .map((consultant) => (
                  <ConsultantCard key={consultant.id} consultant={consultant} />
                ))}
            </div>
          </TabsContent>
          
          <TabsContent value="labor">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {consultants
                .filter(c => c.id.includes('labor'))
                .map((consultant) => (
                  <ConsultantCard key={consultant.id} consultant={consultant} />
                ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Services List */}
      <div>
        <h2 className="text-2xl font-bold mb-6">Our Agronomy Services</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {agronomyServices.map((service) => (
            <Card key={service.id} className="flex">
              <div className="p-6 flex-grow">
                <h3 className="text-lg font-semibold mb-2">{service.title}</h3>
                <p className="text-muted-foreground">{service.description}</p>
              </div>
              <div className="p-6 border-l flex items-center">
                <Button 
                  variant="outline"
                  onClick={() => handleBookService(service.title)}
                >
                  Book Now
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Service Booking Dialog */}
      <Dialog open={isBookingOpen} onOpenChange={setIsBookingOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Book {selectedService}</DialogTitle>
            <DialogDescription>
              Fill in your details to book this service. Our team will confirm your appointment.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Full Name</Label>
              <Input 
                id="name" 
                value={contactDetails.name} 
                onChange={(e) => setContactDetails({...contactDetails, name: e.target.value})}
                placeholder="John Doe" 
                required 
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="email">Email Address</Label>
              <Input 
                id="email" 
                type="email" 
                value={contactDetails.email} 
                onChange={(e) => setContactDetails({...contactDetails, email: e.target.value})}
                placeholder="<EMAIL>" 
                required 
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input 
                id="phone" 
                value={contactDetails.phone} 
                onChange={(e) => setContactDetails({...contactDetails, phone: e.target.value})}
                placeholder="+256 123 456 789" 
                required 
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="date">Preferred Date</Label>
              <Input 
                id="date" 
                type="date" 
                value={bookingDate} 
                onChange={(e) => setBookingDate(e.target.value)}
                required 
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBookingOpen(false)}>Cancel</Button>
            <Button 
              onClick={handleSubmitBooking} 
              disabled={!contactDetails.name || !contactDetails.email || !contactDetails.phone || !bookingDate || isLoading}
            >
              {isLoading ? "Processing..." : "Book Service"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Package Selection Dialog */}
      <Dialog open={isPackageDialogOpen} onOpenChange={setIsPackageDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Purchase {selectedPackage?.title}</DialogTitle>
            <DialogDescription>
              Complete your purchase for {selectedPackage?.title} at {selectedPackage ? formatCurrency(selectedPackage.price) : ""}/season.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="client-name">Full Name</Label>
              <Input id="client-name" placeholder="John Doe" required />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="client-email">Email</Label>
              <Input id="client-email" type="email" placeholder="<EMAIL>" required />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="client-phone">Phone</Label>
              <Input id="client-phone" placeholder="+256 123 456 789" required />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="farm-size">Farm Size (acres)</Label>
              <Input id="farm-size" type="number" placeholder="10" required />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPackageDialogOpen(false)}>Cancel</Button>
            <Button onClick={handlePackagePurchase}>
              {isLoading ? "Processing..." : "Complete Purchase"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

const ConsultantCard = ({ consultant }: { consultant: ConsultantType }) => {
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [bookingDetails, setBookingDetails] = useState({
    date: "",
    time: "",
    message: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleBookConsultation = () => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setIsDialogOpen(false);
      toast({
        title: "Consultation Booked",
        description: `Your consultation with ${consultant.name} has been booked. A confirmation has been sent to your email.`,
      });
      // Reset form
      setBookingDetails({
        date: "",
        time: "",
        message: "",
      });
    }, 1500);
  };

  return (
    <>
      <Card>
        <div className="p-4">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <img 
                src={consultant.image} 
                alt={consultant.name} 
                className="w-16 h-16 rounded-full object-cover" 
              />
              <Badge 
                className={`absolute -bottom-1 -right-1 ${
                  consultant.availability === "available" ? "bg-green-500" : 
                  consultant.availability === "limited" ? "bg-yellow-500" : "bg-red-500"
                }`}
              >
                {consultant.availability === "available" ? "Available" : 
                consultant.availability === "limited" ? "Limited" : "Busy"}
              </Badge>
            </div>
            <div>
              <h3 className="font-medium">{consultant.name}</h3>
              <p className="text-sm text-muted-foreground">{consultant.title}</p>
            </div>
          </div>

          <div className="mt-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm">Specialization:</span>
              <span className="text-sm font-medium">{consultant.specialization}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm">Experience:</span>
              <span className="text-sm font-medium">{consultant.experience} years</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm">Rate:</span>
              <span className="text-sm font-medium">{formatCurrency(consultant.rate)}/hour</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Rating:</span>
              <span className="text-sm font-medium">★ {consultant.rating} ({consultant.reviewCount} reviews)</span>
            </div>
          </div>

          <Button 
            className="w-full mt-4" 
            onClick={() => setIsDialogOpen(true)}
          >
            Book Consultation
          </Button>
        </div>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Book Consultation with {consultant.name}</DialogTitle>
            <DialogDescription>
              Fill in the details below to book your consultation session.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="booking-date">Date</Label>
              <Input 
                id="booking-date" 
                type="date" 
                value={bookingDetails.date}
                onChange={(e) => setBookingDetails({...bookingDetails, date: e.target.value})}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="booking-time">Time</Label>
              <Input 
                id="booking-time" 
                type="time" 
                value={bookingDetails.time}
                onChange={(e) => setBookingDetails({...bookingDetails, time: e.target.value})}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="booking-message">Additional Information</Label>
              <Input 
                id="booking-message" 
                placeholder="Describe your farming needs"
                value={bookingDetails.message}
                onChange={(e) => setBookingDetails({...bookingDetails, message: e.target.value})}
              />
            </div>
            <div className="grid gap-1">
              <div className="font-medium">Fee:</div>
              <div>{formatCurrency(consultant.rate)}/hour + applicable taxes</div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>Cancel</Button>
            <Button 
              onClick={handleBookConsultation} 
              disabled={!bookingDetails.date || !bookingDetails.time || isLoading}
            >
              {isLoading ? "Processing..." : "Confirm Booking"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Agronomy;
