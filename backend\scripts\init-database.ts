import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import config from '../src/config/env';
import logger from '../src/utils/logger';

// Initialize Prisma client
const prisma = new PrismaClient();

// Helper function to hash passwords
async function hashPassword(password: string): Promise<string> {
  return await bcrypt.hash(password, config.BCRYPT_SALT_ROUNDS);
}

async function main() {
  logger.info('Starting database initialization...');

  try {
    // Create admin user
    const adminPasswordHash = await hashPassword('Admin@123');
    
    const admin = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        passwordHash: adminPasswordHash,
        role: 'ADMIN',
        isEmailVerified: true,
        wallet: {
          create: {
            balance: 10000,
          },
        },
      },
    });
    
    logger.info(`Created admin user with ID: ${admin.id}`);

    // Create test user
    const userPasswordHash = await hashPassword('User@123');
    
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        passwordHash: userPasswordHash,
        role: 'USER',
        isEmailVerified: true,
        wallet: {
          create: {
            balance: 5000,
          },
        },
      },
    });
    
    logger.info(`Created test user with ID: ${user.id}`);

    // Create sample commodities
    const commodities = [
      {
        name: 'Coffee',
        symbol: 'COFF',
        description: 'Arabica coffee beans',
        currentPrice: 5.25,
        category: 'Agriculture',
        unit: 'kg',
        imageUrl: 'https://example.com/images/coffee.jpg',
      },
      {
        name: 'Maize',
        symbol: 'MAZE',
        description: 'Yellow maize',
        currentPrice: 1.75,
        category: 'Agriculture',
        unit: 'kg',
        imageUrl: 'https://example.com/images/maize.jpg',
      },
      {
        name: 'Cocoa',
        symbol: 'COCO',
        description: 'Cocoa beans',
        currentPrice: 8.50,
        category: 'Agriculture',
        unit: 'kg',
        imageUrl: 'https://example.com/images/cocoa.jpg',
      },
    ];

    for (const commodity of commodities) {
      await prisma.commodity.upsert({
        where: { symbol: commodity.symbol },
        update: commodity,
        create: commodity,
      });
    }
    
    logger.info(`Created ${commodities.length} sample commodities`);

    // Create sample warehouses
    const warehouses = [
      {
        name: 'Kampala Central Warehouse',
        location: 'Kampala',
        capacity: 5000,
        availableSpace: 3500,
        pricePerUnitDay: 0.05,
      },
      {
        name: 'Jinja Storage Facility',
        location: 'Jinja',
        capacity: 3000,
        availableSpace: 2000,
        pricePerUnitDay: 0.04,
      },
      {
        name: 'Mbarara Warehouse',
        location: 'Mbarara',
        capacity: 2000,
        availableSpace: 1500,
        pricePerUnitDay: 0.03,
      },
    ];

    for (const warehouse of warehouses) {
      await prisma.warehouse.create({
        data: warehouse,
      });
    }
    
    logger.info(`Created ${warehouses.length} sample warehouses`);

    // Create sample drivers
    const drivers = [
      {
        name: 'John Doe',
        phoneNumber: '+256123456789',
        vehicleReg: 'UXA 123B',
        licenseNumber: 'DL12345',
        isAvailable: true,
      },
      {
        name: 'Jane Smith',
        phoneNumber: '+256987654321',
        vehicleReg: 'UXB 456C',
        licenseNumber: 'DL67890',
        isAvailable: true,
      },
    ];

    for (const driver of drivers) {
      await prisma.driver.create({
        data: driver,
      });
    }
    
    logger.info(`Created ${drivers.length} sample drivers`);

    // Add sample transactions to admin wallet
    const adminWallet = await prisma.wallet.findUnique({
      where: { userId: admin.id },
    });

    if (adminWallet) {
      const transactions = [
        {
          amount: 1000,
          type: 'DEPOSIT',
          status: 'COMPLETED',
          description: 'Initial deposit',
          walletId: adminWallet.id,
          paymentMethod: 'bank_transfer',
        },
        {
          amount: 500,
          type: 'WITHDRAWAL',
          status: 'COMPLETED',
          description: 'Withdrawal to bank account',
          walletId: adminWallet.id,
          paymentMethod: 'bank_transfer',
        },
      ];

      for (const transaction of transactions) {
        await prisma.transaction.create({
          data: transaction,
        });
      }
      
      logger.info(`Added ${transactions.length} sample transactions to admin wallet`);
    }

    logger.info('Database initialization completed successfully!');
  } catch (error) {
    logger.error('Error initializing database:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((e) => {
    logger.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 