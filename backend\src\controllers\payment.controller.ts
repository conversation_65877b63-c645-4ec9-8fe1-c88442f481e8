import { Request, Response, NextFunction } from 'express';
import { PaymentService } from '../services/payment.service';
import { WalletService } from '../services/wallet.service';
import { ApiError } from '../middlewares/errorHandler';
import config from '../config/env';
import logger from '../utils/logger';

export class PaymentController {
  private paymentService: PaymentService;
  private walletService: WalletService;

  constructor() {
    this.paymentService = new PaymentService();
    this.walletService = new WalletService();
  }

  /**
   * Create Stripe payment intent
   */
  createStripePaymentIntent = async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ApiError(401, 'Unauthorized');
      }

      const { amount, metadata = {} } = req.body;

      // Add user ID to metadata
      const paymentMetadata = {
        ...metadata,
        userId: req.user.id,
      };

      const paymentIntent = await this.paymentService.createStripePaymentIntent(
        amount,
        'ugx',
        paymentMetadata
      );

      res.status(200).json({
        status: 'success',
        data: paymentIntent,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Handle Stripe webhook events
   */
  handleStripeWebhook = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const signature = req.headers['stripe-signature'] as string;

      if (!signature) {
        throw new ApiError(400, 'Stripe signature missing');
      }

      if (!config.STRIPE_WEBHOOK_SECRET) {
        throw new ApiError(500, 'Stripe webhook secret not configured');
      }

      // Verify webhook signature
      // In a real implementation, you would use Stripe's webhook construction
      // const event = stripe.webhooks.constructEvent(
      //   req.body,
      //   signature,
      //   config.STRIPE_WEBHOOK_SECRET
      // );

      // For this implementation, we'll assume the webhook is valid
      const event = req.body;

      const result = await this.paymentService.handleStripeWebhook(event);

      res.status(200).json({
        status: 'success',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Initiate mobile money payment
   */
  initiateMobileMoneyPayment = async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ApiError(401, 'Unauthorized');
      }

      const { amount, phoneNumber, provider, reference, description } = req.body;

      let paymentResult;

      // Validate provider
      if (provider === 'mtn') {
        paymentResult = await this.paymentService.initiateMtnMobileMoneyPayment(
          phoneNumber,
          amount,
          reference || `mtn-${Date.now()}`,
          description || 'Wallet deposit via MTN Mobile Money'
        );
      } else if (provider === 'airtel') {
        paymentResult = await this.paymentService.initiateAirtelMoneyPayment(
          phoneNumber,
          amount,
          reference || `airtel-${Date.now()}`,
          description || 'Wallet deposit via Airtel Money'
        );
      } else {
        throw new ApiError(400, 'Invalid provider. Supported providers: mtn, airtel');
      }

      // Create a pending transaction in the wallet
      // In a real system, this would be handled by the webhook callback
      // For demo purposes, we'll assume payment is successful
      const transaction = await this.walletService.depositFunds(
        req.user.id,
        amount,
        'mobile_money',
        {
          provider,
          phoneNumber,
          externalTransactionId: paymentResult.transactionId,
        },
        paymentResult.transactionId
      );

      res.status(200).json({
        status: 'success',
        message: 'Mobile money payment initiated',
        data: {
          ...paymentResult,
          transaction,
        },
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Check mobile money payment status
   */
  checkMobileMoneyStatus = async (req: Request, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new ApiError(401, 'Unauthorized');
      }

      const { transactionId, provider } = req.params;

      const status = await this.paymentService.checkMobileMoneyStatus(
        transactionId,
        provider
      );

      res.status(200).json({
        status: 'success',
        data: status,
      });
    } catch (error) {
      next(error);
    }
  };
} 