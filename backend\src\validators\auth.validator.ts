import Jo<PERSON> from 'joi';
import { Role } from '../../types/role';

/**
 * Validation schemas for auth routes
 */
export class AuthValidation {
  /**
   * Validation schema for user registration
   */
  static register = Joi.object({
    body: Joi.object({
      firstName: Joi.string()
        .trim()
        .min(2)
        .max(50)
        .required()
        .messages({
          'string.empty': 'First name is required',
          'string.min': 'First name must be at least 2 characters',
          'string.max': 'First name cannot exceed 50 characters',
          'any.required': 'First name is required',
        }),
      lastName: Joi.string()
        .trim()
        .min(2)
        .max(50)
        .required()
        .messages({
          'string.empty': 'Last name is required',
          'string.min': 'Last name must be at least 2 characters',
          'string.max': 'Last name cannot exceed 50 characters',
          'any.required': 'Last name is required',
        }),
      email: Joi.string()
        .email()
        .trim()
        .required()
        .messages({
          'string.empty': 'Email is required',
          'string.email': 'Please provide a valid email address',
          'any.required': 'Email is required',
        }),
      password: Joi.string()
        .min(8)
        .max(100)
        .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])'))
        .required()
        .messages({
          'string.empty': 'Password is required',
          'string.min': 'Password must be at least 8 characters',
          'string.max': 'Password cannot exceed 100 characters',
          'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
          'any.required': 'Password is required',
        }),
      confirmPassword: Joi.string()
        .valid(Joi.ref('password'))
        .required()
        .messages({
          'string.empty': 'Password confirmation is required',
          'any.only': 'Passwords do not match',
          'any.required': 'Password confirmation is required',
        }),
      phoneNumber: Joi.string()
        .pattern(new RegExp('^[0-9+]{8,15}$'))
        .allow('')
        .optional()
        .messages({
          'string.pattern.base': 'Please provide a valid phone number (8-15 digits, may include +)',
        }),
      country: Joi.string()
        .trim()
        .max(100)
        .allow('')
        .optional()
        .messages({
          'string.max': 'Country name cannot exceed 100 characters',
        }),
      city: Joi.string()
        .trim()
        .max(100)
        .allow('')
        .optional()
        .messages({
          'string.max': 'City name cannot exceed 100 characters',
        }),
      address: Joi.string()
        .trim()
        .max(200)
        .allow('')
        .optional()
        .messages({
          'string.max': 'Address cannot exceed 200 characters',
        }),
      role: Joi.string()
        .valid(...Object.values(Role))
        .required()
        .messages({
          'string.empty': 'Role is required',
          'any.only': 'Invalid role specified',
          'any.required': 'Role is required',
        }),
    }),
  });

  /**
   * Validation schema for user login
   */
  static login = Joi.object({
    body: Joi.object({
      email: Joi.string().email().required()
        .messages({
          'string.email': 'Please provide a valid email address',
          'any.required': 'Email is required',
        }),
      password: Joi.string().required()
        .messages({
          'any.required': 'Password is required',
        }),
    }).required(),
  });

  /**
   * Validation schema for token refresh
   */
  static refreshToken = Joi.object({
    body: Joi.object({
      refreshToken: Joi.string().required()
        .messages({
          'any.required': 'Refresh token is required',
        }),
    }).required(),
  });

  /**
   * Validation schema for logout
   */
  static logout = Joi.object({
    body: Joi.object({
      refreshToken: Joi.string().required()
        .messages({
          'any.required': 'Refresh token is required',
        }),
    }).required(),
  });

  /**
   * Validation schema for forgot password
   */
  static forgotPassword = Joi.object({
    body: Joi.object({
      email: Joi.string().email().required()
        .messages({
          'string.email': 'Please provide a valid email address',
          'any.required': 'Email is required',
        }),
    }).required(),
  });

  /**
   * Validation schema for reset password
   */
  static resetPassword = Joi.object({
    body: Joi.object({
      token: Joi.string().required()
        .messages({
          'any.required': 'Reset token is required',
        }),
      password: Joi.string()
        .min(8)
        .max(100)
        .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])'))
        .required()
        .messages({
          'string.min': 'Password must be at least 8 characters',
          'string.max': 'Password cannot exceed 100 characters',
          'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
          'any.required': 'Password is required',
        }),
      confirmPassword: Joi.string().valid(Joi.ref('password')).required()
        .messages({
          'any.only': 'Passwords do not match',
          'any.required': 'Password confirmation is required',
        }),
    }).required(),
  });

  /**
   * Validation schema for email verification
   */
  static verifyEmail = Joi.object({
    body: Joi.object({
      token: Joi.string().required()
        .messages({
          'any.required': 'Verification token is required',
        }),
    }).required(),
  });
} 