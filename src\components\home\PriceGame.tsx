import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, TrendingUp, Tren<PERSON>D<PERSON>, Clock, Zap } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { commodities } from "@/data/commodityData";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

// Leaderboard entry interface
interface LeaderboardEntry {
  id: string;
  username: string;
  avatar: string;
  initials: string;
  score: number;
  correctPredictions: number;
  rank?: number;
}

// Prediction market data
interface PredictionMarket {
  commodityId: string;
  upPercentage: number;
  downPercentage: number;
  endsAt: Date;
  totalParticipants: number;
}

// Time formatter
const formatTimeRemaining = (date: Date) => {
  const now = new Date();
  const diff = date.getTime() - now.getTime();
  
  if (diff <= 0) return "Ended";
  
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  
  return `${hours}h ${minutes}m`;
};

const PriceGame = ({ className }: { className?: string }) => {
  const [selectedCommodity, setSelectedCommodity] = useState(commodities[0].id);
  const [prediction, setPrediction] = useState<'up' | 'down' | null>(null);
  const [hasPredicted, setHasPredicted] = useState(false);
  
  // Mock leaderboard data
  const leaderboard: LeaderboardEntry[] = [
    { id: '1', username: 'farmer_joe', avatar: '', initials: 'FJ', score: 2450, correctPredictions: 24 },
    { id: '2', username: 'coffee_trader', avatar: '', initials: 'CT', score: 2100, correctPredictions: 20 },
    { id: '3', username: 'agro_expert', avatar: '', initials: 'AE', score: 1850, correctPredictions: 18 },
    { id: '4', username: 'You', avatar: '', initials: 'YO', score: 1500, correctPredictions: 15 },
    { id: '5', username: 'market_watcher', avatar: '', initials: 'MW', score: 1350, correctPredictions: 13 },
  ].map((entry, index) => ({ ...entry, rank: index + 1 }));

  // Mock prediction markets
  const predictionMarkets: Record<string, PredictionMarket> = {
    'coffee-arabica': {
      commodityId: 'coffee-arabica',
      upPercentage: 65,
      downPercentage: 35,
      endsAt: new Date(Date.now() + 3 * 60 * 60 * 1000), // 3 hours from now
      totalParticipants: 128
    },
    'cocoa': {
      commodityId: 'cocoa',
      upPercentage: 48,
      downPercentage: 52,
      endsAt: new Date(Date.now() + 5 * 60 * 60 * 1000), // 5 hours from now
      totalParticipants: 94
    },
    'maize': {
      commodityId: 'maize',
      upPercentage: 72,
      downPercentage: 28,
      endsAt: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
      totalParticipants: 156
    },
    'vanilla': {
      commodityId: 'vanilla',
      upPercentage: 55,
      downPercentage: 45,
      endsAt: new Date(Date.now() + 6 * 60 * 60 * 1000), // 6 hours from now
      totalParticipants: 82
    },
    'cotton': {
      commodityId: 'cotton',
      upPercentage: 38,
      downPercentage: 62,
      endsAt: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now
      totalParticipants: 103
    },
  };
  
  // Get current prediction market
  const getCurrentMarket = () => {
    return predictionMarkets[selectedCommodity] || predictionMarkets['coffee-arabica'];
  };
  
  // Handle prediction submission
  const handlePredict = (direction: 'up' | 'down') => {
    setPrediction(direction);
    
    setTimeout(() => {
      setHasPredicted(true);
      toast({
        title: "Prediction submitted!",
        description: `Your prediction that ${getCommodityName()} will go ${direction} has been recorded. Check back later to see the results.`,
      });
    }, 500);
  };
  
  // Reset prediction for new commodity
  const handleCommodityChange = (value: string) => {
    setSelectedCommodity(value);
    setPrediction(null);
    setHasPredicted(false);
  };
  
  // Get commodity name
  const getCommodityName = () => {
    const commodity = commodities.find(c => c.id === selectedCommodity);
    return commodity?.name || '';
  };
  
  // Current user's position in leaderboard
  const userPosition = leaderboard.findIndex(entry => entry.username === 'You') + 1;
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center text-xl">
          <Trophy className="h-5 w-5 mr-2 text-[#8BC34A]" /> 
          Price Prediction Game
        </CardTitle>
        <CardDescription>Predict price movements and earn points</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-1 block">Select Commodity</label>
              <Select 
                value={selectedCommodity} 
                onValueChange={handleCommodityChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select commodity" />
                </SelectTrigger>
                <SelectContent>
                  {commodities.map(commodity => (
                    <SelectItem key={commodity.id} value={commodity.id}>
                      {commodity.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="bg-muted/50 p-4 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-medium">Market Prediction</h4>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatTimeRemaining(getCurrentMarket().endsAt)} left
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm mb-1">
                  <span className="flex items-center">
                    <TrendingUp className="h-3 w-3 mr-1 text-[#8BC34A]" /> Up
                  </span>
                  <span>{getCurrentMarket().upPercentage}%</span>
                </div>
                <Progress value={getCurrentMarket().upPercentage} className="h-2 bg-muted" indicatorClassName="bg-[#8BC34A]" />
                
                <div className="flex justify-between text-sm mb-1">
                  <span className="flex items-center">
                    <TrendingDown className="h-3 w-3 mr-1 text-red-500" /> Down
                  </span>
                  <span>{getCurrentMarket().downPercentage}%</span>
                </div>
                <Progress value={getCurrentMarket().downPercentage} className="h-2 bg-muted" indicatorClassName="bg-red-500" />
              </div>
              
              <div className="mt-3 text-xs text-muted-foreground text-center">
                {getCurrentMarket().totalParticipants} participants in this prediction
              </div>
            </div>
            
            {hasPredicted ? (
              <div className="bg-[#8BC34A]/10 border border-[#8BC34A]/20 p-4 rounded-lg text-center">
                <Zap className="h-8 w-8 mx-auto mb-2 text-[#8BC34A]" />
                <h4 className="font-medium mb-1">Prediction Submitted!</h4>
                <p className="text-sm text-muted-foreground">
                  You predicted {getCommodityName()} will go 
                  <span className={prediction === 'up' ? 'text-[#8BC34A] font-medium mx-1' : 'text-red-500 font-medium mx-1'}>
                    {prediction === 'up' ? 'UP' : 'DOWN'}
                  </span>
                  by {formatTimeRemaining(getCurrentMarket().endsAt)}
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-3"
                  onClick={() => {
                    setHasPredicted(false);
                    setPrediction(null);
                  }}
                >
                  Make a new prediction
                </Button>
              </div>
            ) : (
              <div className="flex flex-col gap-2">
                <h4 className="font-medium mb-1">Your Prediction</h4>
                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    variant={prediction === 'up' ? 'default' : 'outline'} 
                    className={prediction === 'up' ? 'bg-[#8BC34A] hover:bg-[#8BC34A]/90' : ''}
                    onClick={() => handlePredict('up')}
                  >
                    <TrendingUp className="h-4 w-4 mr-2" /> 
                    Price will go UP
                  </Button>
                  <Button 
                    variant={prediction === 'down' ? 'default' : 'outline'} 
                    className={prediction === 'down' ? 'bg-red-500 hover:bg-red-600' : ''}
                    onClick={() => handlePredict('down')}
                  >
                    <TrendingDown className="h-4 w-4 mr-2" /> 
                    Price will go DOWN
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground text-center mt-1">
                  Make your prediction before the market closes
                </div>
              </div>
            )}
          </div>
          
          <div className="border rounded-md overflow-hidden">
            <div className="bg-muted/50 p-3 flex items-center justify-between">
              <h4 className="font-medium flex items-center">
                <BarChart className="h-4 w-4 mr-2 text-[#8BC34A]" />
                Leaderboard
              </h4>
              <Badge variant="outline" className="text-xs">
                Your rank: #{userPosition}
              </Badge>
            </div>
            
            <div className="divide-y">
              {leaderboard.map((entry) => (
                <div 
                  key={entry.id} 
                  className={`flex items-center p-3 ${entry.username === 'You' ? 'bg-muted/30' : ''}`}
                >
                  <div className="w-6 text-sm font-medium text-muted-foreground">
                    {entry.rank}
                  </div>
                  <Avatar className="h-8 w-8 mr-2">
                    <AvatarImage src={entry.avatar} alt={entry.username} />
                    <AvatarFallback className={entry.username === 'You' ? 'bg-[#8BC34A]/20 text-[#8BC34A]' : ''}>
                      {entry.initials}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium text-sm">{entry.username}</div>
                    <div className="text-xs text-muted-foreground flex items-center">
                      <TrendingUp className="h-3 w-3 mr-1 text-[#8BC34A]" />
                      {entry.correctPredictions} correct predictions
                    </div>
                  </div>
                  <div className="font-semibold">
                    {entry.score} pts
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="text-xs text-muted-foreground">
        New prediction markets open every day. Top predictors win rewards.
      </CardFooter>
    </Card>
  );
};

export default PriceGame; 