/**
 * Currency formatter utility for the application
 * Formats numbers as UGX currency values
 */

/**
 * Format a number as UGX currency
 * @param value - The number to format
 * @param includeCurrency - Whether to include the UGX symbol
 * @returns Formatted currency string
 */
export const formatCurrency = (value: number, includeCurrency = true): string => {
  // Format with thousand separators
  const formattedValue = new Intl.NumberFormat('en-UG', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
  
  // Return with or without currency symbol
  return includeCurrency ? `UGX ${formattedValue}` : formattedValue;
};

/**
 * Calculate fees for a transaction
 * @param amount - The transaction amount
 * @param feePercentage - The fee percentage (default: 0.5%)
 * @returns The calculated fee amount
 */
export const calculateFee = (amount: number, feePercentage = 0.5): number => {
  return Math.round(amount * (feePercentage / 100));
};

/**
 * Calculate the final amount after fees
 * @param amount - The transaction amount
 * @param feePercentage - The fee percentage (default: 0.5%)
 * @returns The final amount after fees
 */
export const calculateFinalAmount = (amount: number, feePercentage = 0.5): number => {
  const fee = calculateFee(amount, feePercentage);
  return amount - fee;
}; 