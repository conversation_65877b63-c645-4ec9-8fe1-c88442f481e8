import React, { useContext, useState, useEffect, createContext } from 'react';
import { commodities } from '@/data/commodityData';
import { useToast } from "@/hooks/use-toast";
import { UserAsset, Transaction, PaymentMethod } from '@/types/wallet'; // Import corrected types

// Define the context state and actions
type WalletContextType = {
    cashBalance: number;
    userAssets: UserAsset[];
    transactions: Transaction[];
    totalPortfolioValue: number;
    activeTransaction: Transaction | null;
    isLoadingTransactions: boolean;

    // Actions - Use the corrected PaymentMethod type
    depositFunds: (amount: number, method: PaymentMethod, accountDetails: { [key: string]: string }) => Promise<void>;
    withdrawFunds: (amount: number, method: PaymentMethod, accountDetails: { [key: string]: string }) => Promise<void>;
    buyAsset: (assetId: string, quantity: number, totalCost: number) => void;
    sellAsset: (assetId: string, quantity: number, proceeds: number) => void;
    spendFromWallet: (amountToSpend: number, serviceName: string, remarks: string) => Promise<boolean>;
    loadTransactionHistory: () => Promise<void>; // Expose transaction loading
};

// Create the context (initial value undefined)
export const WalletContext = createContext<WalletContextType | undefined>(undefined); // Changed from null

// Custom hook to use the context
export const useWalletContext = () => {
    const context = useContext(WalletContext);
    if (!context) {
        throw new Error('useWalletContext must be used within a WalletProvider');
    }
    return context;
};

// Mock initial assets (based on Wallet.tsx logic)
const initialAssetsData: UserAsset[] = commodities.map(commodity => ({
    id: commodity.id,
    name: commodity.name,
    symbol: commodity.id.toUpperCase().substring(0,3),
    balance: parseFloat((Math.random() * 10).toFixed(2)), // Random initial balance
    price: commodity.currentPrice, // Use current price
    value: 0, // Will be calculated in useEffect
    change: commodity.priceChange,
    iconName: commodity.name.toLowerCase().includes("coffee") ? "Coffee" : commodity.name.toLowerCase().includes("maize") ? "Grain" : "Package",
    color: commodity.color // Get color from commodity data
}));

// Wallet Provider component
const WalletProvider = ({ children }: { children: React.ReactNode }) => {
    const { toast } = useToast();
    const [cashBalance, setCashBalance] = useState(420000); // Initial balance
    const [userAssets, setUserAssets] = useState<UserAsset[]>(initialAssetsData);
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [activeTransaction, setActiveTransaction] = useState<Transaction | null>(null);
    const [isLoadingTransactions, setIsLoadingTransactions] = useState(false);

    // Effect to load initial transactions (runs once on mount)
    useEffect(() => {
        loadTransactionHistory();
    }, []); // Empty dependency array

    // Effect to calculate initial asset values and update values if balances/prices change
    useEffect(() => {
        setUserAssets(prevAssets =>
            prevAssets.map(asset => {
                const commodity = commodities.find(c => c.id === asset.id);
                // Use the latest price from commodities if found, otherwise keep existing
                const latestPrice = commodity?.currentPrice ?? asset.price;
                // Update change and color from commodities if available
                const latestChange = commodity?.priceChange ?? asset.change;
                const assetColor = commodity?.color ?? asset.color;

                return {
                    ...asset,
                    price: latestPrice, // Update price
                    value: asset.balance * latestPrice, // Recalculate value
                    change: latestChange, // Update change
                    color: assetColor, // Update color
                };
            })
        );
    }, [
        userAssets.map(a => a.balance).join(','), 
        JSON.stringify(commodities.map(c => ({ id: c.id, currentPrice: c.currentPrice }))) // NEW dependency: more sensitive to price changes
    ]); // Dependency on asset balances and commodities prices


    const totalPortfolioValue = userAssets.reduce((sum, asset) => sum + asset.value, 0);

    const loadTransactionHistory = async () => {
        setIsLoadingTransactions(true);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay

        // Ensure mock data aligns with Transaction type
        const mockTransactions: Transaction[] = [
          { id: "TX001", type: "deposit", asset: "UGX", amount: 50000, value: 50000, status: "completed", date: new Date(Date.now() - ******** * 2).toISOString(), method: "MTN Mobile Money"},
          { id: "TX002", type: "buy", asset: commodities.find(c => c.id === "coffee-arabica")?.name || "Coffee", amount: 2, price: commodities.find(c => c.id === "coffee-arabica")?.currentPrice || 7500, value: 2 * (commodities.find(c => c.id === "coffee-arabica")?.currentPrice || 7500), status: "completed", date: new Date(Date.now() - ******** * 1).toISOString() },
          { id: "TX003", type: "withdraw", asset: "UGX", amount: 10000, value: 10000, status: "pending", date: new Date().toISOString(), method: "Airtel Money"}, // status should be 'pending' or 'processing' initially
          { id: "TX004", type: "deposit", asset: "UGX", amount: 25000, value: 25000, status: "completed", date: new Date(Date.now() - ******** * 3).toISOString(), method: "Safaricom M-PESA"},
           { id: "TX005", type: "sell", asset: commodities.find(c => c.id === "cocoa")?.name || "Cocoa", amount: 1, price: commodities.find(c => c.id === "cocoa")?.currentPrice || 3800, value: 1 * (commodities.find(c => c.id === "cocoa")?.currentPrice || 3800), status: "completed", date: new Date(Date.now() - ******** * 0.5).toISOString() },
        ];
        setTransactions(mockTransactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()));
        setIsLoadingTransactions(false);
    };

    // Use corrected PaymentMethod type for 'method' parameter
    const depositFunds = async (amount: number, method: PaymentMethod, accountDetails: { [key: string]: string }) => {
         if (isNaN(amount) || amount <= 0) { toast({ title: "Invalid Amount", variant: "destructive" }); return; }
        if (!method) { toast({ title: "No Payment Method", variant: "destructive" }); return; }
         if (method.type === "mobile_money" && (!accountDetails.phone || !/^\d{9,15}$/.test(accountDetails.phone))) {
            toast({ title: "Invalid Phone Number", description: `Please enter a valid phone number for ${method.name}.`, variant: "destructive"}); return;
        }

        const newTx: Transaction = {
            id: `TXD${Date.now()}`,
            type: "deposit",
            asset: "UGX", // Asset is UGX for currency transactions
            amount: amount,
            value: amount, // Value is same as amount for UGX
            status: "processing", // Set status to processing initially
            date: new Date().toISOString(),
            method: method.name,
        };

        setActiveTransaction(newTx);
        // Add transaction and sort, ensure newTx status is processing
        setTransactions(prev => [{...newTx, status: "processing" as const}, ...prev].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()));
        toast({ title: "Deposit Initiated", description: `Processing ${formatCurrency(amount)}...`});

        await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate async process

        const success = Math.random() > 0.2; // 80% success rate

        // Update transaction status and cash balance
        setTransactions(prev => prev.map(tx => tx.id === newTx.id ? {...tx, status: success ? "completed" : "failed"} : tx));
        setActiveTransaction(null);

        if (success) {
            setCashBalance(prev => prev + amount);
            toast({ title: "Deposit Successful", description: `${formatCurrency(amount)} has been added to your balance.` });
        } else {
             toast({ title: "Deposit Failed", description: `Failed to deposit ${formatCurrency(amount)}. Please try again.`, variant: "destructive" });
        }
    };

    // Use corrected PaymentMethod type for 'method' parameter
    const withdrawFunds = async (amount: number, method: PaymentMethod, accountDetails: { [key: string]: string }) => {
        if (isNaN(amount) || amount <= 0) { toast({ title: "Invalid Amount", variant: "destructive" }); return; }
        if (!method) { toast({ title: "No Payment Method", variant: "destructive" }); return; }
        if (amount > cashBalance) { toast({ title: "Insufficient Balance", variant: "destructive" }); return; }
        if (method.type === "mobile_money" && (!accountDetails.phone || !/^\d{9,15}$/.test(accountDetails.phone))) {
             toast({ title: "Invalid Phone Number", description: `Please enter a valid phone number for ${method.name}.`, variant: "destructive"}); return;
        }

         // Deduct cash balance immediately for withdrawal attempt
         setCashBalance(prev => prev - amount); // Deduct at start
         toast({ title: "Withdrawal Initiated", description: `Processing withdrawal of ${formatCurrency(amount)}...`});


        const newTx: Transaction = {
            id: `TXW${Date.now()}`,
            type: "withdraw",
            asset: "UGX", // Asset is UGX
            amount: amount,
            value: amount, // Value is same as amount for UGX
            status: "processing", // Set status to processing initially
            date: new Date().toISOString(),
            method: method.name,
        };

        setActiveTransaction(newTx);
        // Add transaction and sort, ensure newTx status is processing
        setTransactions(prev => [{...newTx, status: "processing" as const}, ...prev].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()));


        await new Promise(resolve => setTimeout(resolve, 3000)); // Simulate async process

        const success = Math.random() > 0.2; // 80% success rate

         // Update transaction status
         setTransactions(prev => prev.map(tx => tx.id === newTx.id ? {...tx, status: success ? "completed" : "failed"} : tx));
        setActiveTransaction(null);

        if (success) {
            toast({ title: "Withdrawal Successful", description: `${formatCurrency(amount)} has been withdrawn.` });
        } else {
             // Refund cash balance if withdrawal fails
            setCashBalance(prev => prev + amount);
            toast({ title: "Withdrawal Failed", description: `Failed to withdraw ${formatCurrency(amount)}. Funds returned to balance.`, variant: "destructive" });
        }
    };

    const spendFromWallet = async (amountToSpend: number, serviceName: string, remarks: string): Promise<boolean> => {
        if (isNaN(amountToSpend) || amountToSpend <= 0) {
            toast({ title: "Invalid Amount", description: "Payment amount must be positive.", variant: "destructive" });
            return false;
        }
        if (cashBalance < amountToSpend) {
            toast({ title: "Insufficient Wallet Balance", description: `Cannot pay ${formatCurrency(amountToSpend)} for ${serviceName}.`, variant: "destructive" });
            return false;
        }

        setCashBalance(prev => prev - amountToSpend);

        const newTx: Transaction = {
            id: `TXEXP${Date.now()}`,
            // Assuming Transaction type can be extended implicitly or needs update in types/wallet.ts
            // For now, let's use a general 'expense' type string.
            // If Transaction['type'] is a strict union, this might need adjustment or casting.
            type: "expense" as Transaction['type'], // Using 'expense' or 'payment'
            asset: serviceName, // What was paid for
            amount: amountToSpend, // How much was paid (could also be seen as quantity if asset is 'UGX')
            value: amountToSpend, // The actual UGX value of the transaction
            status: "completed",
            date: new Date().toISOString(),
            method: "Wallet", // Payment method used
        };

        setTransactions(prev => [newTx, ...prev].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()));
        toast({ title: "Payment Successful", description: `${formatCurrency(amountToSpend)} paid for ${serviceName}.` });
        return true;
    };

    const buyAsset = (assetId: string, quantity: number, totalCost: number) => {
        const asset = userAssets.find(a => a.id === assetId);
        if (!asset) { toast({ title: "Asset Not Found", variant: "destructive"}); return; }
        if (cashBalance < totalCost) { toast({ title: "Insufficient Cash Balance", variant: "destructive" }); return; }
        if (isNaN(quantity) || quantity <= 0) { toast({ title: "Invalid Quantity", variant: "destructive" }); return; }
        if (isNaN(totalCost) || totalCost <= 0) { toast({ title: "Invalid Total Cost", variant: "destructive" }); return; }


        setCashBalance(prev => prev - totalCost);
        setUserAssets(prev => prev.map(item =>
            item.id === assetId ? { ...item, balance: parseFloat((item.balance + quantity).toFixed(2)), value: parseFloat(((item.balance + quantity) * item.price).toFixed(2)) } : item
        ));

        const newTx: Transaction = {
            id: `TXB${Date.now()}`,
            type: "buy",
            asset: asset.name,
            amount: quantity,
            price: asset.price, // Price at time of buy
            value: totalCost,
            status: "completed", // Assume instant completion for asset trades
            date: new Date().toISOString(),
        };
         setTransactions(prev => [newTx, ...prev].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()));

        toast({ title: `Asset Purchased`, description: `Successfully bought ${quantity} units of ${asset.name}.`});
    };

    const sellAsset = (assetId: string, quantity: number, proceeds: number) => {
        const asset = userAssets.find(a => a.id === assetId);
         if (!asset) { toast({ title: "Asset Not Found", variant: "destructive"}); return; }
        if (isNaN(quantity) || quantity <= 0) { toast({ title: "Invalid Quantity", variant: "destructive" }); return; }
        if (quantity > asset.balance) { toast({ title: "Insufficient Asset Balance", variant: "destructive"}); return; }
         if (isNaN(proceeds) || proceeds <= 0) { toast({ title: "Invalid Proceeds", variant: "destructive" }); return; }


        setCashBalance(prev => prev + proceeds);
        setUserAssets(prev => prev.map(item =>
            item.id === assetId ? { ...item, balance: parseFloat((item.balance - quantity).toFixed(2)), value: parseFloat(((item.balance - quantity) * item.price).toFixed(2)) } : item
        ));

        const newTx: Transaction = {
            id: `TXS${Date.now()}`,
            type: "sell",
            asset: asset.name,
            amount: quantity,
            price: asset.price, // Price at time of sell
            value: proceeds,
             status: "completed", // Assume instant completion for asset trades
            date: new Date().toISOString(),
        };
         setTransactions(prev => [newTx, ...prev].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()));

        toast({ title: `Asset Sold`, description: `Successfully sold ${quantity} units of ${asset.name}.` });
    };

    return (
        <WalletContext.Provider
            value={{
                cashBalance,
                userAssets,
                transactions,
                totalPortfolioValue,
                activeTransaction,
                isLoadingTransactions,
                depositFunds,
                withdrawFunds,
                buyAsset,
                sellAsset,
                spendFromWallet,
                loadTransactionHistory,
            }}
        >
            {children}
        </WalletContext.Provider>
    );
};

export { WalletProvider };

function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-UG', {
        style: 'currency',
        currency: 'UGX',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
}
