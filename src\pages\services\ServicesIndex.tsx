
import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Warehouse, PiggyBank, ShieldCheck, LandPlot, BarChart3, Users, Truck, Tractor, Leaf } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { services, getServicesByUserType } from '@/data/servicesData';

const ServicesIndex = () => {
  const navigate = useNavigate();
  
  // For demo purposes, we'll show services for all user types
  // In a real app, this would filter based on the logged-in user type
  const userType = "farmer"; // Example: Could be "farmer", "trader", or "transporter"
  const userServices = getServicesByUserType(userType);
  
  // Group services by category
  const groupedServices = services.reduce((acc, service) => {
    if (!acc[service.category]) {
      acc[service.category] = [];
    }
    acc[service.category].push(service);
    return acc;
  }, {} as Record<string, typeof services>);

  // Icon mapping for service categories
  const categoryIcons = {
    warehousing: <Warehouse className="h-6 w-6" />,
    financing: <PiggyBank className="h-6 w-6" />,
    insurance: <ShieldCheck className="h-6 w-6" />,
    leasing: <LandPlot className="h-6 w-6" />,
    futures: <BarChart3 className="h-6 w-6" />,
    transportation: <Truck className="h-6 w-6" />,
    equipment: <Tractor className="h-6 w-6" />,
    agronomy: <Leaf className="h-6 w-6" />,
  };

  // Route mapping for service categories
  const categoryRoutes = {
    warehousing: "/services/warehousing",
    financing: "/services/financing",
    insurance: "/services/insurance",
    leasing: "/services/leasing",
    futures: "/services/futures",
    transportation: "/services/transportation",
    equipment: "/services/equipment",
    agronomy: "/services/agronomy",
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Agricultural Services</h1>
          <p className="text-muted-foreground max-w-2xl">
            Access comprehensive services tailored for farmers, traders, and transporters to help manage and grow your agricultural business.
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Badge variant="outline" className="flex items-center gap-1 bg-primary/10 border-primary/30 text-primary">
            <Users size={14} /> Viewing as: Farmer
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Equipment Leasing Services */}
        <Card className="bg-card hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-2">
              {categoryIcons.equipment}
              <CardTitle>Hire a Tractor</CardTitle>
            </div>
            <CardDescription>Lease agricultural machinery and equipment for your farming operations</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {groupedServices.equipment?.map(service => (
                <li key={service.id} className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  <span>{service.title}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate(categoryRoutes.equipment)}>
              Access Equipment Services
            </Button>
          </CardFooter>
        </Card>

        {/* Transportation Services */}
        <Card className="bg-card hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-2">
              {categoryIcons.transportation}
              <CardTitle>Transportation Services</CardTitle>
            </div>
            <CardDescription>Book trucks and logistics services for moving your agricultural products</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {groupedServices.transportation?.map(service => (
                <li key={service.id} className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  <span>{service.title}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate(categoryRoutes.transportation)}>
              Access Transportation Services
            </Button>
          </CardFooter>
        </Card>

        {/* Agronomy Services */}
        <Card className="bg-card hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-2">
              {categoryIcons.agronomy}
              <CardTitle>Agronomy Services</CardTitle>
            </div>
            <CardDescription>Expert agricultural consulting and labor services for optimal farm management</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {groupedServices.agronomy?.map(service => (
                <li key={service.id} className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  <span>{service.title}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate(categoryRoutes.agronomy)}>
              Access Agronomy Services
            </Button>
          </CardFooter>
        </Card>

        {/* Warehousing Services */}
        <Card className="bg-card hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-2">
              {categoryIcons.warehousing}
              <CardTitle>Warehousing Services</CardTitle>
            </div>
            <CardDescription>Secure storage and inventory management for your agricultural products</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {groupedServices.warehousing?.map(service => (
                <li key={service.id} className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  <span>{service.title}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate(categoryRoutes.warehousing)}>
              Access Warehousing Services
            </Button>
          </CardFooter>
        </Card>

        {/* Financing Services */}
        <Card className="bg-card hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-2">
              {categoryIcons.financing}
              <CardTitle>Financing Services</CardTitle>
            </div>
            <CardDescription>Financial solutions for agriculture production and business growth</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {groupedServices.financing?.map(service => (
                <li key={service.id} className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  <span>{service.title}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate(categoryRoutes.financing)}>
              Access Financing Services
            </Button>
          </CardFooter>
        </Card>

        {/* Insurance Services */}
        <Card className="bg-card hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-2">
              {categoryIcons.insurance}
              <CardTitle>Insurance Services</CardTitle>
            </div>
            <CardDescription>Protect your agricultural business against risks and uncertainties</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {groupedServices.insurance?.map(service => (
                <li key={service.id} className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  <span>{service.title}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate(categoryRoutes.insurance)}>
              Access Insurance Services
            </Button>
          </CardFooter>
        </Card>

        {/* Land Leasing Services */}
        <Card className="bg-card hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-2">
              {categoryIcons.leasing}
              <CardTitle>Land Leasing Services</CardTitle>
            </div>
            <CardDescription>Find suitable land or list your land for lease to other farmers</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {groupedServices.leasing?.map(service => (
                <li key={service.id} className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  <span>{service.title}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate(categoryRoutes.leasing)}>
              Access Land Leasing Services
            </Button>
          </CardFooter>
        </Card>

        {/* Futures Acquisition */}
        <Card className="bg-card hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center gap-2">
              {categoryIcons.futures}
              <CardTitle>Futures Trading</CardTitle>
            </div>
            <CardDescription>Trade agricultural commodities futures and manage market risk</CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {groupedServices.futures?.map(service => (
                <li key={service.id} className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary"></div>
                  <span>{service.title}</span>
                </li>
              ))}
            </ul>
          </CardContent>
          <CardFooter>
            <Button onClick={() => navigate(categoryRoutes.futures)}>
              Access Futures Trading
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default ServicesIndex;
