import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

// Load environment variables from .env.test if it exists, otherwise from .env
dotenv.config({ path: '.env.test' });
if (!process.env.DATABASE_URL) {
  dotenv.config({ path: '.env' });
}

// Global setup
beforeAll(async () => {
  // Ensure we're not running tests against a production database
  if (process.env.NODE_ENV === 'production') {
    throw new Error('Tests should not run against production environment');
  }
  
  // Any global setup can go here
});

// Global teardown
afterAll(async () => {
  // Any global teardown can go here
});

// This will make Jest wait for all asynchronous operations to complete
// before considering a test complete
// @ts-ignore
jest.setTimeout(10000); // 10 second timeout 