import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PaymentMethodType } from "./PaymentMethodSelector";
import { Loader2, AlertCircle, HelpCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { DepositRequest, initiateDeposit, DepositResponse } from "@/services/walletService";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowUpRight, ArrowDownRight, Wallet, CreditCard } from "lucide-react";
import AssetDistributionCard from "@/components/dashboard/AssetDistributionCard";
import { commodities } from "@/data/commodityData"; // Assuming this holds UGX prices
import { formatCurrency } from "@/utils/currencyFormatter";

interface DepositFormsProps {
  method: PaymentMethodType;
  onDeposit: (amount: number) => void;
  onDepositInitiated?: (response: DepositResponse) => void;
}

export const DepositForms = ({ 
  method, 
  onDeposit,
  onDepositInitiated
}: DepositFormsProps) => {
  const [amount, setAmount] = useState<string>("");
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [cardNumber, setCardNumber] = useState<string>("");
  const [expiryDate, setExpiryDate] = useState<string>("");
  const [cvv, setCvv] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const { toast } = useToast();

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};
    
    // Validate amount
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      newErrors.amount = "Please enter a valid amount";
    } else if (parseFloat(amount) < 10) {
      newErrors.amount = "Minimum deposit amount is 10 UGX";
    }

    // Validate phone number for mobile money methods
    if ((method === "mtn" || method === "airtel" || method === "safaricom")) {
      if (!phoneNumber) {
        newErrors.phoneNumber = "Phone number is required";
      } else if (!/^\d{10,12}$/.test(phoneNumber.replace(/\D/g, ''))) {
        newErrors.phoneNumber = "Please enter a valid phone number";
      }
    }

    // Validate card details
    if (method === "visa" || method === "virtualCard") {
      if (!email) {
        newErrors.email = "Email is required";
      } else if (!/\S+@\S+\.\S+/.test(email)) {
        newErrors.email = "Please enter a valid email address";
      }
      
      if (method === "visa") {
        if (!cardNumber) {
          newErrors.cardNumber = "Card number is required";
        } else if (!/^\d{16}$/.test(cardNumber.replace(/\s/g, ''))) {
          newErrors.cardNumber = "Please enter a valid 16-digit card number";
        }
        
        if (!expiryDate) {
          newErrors.expiryDate = "Expiry date is required";
        } else if (!/^(0[1-9]|1[0-2])\/\d{2}$/.test(expiryDate)) {
          newErrors.expiryDate = "Please enter a valid expiry date (MM/YY)";
        }
        
        if (!cvv) {
          newErrors.cvv = "CVV is required";
        } else if (!/^\d{3,4}$/.test(cvv)) {
          newErrors.cvv = "Please enter a valid CVV";
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsProcessing(true);

    try {
      // Extract expiry month and year from expiry date
      let expiryMonth: string | undefined;
      let expiryYear: string | undefined;
      
      if (expiryDate) {
        const [month, year] = expiryDate.split('/');
        expiryMonth = month;
        expiryYear = year;
      }
      
      // Create deposit request
      const depositRequest: DepositRequest = {
        amount: parseFloat(amount),
        paymentMethod: method,
        payerDetails: {
          phoneNumber: (method === "mtn" || method === "airtel" || method === "safaricom") 
            ? phoneNumber 
            : undefined,
          email: (method === "visa" || method === "virtualCard") ? email : undefined,
          cardDetails: (method === "visa") ? {
            cardNumber: cardNumber.replace(/\s/g, ''),
            expiryMonth,
            expiryYear,
            cvv
          } : undefined
        }
      };
      
      // Call deposit service
      const response = await initiateDeposit(depositRequest);
      
      // If there's a payment URL for card payments, redirect to it
      if (response.paymentUrl && (method === "visa" || method === "virtualCard")) {
        window.location.href = response.paymentUrl;
        return;
      }
      
      // For mobile money, handle the response
      onDeposit(parseFloat(amount));
      
      if (onDepositInitiated) {
        onDepositInitiated(response);
      }
      
      // Show success message
      toast({
        title: "Deposit initiated",
        description: method === "visa" || method === "virtualCard" 
          ? "You will be redirected to complete your payment"
          : "Check your phone to complete the payment",
      });
      
      resetForm();
    } catch (error) {
      console.error("Deposit failed:", error);
      toast({
        title: "Deposit failed",
        description: "There was an error processing your deposit. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const resetForm = () => {
    setAmount("");
    setPhoneNumber("");
    setEmail("");
    setCardNumber("");
    setExpiryDate("");
    setCvv("");
    setErrors({});
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(' ');
    } else {
      return value;
    }
  };

  const renderMobileMoneyForm = () => {
    let providerName = "";
    switch(method) {
      case "mtn": providerName = "MTN Mobile Money"; break;
      case "airtel": providerName = "Airtel Money"; break;
      case "safaricom": providerName = "Safaricom M-Pesa"; break;
      default: providerName = "Mobile Money";
    }

    return (
      <>
        <div className="space-y-2 mb-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You will receive a prompt on your {providerName} phone to complete the payment.
            </AlertDescription>
          </Alert>
        </div>
        <div className="space-y-3">
          <div className="grid gap-2">
            <Label htmlFor="phoneNumber" className="flex items-center">
              Phone Number
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="h-3 w-3 ml-1 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-[200px] text-xs">Enter the phone number registered with {providerName} without spaces or special characters.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </Label>
            <Input
              id="phoneNumber"
              type="tel"
              placeholder={`Enter your ${providerName} number`}
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              className={errors.phoneNumber ? "border-down" : ""}
            />
            {errors.phoneNumber && (
              <p className="text-xs text-down">{errors.phoneNumber}</p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="amount" className="flex items-center">
              Amount (UGX)
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="h-3 w-3 ml-1 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-[200px] text-xs">Enter the amount you wish to deposit in Uganda Shillings (UGX).</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount to deposit"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              min="10"
              step="1"
              className={errors.amount ? "border-down" : ""}
            />
            {errors.amount && (
              <p className="text-xs text-down">{errors.amount}</p>
            )}
          </div>
        </div>
      </>
    );
  };

  const renderCardForm = () => {
    return (
      <div className="space-y-3">
        <div className="space-y-2 mb-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You will be redirected to a secure payment page to complete your transaction.
            </AlertDescription>
          </Alert>
        </div>

        <div className="grid gap-2">
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            placeholder="Enter your email address"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className={errors.email ? "border-down" : ""}
          />
          {errors.email && (
            <p className="text-xs text-down">{errors.email}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="amount" className="flex items-center">
            Amount (UGX)
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="h-3 w-3 ml-1 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="w-[200px] text-xs">Enter the amount you wish to deposit in Uganda Shillings (UGX).</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Label>
          <Input
            id="amount"
            type="number"
            placeholder="Enter amount to deposit"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            min="10"
            step="1"
            className={errors.amount ? "border-down" : ""}
          />
          {errors.amount && (
            <p className="text-xs text-down">{errors.amount}</p>
          )}
        </div>

        {method === "visa" && (
          <>
            <div className="grid gap-2">
              <Label htmlFor="cardNumber">Card Number</Label>
              <Input
                id="cardNumber"
                placeholder="Enter card number"
                value={cardNumber}
                onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
                maxLength={19}
                className={errors.cardNumber ? "border-down" : ""}
              />
              {errors.cardNumber && (
                <p className="text-xs text-down">{errors.cardNumber}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="expiryDate">Expiry Date</Label>
                <Input
                  id="expiryDate"
                  placeholder="MM/YY"
                  value={expiryDate}
                  onChange={(e) => {
                    let value = e.target.value;
                    // Add slash after month input
                    if (value.length === 2 && expiryDate.length === 1) {
                      value += '/';
                    }
                    // Limit to MM/YY format
                    if (value.length <= 5) {
                      setExpiryDate(value);
                    }
                  }}
                  maxLength={5}
                  className={errors.expiryDate ? "border-down" : ""}
                />
                {errors.expiryDate && (
                  <p className="text-xs text-down">{errors.expiryDate}</p>
                )}
              </div>

              <div className="grid gap-2">
                <Label htmlFor="cvv">CVV</Label>
                <Input
                  id="cvv"
                  type="password"
                  placeholder="CVV"
                  value={cvv}
                  onChange={(e) => {
                    const value = e.target.value.replace(/\D/g, '');
                    if (value.length <= 4) {
                      setCvv(value);
                    }
                  }}
                  maxLength={4}
                  className={errors.cvv ? "border-down" : ""}
                />
                {errors.cvv && (
                  <p className="text-xs text-down">{errors.cvv}</p>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  return (
    <form onSubmit={handleSubmit} className="mt-6">
      {(method === "mtn" || method === "airtel" || method === "safaricom") && renderMobileMoneyForm()}
      {(method === "visa" || method === "virtualCard") && renderCardForm()}

      <Button 
        type="submit" 
        className="w-full mt-6"
        disabled={isProcessing}
      >
        {isProcessing ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing...
          </>
        ) : method === "visa" || method === "virtualCard" ? (
          'Proceed to Payment'
        ) : (
          'Deposit Funds'
        )}
      </Button>
    </form>
  );
};
