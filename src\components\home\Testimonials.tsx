
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "Trader",
    content: "The tokenization system makes it easy to diversify our portfolio with fractional ownership.",
    avatar: "<PERSON>",
  },
  {
    name: "<PERSON>",
    role: "Farmer and Entrepreneur",
    content: "The trading engine is incredibly fast, and the real-time analytics provide valuable insights that have significantly improved my trading strategies.",
    avatar: "SW",
  },
  {
    name: "<PERSON>",
    role: "Analyst",
    content: "I appreciate the robust security measures. Multi-factor authentication and encryption give me confidence that my assets are well-protected.",
    avatar: "MC",
  },
];

const Testimonials = () => {
  return (
    <section className="py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold mb-3">What Our Users Say</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          F<PERSON><PERSON> A<PERSON> has revolutionized how we invest in commodities
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {testimonials.map((testimonial, index) => (
          <Card key={index} className="border border-border bg-card">
            <CardContent className="pt-6">
              <div className="mb-4">
                <svg
                  className="h-6 w-6 text-primary"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z"></path>
                </svg>
              </div>
              <p className="text-muted-foreground mb-6">{testimonial.content}</p>
              <div className="flex items-center">
                <Avatar className="h-10 w-10 mr-3">
                  <AvatarImage src="" />
                  <AvatarFallback className="bg-primary/20 text-primary">{testimonial.avatar}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">{testimonial.name}</div>
                  <div className="text-xs text-muted-foreground">{testimonial.role}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
};

export default Testimonials;
