import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PaymentMethodType } from "./PaymentMethodSelector";
import { Loader2, AlertCircle, HelpCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { WithdrawalRequest, initiateWithdrawal } from "@/services/walletService";

interface WithdrawFormProps {
  method: PaymentMethodType;
  maxAmount: number;
  onWithdraw: (amount: number) => void;
  onWithdrawalSubmitted: (response: any) => void;
}

export const WithdrawForms = ({ 
  method, 
  maxAmount, 
  onWithdraw, 
  onWithdrawalSubmitted 
}: WithdrawFormProps) => {
  const [amount, setAmount] = useState<string>("");
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [bankName, setBankName] = useState<string>("");
  const [accountNumber, setAccountNumber] = useState<string>("");
  const [accountName, setAccountName] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const { toast } = useToast();

  // Calculate fees and final amount
  const withdrawalAmount = parseFloat(amount) || 0;
  const fee = withdrawalAmount * 0.01; // 1% fee
  const receiveAmount = withdrawalAmount - fee;

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};
    
    // Validate amount
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      newErrors.amount = "Please enter a valid amount";
    } else if (parseFloat(amount) > maxAmount) {
      newErrors.amount = `You can withdraw a maximum of $${maxAmount.toFixed(2)}`;
    } else if (parseFloat(amount) < 10) {
      newErrors.amount = "Minimum withdrawal amount is $10";
    }

    // Validate phone number for mobile money methods
    if ((method === "mtn" || method === "airtel" || method === "safaricom")) {
      if (!phoneNumber) {
        newErrors.phoneNumber = "Phone number is required";
      } else if (!/^\d{10,12}$/.test(phoneNumber.replace(/\D/g, ''))) {
        newErrors.phoneNumber = "Please enter a valid phone number";
      }
    }

    // Validate bank account details
    if (method === "bankAccount") {
      if (!bankName) {
        newErrors.bankName = "Bank name is required";
      }
      if (!accountName) {
        newErrors.accountName = "Account holder name is required";
      }
      if (!accountNumber) {
        newErrors.accountNumber = "Account number is required";
      } else if (!/^\d{5,17}$/.test(accountNumber.replace(/\D/g, ''))) {
        newErrors.accountNumber = "Please enter a valid account number";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsProcessing(true);

    try {
      // Create the withdrawal request object
      const withdrawalRequest: WithdrawalRequest = {
        amount: parseFloat(amount),
        paymentMethod: method,
        recipientDetails: {
          phoneNumber: (method === "mtn" || method === "airtel" || method === "safaricom") 
            ? phoneNumber 
            : undefined,
          bankName: method === "bankAccount" ? bankName : undefined,
          accountNumber: method === "bankAccount" ? accountNumber : undefined,
          accountName: method === "bankAccount" ? accountName : undefined,
        }
      };

      // Call the API to initiate withdrawal
      const response = await initiateWithdrawal(withdrawalRequest);
      
      // Update the UI and pass the response to the parent
      onWithdraw(parseFloat(amount));
      onWithdrawalSubmitted(response);
      
      // Show success message
      toast({
        title: "Withdrawal initiated",
        description: `Your withdrawal of $${parseFloat(amount).toFixed(2)} has been initiated.`,
      });
      
      // Reset form
      resetForm();
    } catch (error) {
      console.error("Withdrawal failed:", error);
      toast({
        title: "Withdrawal failed",
        description: "There was an error processing your withdrawal. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const resetForm = () => {
    setAmount("");
    setPhoneNumber("");
    setBankName("");
    setAccountNumber("");
    setAccountName("");
    setErrors({});
  };

  const renderMobileMoneyForm = () => {
    let providerName = "";
    switch(method) {
      case "mtn": providerName = "MTN Mobile Money"; break;
      case "airtel": providerName = "Airtel Money"; break;
      case "safaricom": providerName = "Safaricom M-Pesa"; break;
      default: providerName = "Mobile Money";
    }

    return (
      <>
        <div className="space-y-2 mb-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Funds will be sent to your {providerName} account. A 1% withdrawal fee applies.
            </AlertDescription>
          </Alert>
        </div>
        <div className="space-y-3">
          <div className="grid gap-2">
            <Label htmlFor="phoneNumber" className="flex items-center">
              Phone Number
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="h-3 w-3 ml-1 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-[200px] text-xs">Enter the phone number registered with {providerName} without spaces or special characters.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </Label>
            <Input
              id="phoneNumber"
              type="tel"
              placeholder={`Enter your ${providerName} number`}
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              className={errors.phoneNumber ? "border-down" : ""}
            />
            {errors.phoneNumber && (
              <p className="text-xs text-down">{errors.phoneNumber}</p>
            )}
          </div>

          <div className="grid gap-2">
            <Label htmlFor="amount" className="flex items-center">
              Amount (USD)
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="h-3 w-3 ml-1 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="w-[200px] text-xs">Minimum withdrawal amount is $10. A 1% fee applies to all withdrawals.</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </Label>
            <Input
              id="amount"
              type="number"
              placeholder="Enter amount to withdraw"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              min="10"
              max={maxAmount.toString()}
              step="0.01"
              className={errors.amount ? "border-down" : ""}
            />
            {errors.amount ? (
              <p className="text-xs text-down">{errors.amount}</p>
            ) : (
              <p className="text-xs text-muted-foreground">Available balance: ${maxAmount.toFixed(2)}</p>
            )}
          </div>

          {/* Fee calculation display */}
          {withdrawalAmount > 0 && (
            <div className="mt-4 p-3 bg-card border border-border rounded-md">
              <div className="flex justify-between items-center text-sm mb-1">
                <span>Amount:</span>
                <span>${withdrawalAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center text-sm mb-1">
                <span>Fee (1%):</span>
                <span>${fee.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center text-sm font-medium border-t border-border pt-1 mt-1">
                <span>You will receive:</span>
                <span>${receiveAmount.toFixed(2)}</span>
              </div>
            </div>
          )}
        </div>
      </>
    );
  };

  const renderBankAccountForm = () => {
    return (
      <div className="space-y-3">
        <div className="grid gap-2">
          <Label htmlFor="bankName">Bank Name</Label>
          <Input
            id="bankName"
            placeholder="Enter bank name"
            value={bankName}
            onChange={(e) => setBankName(e.target.value)}
            className={errors.bankName ? "border-down" : ""}
          />
          {errors.bankName && (
            <p className="text-xs text-down">{errors.bankName}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="accountName">Account Holder Name</Label>
          <Input
            id="accountName"
            placeholder="Enter account holder name"
            value={accountName}
            onChange={(e) => setAccountName(e.target.value)}
            className={errors.accountName ? "border-down" : ""}
          />
          {errors.accountName && (
            <p className="text-xs text-down">{errors.accountName}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="accountNumber">Account Number</Label>
          <Input
            id="accountNumber"
            placeholder="Enter account number"
            value={accountNumber}
            onChange={(e) => setAccountNumber(e.target.value)}
            className={errors.accountNumber ? "border-down" : ""}
          />
          {errors.accountNumber && (
            <p className="text-xs text-down">{errors.accountNumber}</p>
          )}
        </div>

        <div className="grid gap-2">
          <Label htmlFor="amount" className="flex items-center">
            Amount (USD)
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="h-3 w-3 ml-1 text-muted-foreground" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="w-[200px] text-xs">Minimum withdrawal amount is $10. A 1% fee applies to all withdrawals.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Label>
          <Input
            id="amount"
            type="number"
            placeholder="Enter amount to withdraw"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
            min="10"
            max={maxAmount.toString()}
            step="0.01"
            className={errors.amount ? "border-down" : ""}
          />
          {errors.amount ? (
            <p className="text-xs text-down">{errors.amount}</p>
          ) : (
            <p className="text-xs text-muted-foreground">Available balance: ${maxAmount.toFixed(2)}</p>
          )}
        </div>

        {/* Fee calculation display */}
        {withdrawalAmount > 0 && (
          <div className="mt-4 p-3 bg-card border border-border rounded-md">
            <div className="flex justify-between items-center text-sm mb-1">
              <span>Amount:</span>
              <span>${withdrawalAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center text-sm mb-1">
              <span>Fee (1%):</span>
              <span>${fee.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center text-sm font-medium border-t border-border pt-1 mt-1">
              <span>You will receive:</span>
              <span>${receiveAmount.toFixed(2)}</span>
            </div>
          </div>
        )}

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Bank withdrawals typically take 1-3 business days to process. A 1% fee applies.
          </AlertDescription>
        </Alert>
      </div>
    );
  };

  return (
    <form onSubmit={handleSubmit} className="mt-6">
      {(method === "mtn" || method === "airtel" || method === "safaricom") && renderMobileMoneyForm()}
      {method === "bankAccount" && renderBankAccountForm()}

      <Button 
        type="submit" 
        className="w-full mt-6"
        disabled={isProcessing}
      >
        {isProcessing ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing Withdrawal...
          </>
        ) : (
          `Withdraw Funds`
        )}
      </Button>
    </form>
  );
};
