import React from 'react';
import { CheckCircle2, Clock, AlertCircle, FileText, ClipboardCheck, Calculator, BadgeCheck, Upload } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface LoanTrackerProps {
  applicationId: string;
  status: string;
  loanType: string;
  loanAmount: number;
  submissionDate: string;
}

const LoanTracker = ({
  applicationId,
  status,
  loanType,
  loanAmount,
  submissionDate
}: LoanTrackerProps) => {
  const steps = [
    { id: "submitted", label: "Application Submitted", icon: FileText, statuses: ["submitted", "under_review", "pending_documents", "credit_assessment", "approved", "rejected"] },
    { id: "under_review", label: "Under Review", icon: ClipboardCheck, statuses: ["under_review", "pending_documents", "credit_assessment", "approved", "rejected"] },
    { id: "pending_documents", label: "Document Verification", icon: Clock, statuses: ["pending_documents", "credit_assessment", "approved", "rejected"] },
    { id: "credit_assessment", label: "Credit Assessment", icon: Calculator, statuses: ["credit_assessment", "approved", "rejected"] },
    { id: "decision", label: "Final Decision", icon: BadgeCheck, statuses: ["approved", "rejected"] }
  ];

  const getStatusDetails = () => {
    switch (status) {
      case "draft":
        return {
          color: "text-amber-500",
          bgColor: "bg-amber-50",
          message: "Your application is in draft mode and has not been submitted yet."
        };
      case "submitted":
        return {
          color: "text-blue-500",
          bgColor: "bg-blue-50",
          message: "Your application has been submitted and is in the queue for review."
        };
      case "under_review":
        return {
          color: "text-blue-500",
          bgColor: "bg-blue-50",
          message: "Our team is currently reviewing your application and documentation."
        };
      case "pending_documents":
        return {
          color: "text-amber-500",
          bgColor: "bg-amber-50",
          message: "We need additional documents to proceed with your application."
        };
      case "credit_assessment":
        return {
          color: "text-purple-500",
          bgColor: "bg-purple-50",
          message: "Your credit assessment is in progress."
        };
      case "approved":
        return {
          color: "text-green-500",
          bgColor: "bg-green-50",
          message: "Congratulations! Your loan application has been approved."
        };
      case "rejected":
        return {
          color: "text-red-500",
          bgColor: "bg-red-50",
          message: "Unfortunately, your loan application has been declined."
        };
      default:
        return {
          color: "text-gray-500",
          bgColor: "bg-gray-50",
          message: "Application status is being updated."
        };
    }
  };

  const statusDetails = getStatusDetails();

  const getStepIcon = (step: (typeof steps)[0], currentStatus: string) => {
    const isActive = step.statuses.includes(currentStatus);
    const isCompleted = steps.findIndex(s => s.id === step.id) < steps.findIndex(s => s.statuses.includes(currentStatus));
    
    if (isCompleted) {
      return <CheckCircle2 className="h-6 w-6 text-green-500" />;
    } else if (isActive) {
      if (currentStatus === "rejected" && step.id === "decision") {
        return <AlertCircle className="h-6 w-6 text-red-500" />;
      }
      return <step.icon className={`h-6 w-6 ${statusDetails.color}`} />;
    } else {
      return <step.icon className="h-6 w-6 text-gray-300" />;
    }
  };

  return (
    <div className="space-y-6">
      <Card className={cn("border-l-4", 
        status === "approved" ? "border-l-green-500" : 
        status === "rejected" ? "border-l-red-500" : 
        "border-l-blue-500"
      )}>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h3 className="text-lg font-medium">Application #{applicationId}</h3>
              <p className="text-sm text-muted-foreground">Submitted on {submissionDate}</p>
            </div>
            <div className={cn("px-3 py-1 rounded-full text-sm font-medium", statusDetails.bgColor, statusDetails.color)}>
              {status === "under_review" ? "Under Review" : 
               status === "pending_documents" ? "Pending Documents" :
               status === "credit_assessment" ? "Credit Assessment" :
               status.charAt(0).toUpperCase() + status.slice(1)}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Loan Type</p>
              <p className="font-medium">{loanType}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Amount</p>
              <p className="font-medium">{loanAmount.toLocaleString()} UGX</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">Expected Decision</p>
              <p className="font-medium">3-5 business days</p>
            </div>
          </div>

          <p className="mt-4 text-sm p-3 rounded-md bg-muted">
            {statusDetails.message}
          </p>
        </CardContent>
      </Card>

      <div className="relative">
        <div className="absolute left-6 top-0 h-full w-0.5 bg-gray-200"></div>
        <div className="space-y-8">
          {steps.map((step, index) => (
            <div key={step.id} className="relative flex items-start gap-4">
              <div className="flex-shrink-0 flex items-center justify-center w-12 h-12 rounded-full bg-white border z-10">
                {getStepIcon(step, status)}
              </div>
              <div className="pt-1 space-y-1">
                <h4 className={cn(
                  "font-medium",
                  step.statuses.includes(status) ? "text-foreground" : "text-muted-foreground"
                )}>
                  {step.label}
                </h4>
                {status === "pending_documents" && step.id === "pending_documents" && (
                  <div className="mt-2 space-y-2">
                    <p className="text-sm text-muted-foreground">We need additional documents to proceed:</p>
                    <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground pl-2">
                      <li>Proof of income (last 3 months)</li>
                      <li>Business registration certificate</li>
                    </ul>
                    <Button size="sm" className="mt-2">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Documents
                    </Button>
                  </div>
                )}
                {status === "approved" && step.id === "decision" && (
                  <div className="mt-2">
                    <p className="text-sm text-green-600 mb-2">Your loan has been approved! Funds will be disbursed within 48 hours.</p>
                    <Button size="sm" variant="default">
                      View Loan Details
                    </Button>
                  </div>
                )}
                {status === "rejected" && step.id === "decision" && (
                  <div className="mt-2">
                    <p className="text-sm text-red-600 mb-2">Your application did not meet our current lending criteria.</p>
                    <Button size="sm" variant="outline">
                      Request Feedback
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LoanTracker;
