import { Request, Response, NextFunction } from 'express';
import { User } from '../../types/user';
import jwt from 'jsonwebtoken';
import { ApiError } from './errorHandler';
import config from '../config/env';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface JwtPayload {
  id: string;
  iat: number;
  exp: number;
}

export const authenticate = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new ApiError(401, 'Authentication required. Please login.');
    }
    
    const token = authHeader.split(' ')[1];
    
    // Verify token
    const decoded = jwt.verify(token, config.JWT_ACCESS_SECRET) as JwtPayload;
    
    // Find user by id
    const user = await prisma.user.findUnique({
      where: { id: decoded.id },
      select: {
        id: true,
        email: true,
        role: true,
        firstName: true,
        lastName: true,
        isEmailVerified: true,
        twoFactorEnabled: true
      }
    });
    
    if (!user) {
      return next(new ApiError(401, 'User not found'));
    }
    
    if (!user.isEmailVerified) {
      return next(new ApiError(401, 'User account is not verified'));
    }
  
    // Set user in request object
    req.user = user;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return next(new ApiError(401, 'Invalid or expired token'));
    }
    next(error);
  }
};

export const authorize = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new ApiError(401, 'Authentication required'));
    }
    
    if (!roles.includes(req.user.role as string)) {
      return next(new ApiError(403, 'You do not have permission to perform this action'));
    }
    
    next();
  };
}; 
