import React, { createContext, useState, useEffect, useContext } from 'react';
import { Transaction } from '@/pages/Wallet';

interface TransactionContextProps {
  transactions: Transaction[];
  setTransactions: React.Dispatch<React.SetStateAction<Transaction[]>>;
}

const TransactionContext = createContext<TransactionContextProps | undefined>(undefined);

export const TransactionProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);

  useEffect(() => {
    // Load transactions from local storage or an API here
    // For now, let's use a mock transaction
    const mockTransactions: Transaction[] = [
      { id: "TX001", type: "deposit", asset: "UGX", amount: 50000, value: 50000, status: "completed", date: new Date(Date.now() - 86400000 * 2).toISOString(), method: "MTN Mobile Money"},
      { id: "TX002", type: "buy", asset: "Coffee", amount: 2, price: 7500, value: 15000, status: "completed", date: new Date(Date.now() - 86400000 * 1).toISOString() },
      { id: "TX003", type: "withdraw", asset: "UGX", amount: 10000, value: 10000, status: "pending", date: new Date().toISOString(), method: "Airtel Money"},
      { id: "TX004", type: "deposit", asset: "UGX", amount: 25000, value: 25000, status: "completed", date: new Date(Date.now() - 86400000 * 3).toISOString(), method: "Safaricom M-PESA"},
    ];
    setTransactions(mockTransactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()));

    // TODO: Integrate real-time transaction data stream here
  }, []);

  return (
    <TransactionContext.Provider value={{ transactions, setTransactions }}>
      {children}
    </TransactionContext.Provider>
  );
};

export const useTransactionContext = () => {
  const context = useContext(TransactionContext);
  if (!context) {
    throw new Error('useTransactionContext must be used within a TransactionProvider');
  }
  return context;
};
