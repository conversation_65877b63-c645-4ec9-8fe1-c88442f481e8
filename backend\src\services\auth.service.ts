import jwt, { SignOptions, JwtPayload } from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import config from '../config/env';

const prisma = new PrismaClient();

export class AuthService {
  /**
   * Generate JWT tokens for user
   */
  generateTokens(user: { id: string; email: string; role: string }) {
    // Create JWT payload
    const payload = {
      id: user.id,
      email: user.email,
      role: user.role,
    };

    // Generate access token
    const accessToken = jwt.sign(
      payload,
      config.JWT_ACCESS_SECRET as jwt.Secret,
      { expiresIn: config.JWT_ACCESS_EXPIRY } as SignOptions
    );

    // Generate refresh token
    const refreshToken = jwt.sign(
      payload,
      config.JWT_REFRESH_SECRET as jwt.Secret,
      { expiresIn: config.JWT_REFRESH_EXPIRY } as SignOptions
    );

    return { accessToken, refreshToken };
  }

  /**
   * Verify JWT token
   */
  verifyToken(token: string, secret: string): JwtPayload {
    return jwt.verify(token, secret as jwt.Secret) as JwtPayload;
  }

  /**
   * Verify access token
   */
  verifyAccessToken(token: string): JwtPayload {
    return this.verifyToken(token, config.JWT_ACCESS_SECRET);
  }

  /**
   * Verify refresh token
   */
  verifyRefreshToken(token: string): JwtPayload {
    return this.verifyToken(token, config.JWT_REFRESH_SECRET);
  }
} 