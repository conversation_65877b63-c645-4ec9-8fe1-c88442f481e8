
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { LandPlot, Search, FileSearch, Calendar } from "lucide-react";
import { getServicesByCategory } from '@/data/servicesData';

// Mock land listings
const landListings = [
  { 
    id: "land-001", 
    title: "Fertile Farmland for Lease", 
    location: "Masaka District", 
    size: 10, 
    pricePerAcre: 600000, 
    term: 3, 
    soilType: "Loam",
    waterAccess: true,
    roadAccess: true,
    status: "Available",
    images: ["/placeholder.svg"],
    owner: {
      name: "John Muwanga",
      contact: "+256700123456",
      rating: 4.8
    }
  },
  { 
    id: "land-002", 
    title: "Coffee Plantation Land", 
    location: "Mbale District", 
    size: 5, 
    pricePerAcre: 750000, 
    term: 5, 
    soilType: "Volcanic",
    waterAccess: true,
    roadAccess: true,
    status: "Available",
    images: ["/placeholder.svg"],
    owner: {
      name: "Sarah Nakato",
      contact: "+256782987654",
      rating: 4.5
    }
  },
  { 
    id: "land-003", 
    title: "Grazing Land with Water Access", 
    location: "Mbarara District", 
    size: 15, 
    pricePerAcre: 450000, 
    term: 2, 
    soilType: "Clay",
    waterAccess: true,
    roadAccess: false,
    status: "Available",
    images: ["/placeholder.svg"],
    owner: {
      name: "Michael Kintu",
      contact: "+256754321098",
      rating: 4.2
    }
  },
];

// Mock user's leases
const userLeases = [
  { 
    id: "lease-101", 
    landId: "land-004", 
    title: "Cocoa Growing Land", 
    location: "Bundibugyo District", 
    size: 8, 
    totalPrice: 5600000,
    startDate: "2023-10-01",
    endDate: "2025-09-30",
    term: 2,
    nextPayment: {
      amount: 700000,
      dueDate: "2024-04-01"
    },
    status: "Active"
  },
];

const LeasingServices = () => {
  const [activeTab, setActiveTab] = useState("find");
  const leasingServices = getServicesByCategory("leasing");
  const [searchParams, setSearchParams] = useState({
    location: "",
    minSize: "",
    maxSize: "",
    soilType: "",
    hasWater: false
  });
  const [viewingLand, setViewingLand] = useState<(typeof landListings)[0] | null>(null);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, would filter results based on searchParams
    console.log("Searching with params:", searchParams);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <LandPlot className="h-8 w-8 mr-2 text-primary" />
        <h1 className="text-3xl font-bold">Land Leasing Services</h1>
      </div>
      
      <p className="text-muted-foreground mb-8">
        Connect with landowners to lease farmland or list your land for lease to other farmers.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {leasingServices.map((service) => (
          <Card key={service.id} className="bg-card hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle>{service.title}</CardTitle>
              <CardDescription>{service.description}</CardDescription>
            </CardHeader>
            <CardFooter>
              <Button variant="outline" onClick={() => setActiveTab(service.id === "land-listing" ? "find" : "manage")}>
                {service.id === "land-listing" ? "Find Land" : "Manage Leases"}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="find" className="flex items-center gap-2">
            <Search className="h-4 w-4" /> Find Land
          </TabsTrigger>
          <TabsTrigger value="manage" className="flex items-center gap-2">
            <FileSearch className="h-4 w-4" /> Manage Leases
          </TabsTrigger>
          <TabsTrigger value="list" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" /> List Your Land
          </TabsTrigger>
        </TabsList>

        {/* Find Land Tab */}
        <TabsContent value="find">
          <Card>
            <CardHeader>
              <CardTitle>Available Land for Lease</CardTitle>
              <CardDescription>Find suitable farmland for your agricultural activities</CardDescription>
            </CardHeader>
            <CardContent>
              {!viewingLand ? (
                <>
                  <form onSubmit={handleSearch} className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div>
                      <Label htmlFor="location">Location</Label>
                      <Input 
                        id="location" 
                        placeholder="District or region" 
                        value={searchParams.location}
                        onChange={e => setSearchParams({...searchParams, location: e.target.value})}
                      />
                    </div>
                    <div>
                      <Label htmlFor="size">Land Size (acres)</Label>
                      <div className="flex gap-2">
                        <Input 
                          id="minSize" 
                          placeholder="Min" 
                          type="number"
                          value={searchParams.minSize}
                          onChange={e => setSearchParams({...searchParams, minSize: e.target.value})}
                        />
                        <Input 
                          id="maxSize" 
                          placeholder="Max" 
                          type="number"
                          value={searchParams.maxSize}
                          onChange={e => setSearchParams({...searchParams, maxSize: e.target.value})}
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="soilType">Soil Type</Label>
                      <Select 
                        value={searchParams.soilType}
                        onValueChange={value => setSearchParams({...searchParams, soilType: value})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Any soil type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">Any soil type</SelectItem>
                          <SelectItem value="loam">Loam</SelectItem>
                          <SelectItem value="clay">Clay</SelectItem>
                          <SelectItem value="sandy">Sandy</SelectItem>
                          <SelectItem value="volcanic">Volcanic</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-center space-x-2 md:col-span-2">
                      <Checkbox 
                        id="hasWater" 
                        checked={searchParams.hasWater}
                        onCheckedChange={checked => setSearchParams({...searchParams, hasWater: checked as boolean})}
                      />
                      <Label htmlFor="hasWater">Has water source (river, well, borehole)</Label>
                    </div>
                    <div>
                      <Button type="submit" className="w-full">Search</Button>
                    </div>
                  </form>

                  <div className="space-y-6">
                    {landListings.map(land => (
                      <div key={land.id} className="border rounded-lg overflow-hidden flex flex-col md:flex-row">
                        <div className="w-full md:w-1/3 h-48 bg-muted">
                          <img src={land.images[0]} alt={land.title} className="w-full h-full object-cover" />
                        </div>
                        <div className="flex-1 p-4 md:p-6">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-semibold text-lg">{land.title}</h3>
                              <p className="text-muted-foreground">{land.location}</p>
                            </div>
                            <Badge variant={land.status === "Available" ? "outline" : "secondary"}>
                              {land.status}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Size:</span>
                              <span className="ml-1 font-medium">{land.size} acres</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Price:</span>
                              <span className="ml-1 font-medium">{land.pricePerAcre.toLocaleString()} UGX/acre/year</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Lease Term:</span>
                              <span className="ml-1 font-medium">Up to {land.term} years</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Soil Type:</span>
                              <span className="ml-1 font-medium">{land.soilType}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Water:</span>
                              <span className="ml-1 font-medium">{land.waterAccess ? "Available" : "Not available"}</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Road Access:</span>
                              <span className="ml-1 font-medium">{land.roadAccess ? "Yes" : "No"}</span>
                            </div>
                          </div>
                          
                          <div className="flex justify-between items-center mt-4">
                            <div className="text-sm">
                              <span className="text-muted-foreground">Owner:</span>
                              <span className="ml-1">{land.owner.name} ({land.owner.rating}★)</span>
                            </div>
                            <Button onClick={() => setViewingLand(land)}>View Details</Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div>
                  <Button variant="ghost" onClick={() => setViewingLand(null)} className="mb-4">
                    ← Back to Listings
                  </Button>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <div className="aspect-video bg-muted rounded-lg mb-4">
                        <img src={viewingLand.images[0]} alt={viewingLand.title} className="w-full h-full object-cover rounded-lg" />
                      </div>
                      
                      <h2 className="text-2xl font-bold mb-2">{viewingLand.title}</h2>
                      <p className="text-muted-foreground mb-4">{viewingLand.location}</p>
                      
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="bg-muted p-3 rounded-lg">
                            <div className="text-sm text-muted-foreground">Land Size</div>
                            <div className="font-semibold">{viewingLand.size} acres</div>
                          </div>
                          <div className="bg-muted p-3 rounded-lg">
                            <div className="text-sm text-muted-foreground">Price per Acre</div>
                            <div className="font-semibold">{viewingLand.pricePerAcre.toLocaleString()} UGX/year</div>
                          </div>
                          <div className="bg-muted p-3 rounded-lg">
                            <div className="text-sm text-muted-foreground">Total Annual Cost</div>
                            <div className="font-semibold">{(viewingLand.size * viewingLand.pricePerAcre).toLocaleString()} UGX</div>
                          </div>
                          <div className="bg-muted p-3 rounded-lg">
                            <div className="text-sm text-muted-foreground">Maximum Term</div>
                            <div className="font-semibold">{viewingLand.term} years</div>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <h3 className="font-medium">Land Features</h3>
                          <div className="grid grid-cols-2 gap-2 text-sm">
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${viewingLand.waterAccess ? "bg-green-500" : "bg-red-500"}`}></div>
                              <span>Water Source</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className={`w-3 h-3 rounded-full ${viewingLand.roadAccess ? "bg-green-500" : "bg-red-500"}`}></div>
                              <span>Road Access</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-3 h-3 rounded-full bg-green-500"></div>
                              <span>Soil Type: {viewingLand.soilType}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <h3 className="font-medium">About the Owner</h3>
                          <div className="bg-muted p-3 rounded-lg">
                            <div className="flex justify-between items-center">
                              <div>
                                <div className="font-medium">{viewingLand.owner.name}</div>
                                <div className="text-sm text-muted-foreground">{viewingLand.owner.contact}</div>
                              </div>
                              <div className="flex items-center gap-1">
                                <span className="font-bold">{viewingLand.owner.rating}</span>
                                <span className="text-yellow-500">★</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-muted p-6 rounded-lg space-y-6">
                      <h3 className="text-lg font-medium">Lease this Land</h3>
                      
                      <div className="space-y-4">
                        <div>
                          <Label htmlFor="lease-term">Lease Term (years)</Label>
                          <Select defaultValue="1">
                            <SelectTrigger>
                              <SelectValue placeholder="Select lease term" />
                            </SelectTrigger>
                            <SelectContent>
                              {Array.from({length: viewingLand.term}, (_, i) => i + 1).map(year => (
                                <SelectItem key={year} value={year.toString()}>{year} {year === 1 ? 'year' : 'years'}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <Label htmlFor="start-date">Desired Start Date</Label>
                          <Input type="date" id="start-date" />
                        </div>
                        
                        <div>
                          <Label htmlFor="payment-schedule">Payment Schedule</Label>
                          <Select defaultValue="annual">
                            <SelectTrigger>
                              <SelectValue placeholder="Select payment schedule" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="annual">Annual (once per year)</SelectItem>
                              <SelectItem value="biannual">Bi-annual (twice per year)</SelectItem>
                              <SelectItem value="quarterly">Quarterly (four times per year)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <Label htmlFor="message">Message to Landowner</Label>
                          <Textarea 
                            id="message"
                            placeholder="Introduce yourself and explain your farming plans for this land"
                            className="resize-none min-h-[100px]"
                          />
                        </div>
                      </div>
                      
                      <div className="border-t pt-4 space-y-2">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Annual Payment:</span>
                          <span className="font-medium">{(viewingLand.size * viewingLand.pricePerAcre).toLocaleString()} UGX</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Security Deposit:</span>
                          <span className="font-medium">{(viewingLand.size * viewingLand.pricePerAcre * 0.2).toLocaleString()} UGX</span>
                        </div>
                        <div className="flex justify-between border-t pt-2">
                          <span className="font-semibold">Initial Payment:</span>
                          <span className="font-semibold">{(viewingLand.size * viewingLand.pricePerAcre * 1.2).toLocaleString()} UGX</span>
                        </div>
                      </div>
                      
                      <Button className="w-full">Submit Lease Request</Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Manage Leases Tab */}
        <TabsContent value="manage">
          <Card>
            <CardHeader>
              <CardTitle>Your Active Leases</CardTitle>
              <CardDescription>Manage your land lease agreements</CardDescription>
            </CardHeader>
            <CardContent>
              {userLeases.length > 0 ? (
                <div className="space-y-6">
                  {userLeases.map(lease => (
                    <Card key={lease.id} className="bg-card border">
                      <CardHeader>
                        <div className="flex justify-between items-center">
                          <CardTitle>{lease.title}</CardTitle>
                          <Badge>{lease.status}</Badge>
                        </div>
                        <CardDescription>
                          {lease.location} | {lease.size} acres
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <span className="text-muted-foreground block">Lease Period:</span>
                              <span className="font-medium">
                                {lease.startDate} to {lease.endDate}
                              </span>
                            </div>
                            <div>
                              <span className="text-muted-foreground block">Lease Term:</span>
                              <span className="font-medium">{lease.term} years</span>
                            </div>
                            <div>
                              <span className="text-muted-foreground block">Annual Cost:</span>
                              <span className="font-medium">
                                {(lease.totalPrice / lease.term).toLocaleString()} UGX
                              </span>
                            </div>
                            <div>
                              <span className="text-muted-foreground block">Next Payment:</span>
                              <span className="font-medium">
                                {lease.nextPayment.amount.toLocaleString()} UGX ({lease.nextPayment.dueDate})
                              </span>
                            </div>
                          </div>
                          
                          <div className="rounded-lg bg-primary/5 border border-primary/20 p-4">
                            <h4 className="font-medium mb-2 text-primary">Important Dates</h4>
                            <div className="grid grid-cols-1 gap-2 text-sm">
                              <div className="flex justify-between">
                                <span>Next Payment Due:</span>
                                <span className="font-medium">{lease.nextPayment.dueDate}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Lease Renewal Deadline:</span>
                                <span className="font-medium">
                                  {new Date(new Date(lease.endDate).getTime() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span>Lease Expiry:</span>
                                <span className="font-medium">{lease.endDate}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between">
                        <Button variant="outline">View Agreement</Button>
                        <div className="flex gap-2">
                          <Button variant="outline">Report Issue</Button>
                          <Button>Make Payment</Button>
                        </div>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="mx-auto w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
                    <FileSearch className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No Active Leases</h3>
                  <p className="text-muted-foreground mb-4">
                    You don't have any active land leases at the moment.
                  </p>
                  <Button onClick={() => setActiveTab("find")}>Find Land to Lease</Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* List Your Land Tab */}
        <TabsContent value="list">
          <Card>
            <CardHeader>
              <CardTitle>List Your Land for Lease</CardTitle>
              <CardDescription>Make your farmland available to other farmers</CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="list-title">Listing Title</Label>
                      <Input id="list-title" placeholder="E.g., Fertile Farmland for Coffee Growing" />
                    </div>
                    
                    <div>
                      <Label htmlFor="list-location">Location</Label>
                      <Input id="list-location" placeholder="District, region, or nearest town" />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="list-size">Land Size (acres)</Label>
                        <Input id="list-size" type="number" min="0.1" step="0.1" />
                      </div>
                      <div>
                        <Label htmlFor="list-price">Price per Acre/Year (UGX)</Label>
                        <Input id="list-price" type="number" min="1000" />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="list-max-term">Maximum Lease Term (years)</Label>
                      <Select defaultValue="3">
                        <SelectTrigger>
                          <SelectValue placeholder="Select maximum lease term" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 year</SelectItem>
                          <SelectItem value="2">2 years</SelectItem>
                          <SelectItem value="3">3 years</SelectItem>
                          <SelectItem value="5">5 years</SelectItem>
                          <SelectItem value="7">7 years</SelectItem>
                          <SelectItem value="10">10 years</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label htmlFor="list-soil">Soil Type</Label>
                      <Select defaultValue="">
                        <SelectTrigger>
                          <SelectValue placeholder="Select soil type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="loam">Loam</SelectItem>
                          <SelectItem value="clay">Clay</SelectItem>
                          <SelectItem value="sandy">Sandy</SelectItem>
                          <SelectItem value="volcanic">Volcanic</SelectItem>
                          <SelectItem value="silty">Silty</SelectItem>
                          <SelectItem value="chalky">Chalky</SelectItem>
                          <SelectItem value="peaty">Peaty</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Land Features</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="water-access" />
                          <Label htmlFor="water-access">Access to water source</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="road-access" />
                          <Label htmlFor="road-access">Road access</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="electricity" />
                          <Label htmlFor="electricity">Electricity connection</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="structures" />
                          <Label htmlFor="structures">Existing structures or facilities</Label>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="list-description">Land Description</Label>
                      <Textarea 
                        id="list-description" 
                        placeholder="Describe your land's features, history, and suitability for different crops" 
                        className="resize-none min-h-[150px]"
                      />
                    </div>
                    
                    <div>
                      <Label>Land Images</Label>
                      <div className="border border-dashed rounded-lg p-6 text-center">
                        <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center mb-2">
                          <LandPlot className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          Upload clear images of your land
                        </p>
                        <Button variant="outline" size="sm">Upload Images</Button>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Lease Terms and Conditions</Label>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="allowed-crops" />
                          <Label htmlFor="allowed-crops">Specify allowed crops or activities</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="payment-schedule" defaultChecked />
                          <Label htmlFor="payment-schedule">Allow flexible payment schedules</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="fencing" />
                          <Label htmlFor="fencing">Require fencing or land protection</Label>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="list-additional">Additional Requirements</Label>
                      <Textarea 
                        id="list-additional" 
                        placeholder="Any additional requirements or restrictions for potential lessees" 
                        className="resize-none min-h-[100px]"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-between pt-4 border-t">
                  <Button variant="outline">Save as Draft</Button>
                  <Button>Publish Listing</Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LeasingServices;
