import express, { Request, Response } from 'express';
import authRoutes from './routes/auth.routes';
import walletRoutes from './routes/wallet.routes';
import paymentRoutes from './routes/payment.routes';
import commoditiesRoutes from './routes/commodities.routes';
// Import other routes as they are developed
// import userRoutes from './routes/user.routes';
// import transportRoutes from './routes/transport.routes';
// import warehouseRoutes from './routes/warehouse.routes';
// import insuranceRoutes from './routes/insurance.routes';
// import loanRoutes from './routes/loan.routes';

const router = express.Router();

// API version prefix
const API_VERSION = '/v1';

// Health check endpoint for API specifically
router.get('/', (req: Request, res: Response) => {
  res.json({
    status: 'success',
    message: 'Fotis Agro Trading API',
    version: '1.0.0',
  });
});

// Register routes
router.use(`${API_VERSION}/auth`, authRoutes);
router.use(`${API_VERSION}/wallet`, walletRoutes);
router.use(`${API_VERSION}/payments`, paymentRoutes);
router.use(`${API_VERSION}/commodities`, commoditiesRoutes);
// Register other routes as they are developed
// router.use(`${API_VERSION}/users`, userRoutes);
// router.use(`${API_VERSION}/transport`, transportRoutes);
// router.use(`${API_VERSION}/warehouse`, warehouseRoutes);
// router.use(`${API_VERSION}/insurance`, insuranceRoutes);
// router.use(`${API_VERSION}/loans`, loanRoutes);

export default router; 