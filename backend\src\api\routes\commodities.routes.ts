import express from 'express';
import { CommoditiesController } from '../../controllers/commodities.controller';
import { authenticate } from '../../middlewares/auth';

const router = express.Router();
const commoditiesController = new CommoditiesController();

/**
 * @route GET /api/commodities
 * @desc Get list of commodities
 * @access Private
 */
router.get(
  '/',
  authenticate,
  commoditiesController.getCommodities
);

export default router; 