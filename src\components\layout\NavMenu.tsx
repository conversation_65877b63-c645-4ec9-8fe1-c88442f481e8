
import React from 'react';
import { NavLink } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown, Warehouse, PiggyBank, ShieldCheck, LandPlot, BarChart3, Truck, Tractor, Leaf, Users } from 'lucide-react';

interface NavMenuProps {
  mobile?: boolean;
}

const NavMenu = ({ mobile = false }: NavMenuProps) => {
  const linkClass = (isActive: boolean) =>
    cn(
      'flex items-center px-3 py-2 rounded-md text-sm transition',
      isActive
        ? 'bg-primary/10 text-primary font-medium'
        : 'text-foreground/70 hover:text-primary hover:bg-primary/5'
    );

  const menuLinks = [
    { name: 'Dashboard', path: '/dashboard' },
    { name: 'Trade', path: '/trade' },
    { name: 'Wallet', path: '/wallet' },
  ];

  const serviceLinks = [
    { name: 'All Services', path: '/services', icon: null },
    { name: 'Transport & Logistics', path: '/services/transportation', icon: <Truck size={16} className="mr-2" /> },
    { name: 'Hire a Tractor', path: '/services/equipment', icon: <Tractor size={16} className="mr-2" /> },
    { name: 'Warehousing', path: '/services/warehousing', icon: <Warehouse size={16} className="mr-2" /> },
    { name: 'Financing', path: '/services/financing', icon: <PiggyBank size={16} className="mr-2" /> },
    { name: 'Insurance', path: '/services/insurance', icon: <ShieldCheck size={16} className="mr-2" /> },
    { name: 'Land Leasing', path: '/services/leasing', icon: <LandPlot size={16} className="mr-2" /> },
    { name: 'Agronomy Services', path: '/services/agronomy', icon: <Leaf size={16} className="mr-2" /> },
    { name: 'Futures Trading', path: '/services/futures', icon: <BarChart3 size={16} className="mr-2" /> },
  ];

  const aboutLinks = [
    { name: 'About Us', path: '/about-us' },
    { name: 'Careers', path: '/careers' },
    { name: 'Contact Us', path: '/contact' },
  ];

  return (
    <div className={cn('flex', mobile ? 'flex-col w-full gap-1' : 'items-center gap-1')}>
      {/* Services dropdown - moved to first position */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            className={cn(
              'flex items-center px-3 py-2 rounded-md text-sm transition gap-1',
              mobile ? 'justify-start w-full' : ''
            )}
          >
            Services <ChevronDown size={14} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align={mobile ? "start" : "end"} className="w-60">
          {serviceLinks.map((link, index) => (
            <React.Fragment key={link.path}>
              {index === 1 && <DropdownMenuSeparator />}
              <DropdownMenuItem asChild>
                <NavLink to={link.path} className="cursor-pointer w-full flex items-center">
                  {link.icon}
                  {link.name}
                </NavLink>
              </DropdownMenuItem>
            </React.Fragment>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
      
      {/* Other menu links */}
      {menuLinks.map((link) => (
        <NavLink
          key={link.path}
          to={link.path}
          className={({ isActive }) => linkClass(isActive)}
        >
          {link.name}
        </NavLink>
      ))}
      
      {/* About dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            className={cn(
              'flex items-center px-3 py-2 rounded-md text-sm transition gap-1',
              mobile ? 'justify-start w-full' : ''
            )}
          >
            About <ChevronDown size={14} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align={mobile ? "start" : "end"} className="w-40">
          {aboutLinks.map((link) => (
            <DropdownMenuItem key={link.path} asChild>
              <NavLink to={link.path} className="cursor-pointer w-full">
                {link.name}
              </NavLink>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default NavMenu;
