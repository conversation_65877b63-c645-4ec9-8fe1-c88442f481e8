{"name": "fotis-agro-trading-api", "version": "1.0.0", "description": "Backend API for Fotis Agro Trading Platform", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon", "build": "tsc", "lint": "eslint . --ext .ts", "test": "jest", "format": "prettier --write \"src/**/*.ts\"", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:seed": "ts-node scripts/init-database.ts", "db:reset": "prisma migrate reset --force", "prepare": "prisma generate"}, "keywords": ["agriculture", "trading", "api", "express", "typescript", "prisma"], "author": "Fotis Agro Trading", "license": "MIT", "dependencies": {"@prisma/client": "^5.0.0", "@types/helmet": "^0.0.48", "bcrypt": "^5.1.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "helmet": "^7.2.0", "joi": "^17.9.2", "jsonwebtoken": "^9.0.1", "morgan": "^1.10.0", "nodemailer": "^6.9.4", "stripe": "^12.16.0", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/compression": "^1.8.0", "@types/cors": "^2.8.18", "@types/express": "^4.17.22", "@types/jest": "^29.5.3", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.9", "@types/node": "^20.17.50", "@types/nodemailer": "^6.4.9", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.46.0", "jest": "^29.6.2", "nodemon": "^3.1.10", "prisma": "^5.0.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}