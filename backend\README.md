# Fotis Agro Trading API

Backend API for the Fotis Agro Trading Platform, providing RESTful endpoints for user management, wallet operations, transportation booking, warehouse management, and more.

## Technology Stack

- **Node.js**: JavaScript runtime
- **Express**: Web framework
- **TypeScript**: Type-safe JavaScript
- **PostgreSQL**: Relational database
- **Prisma**: ORM for database operations
- **JWT**: Authentication and authorization
- **Jest**: Testing framework

## Prerequisites

- Node.js (v18.0.0 or higher)
- npm or yarn
- PostgreSQL (v14 or higher)

## Getting Started

### Installation

1. Clone the repository
2. Navigate to the backend directory
3. Install dependencies

```bash
cd backend
npm install
```

### Configuration

1. Create a `.env` file in the root directory based on the `.env.example` template
2. Update the environment variables with your configuration

Key variables to configure:
- `DATABASE_URL`: Your PostgreSQL connection string
- `JWT_ACCESS_SECRET` and `JWT_REFRESH_SECRET`: Secure random strings for JWT signing
- `STRIPE_SECRET_KEY` and other payment gateway credentials (if using payment features)
- SMTP settings for email functionality

### Database Setup

1. Make sure your PostgreSQL server is running
2. Create a database for the application
3. Run Prisma migrations to set up the database schema

```bash
npx prisma migrate dev
```

### Running the Server

#### Development Mode

```bash
npm run dev
```

This will start the server with hot reloading using nodemon.

#### Production Mode

```bash
npm run build
npm start
```

### API Documentation

Once the server is running, API documentation can be accessed at:

- REST API Documentation: `http://localhost:5000/api-docs`

## Project Structure

```
backend/
├── prisma/                 # Prisma schema and migrations
├── src/
│   ├── api/                # API routes
│   │   ├── routes/         # Route definitions
│   │   └── index.ts        # API router setup
│   ├── controllers/        # Request handlers
│   ├── services/           # Business logic
│   ├── middlewares/        # Express middlewares
│   ├── utils/              # Utility functions
│   ├── validators/         # Request validation schemas
│   ├── types/              # TypeScript type definitions
│   ├── config/             # Configuration files
│   └── server.ts           # Express application setup
├── tests/                  # Test files
├── .env                    # Environment variables
├── package.json            # Project dependencies
└── tsconfig.json           # TypeScript configuration
```

## Features

- Authentication & Authorization
  - User registration and login
  - JWT-based authentication
  - Role-based access control

- Wallet Management
  - Deposit and withdraw funds
  - Transaction history
  - Balance checks

- Payment Processing
  - Credit/debit card payments via Stripe
  - Mobile money integration (MTN, Airtel)
  - Payment status webhooks

- Data Validation
  - Input validation using Joi
  - Error handling middleware

- Security
  - Password hashing with bcrypt
  - CORS protection
  - Helmet security headers

## Testing

```bash
npm test
```

## Deployment

For production deployment, we recommend:

1. Setting up a CI/CD pipeline
2. Running database migrations as part of deployment
3. Using environment-specific configuration files
4. Implementing proper logging and monitoring

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License

[MIT License](LICENSE) 