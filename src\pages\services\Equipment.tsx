
import React, { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>oot<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { getServicesByCategory } from "@/data/servicesData";
import { Tractor, Calendar as CalendarIcon, MapPin, Clock, DollarSign } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

type EquipmentType = {
  id: string;
  name: string;
  description: string;
  dailyRate: number;
  weeklyRate: number;
  monthlyRate: number;
  image: string;
  availability: "available" | "limited" | "unavailable";
  location: string;
  brand: string;
  rating: number;
  reviewCount: number;
};

const equipmentData: EquipmentType[] = [
  {
    id: "tractor-1",
    name: "John Deere 6155M",
    description: "155 HP Utility Tractor with premium cab and advanced features",
    dailyRate: 250,
    weeklyRate: 1500,
    monthlyRate: 5000,
    image: "https://picsum.photos/seed/tractor1/300/200",
    availability: "available",
    location: "Central Agricultural Hub",
    brand: "John Deere",
    rating: 4.8,
    reviewCount: 24,
  },
  {
    id: "tractor-2",
    name: "Massey Ferguson 8S.265",
    description: "265 HP Tractor with excellent fuel efficiency and comfort",
    dailyRate: 300,
    weeklyRate: 1800,
    monthlyRate: 6000,
    image: "https://picsum.photos/seed/tractor2/300/200",
    availability: "limited",
    location: "Eastern Farm District",
    brand: "Massey Ferguson",
    rating: 4.6,
    reviewCount: 18,
  },
  {
    id: "digger-1",
    name: "Caterpillar 320 Excavator",
    description: "20-ton hydraulic excavator for heavy-duty digging operations",
    dailyRate: 400,
    weeklyRate: 2400,
    monthlyRate: 8000,
    image: "https://picsum.photos/seed/digger1/300/200",
    availability: "available",
    location: "Southern Agricultural Zone",
    brand: "Caterpillar",
    rating: 4.9,
    reviewCount: 32,
  },
  {
    id: "irrigation-1",
    name: "Valley Center Pivot",
    description: "Complete irrigation system for large field coverage",
    dailyRate: 180,
    weeklyRate: 1000,
    monthlyRate: 3500,
    image: "https://picsum.photos/seed/irrigation1/300/200",
    availability: "available",
    location: "Western Irrigation Center",
    brand: "Valley",
    rating: 4.7,
    reviewCount: 15,
  }
];

const Equipment = () => {
  const equipmentServices = getServicesByCategory("equipment");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [duration, setDuration] = useState("day");
  const [location, setLocation] = useState("");
  const [selectedEquipment, setSelectedEquipment] = useState<string | null>(null);
  
  const handleBooking = (equipmentId: string) => {
    setSelectedEquipment(equipmentId);
    // In a real app, we would handle the booking process here
    console.log(`Booking equipment ${equipmentId} for ${duration} on ${selectedDate}`);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex flex-col space-y-2 mb-8">
        <h1 className="text-3xl font-bold">Hire a Tractor</h1>
        <p className="text-muted-foreground">
          Lease agricultural machinery and equipment for your farming operations
        </p>
      </div>

      {/* Search and Filter Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="text-xl">Find Available Equipment</CardTitle>
          <CardDescription>
            Search for equipment based on your requirements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <div className="flex items-center">
                <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
                <Input 
                  id="location" 
                  placeholder="Search location" 
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Start Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !selectedDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? format(selectedDate, "PPP") : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label>Duration</Label>
              <div className="flex items-center">
                <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                <select 
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2"
                  value={duration}
                  onChange={(e) => setDuration(e.target.value)}
                >
                  <option value="day">Daily</option>
                  <option value="week">Weekly</option>
                  <option value="month">Monthly</option>
                </select>
              </div>
            </div>

            <div className="flex items-end">
              <Button className="w-full">Search Equipment</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Equipment Categories */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Equipment Categories</h2>
        <Tabs defaultValue="all">
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Equipment</TabsTrigger>
            <TabsTrigger value="tractors">Tractors</TabsTrigger>
            <TabsTrigger value="heavy">Heavy Machinery</TabsTrigger>
            <TabsTrigger value="irrigation">Irrigation</TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {equipmentData.map((equipment) => (
                <EquipmentCard 
                  key={equipment.id} 
                  equipment={equipment} 
                  duration={duration}
                  onBook={() => handleBooking(equipment.id)} 
                />
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="tractors" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {equipmentData
                .filter(e => e.id.includes('tractor'))
                .map((equipment) => (
                  <EquipmentCard 
                    key={equipment.id} 
                    equipment={equipment} 
                    duration={duration}
                    onBook={() => handleBooking(equipment.id)} 
                  />
                ))}
            </div>
          </TabsContent>

          {/* Similar content for other tabs */}
          <TabsContent value="heavy" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {equipmentData
                .filter(e => e.id.includes('digger'))
                .map((equipment) => (
                  <EquipmentCard 
                    key={equipment.id} 
                    equipment={equipment} 
                    duration={duration}
                    onBook={() => handleBooking(equipment.id)} 
                  />
                ))}
            </div>
          </TabsContent>

          <TabsContent value="irrigation" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {equipmentData
                .filter(e => e.id.includes('irrigation'))
                .map((equipment) => (
                  <EquipmentCard 
                    key={equipment.id} 
                    equipment={equipment} 
                    duration={duration}
                    onBook={() => handleBooking(equipment.id)} 
                  />
                ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Services Overview */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Our Equipment Services</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {equipmentServices.map((service) => (
            <Card key={service.id} className="bg-card hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle>{service.title}</CardTitle>
                <CardDescription>{service.description}</CardDescription>
              </CardHeader>
              <CardFooter>
                <Button variant="outline" className="w-full">
                  Learn More
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

interface EquipmentCardProps {
  equipment: EquipmentType;
  duration: string;
  onBook: () => void;
}

const EquipmentCard = ({ equipment, duration, onBook }: EquipmentCardProps) => {
  const rate = duration === 'day' 
    ? equipment.dailyRate 
    : duration === 'week' 
      ? equipment.weeklyRate 
      : equipment.monthlyRate;

  return (
    <Card className="overflow-hidden">
      <div className="relative h-48">
        <img 
          src={equipment.image} 
          alt={equipment.name} 
          className="w-full h-full object-cover" 
        />
        <Badge 
          className={cn(
            "absolute top-2 right-2",
            equipment.availability === "available" ? "bg-green-500" : 
            equipment.availability === "limited" ? "bg-yellow-500" : "bg-red-500"
          )}
        >
          {equipment.availability === "available" ? "Available" : 
           equipment.availability === "limited" ? "Limited" : "Unavailable"}
        </Badge>
      </div>
      
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{equipment.name}</CardTitle>
            <CardDescription className="mt-1">{equipment.brand}</CardDescription>
          </div>
          <Badge variant="outline" className="flex items-center">
            ★ {equipment.rating} ({equipment.reviewCount})
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4">{equipment.description}</p>
        <div className="flex items-center text-sm mb-2">
          <MapPin className="h-4 w-4 mr-1 text-muted-foreground" />
          <span>{equipment.location}</span>
        </div>
        <div className="flex items-center font-medium text-lg">
          <DollarSign className="h-5 w-5 mr-1" />
          <span>{rate}</span>
          <span className="text-sm text-muted-foreground ml-1">
            per {duration === 'day' ? 'day' : duration === 'week' ? 'week' : 'month'}
          </span>
        </div>
      </CardContent>
      
      <CardFooter>
        <Button className="w-full" onClick={onBook}>
          Book Now
        </Button>
      </CardFooter>
    </Card>
  );
};

export default Equipment;
