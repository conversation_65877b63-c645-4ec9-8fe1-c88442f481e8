
import React from "react";
import {
  Di<PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { ArrowUpRight, ArrowDownRight } from "lucide-react";
import { Commodity } from "@/data/commodityData";

interface Props {
  open: boolean;
  onClose: () => void;
  commodity: null | {
    id: string;
    name: string;
    color: string;
    currentPrice: number;
    priceChange: number;
    userHoldings: number;
    tradingVolume: number;
    recentTrades: Array<{ id: string; type: "Buy" | "Sell"; amount: number; price: number; date: string }>;
    news: string[];
  };
}

const CommodityDetailsModal: React.FC<Props> = ({ open, onClose, commodity }) => {
  if (!commodity) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-xl w-full">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <span className="w-4 h-4 rounded-full mr-2" style={{ background: commodity.color }} />
            <span>{commodity.name}</span>
          </DialogTitle>
          <DialogDescription>
            Overview of your holding and latest updates
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-5 mt-4">
          <div className="flex flex-wrap gap-8">
            <div className="flex-1 min-w-[160px] space-y-2">
              <div className="text-sm text-muted-foreground">Current Price</div>
              <div className="text-lg font-semibold">
                {commodity.currentPrice.toLocaleString()} UGX/kg
                <span className={`ml-2 inline-flex items-center ${commodity.priceChange > 0 ? "text-green-500" : commodity.priceChange < 0 ? "text-red-500" : "text-yellow-500"}`}>
                  {commodity.priceChange > 0 ? (
                    <ArrowUpRight className="h-4 w-4 mr-1" />
                  ) : commodity.priceChange < 0 ? (
                    <ArrowDownRight className="h-4 w-4 mr-1" />
                  ) : (
                    <span className="h-4 w-4 mr-1" />
                  )}
                  {Math.abs(commodity.priceChange)}%
                </span>
              </div>
            </div>
            <div className="flex-1 min-w-[160px] space-y-2">
              <div className="text-sm text-muted-foreground">Your Holdings</div>
              <div className="text-lg font-semibold">{commodity.userHoldings.toLocaleString()} units</div>
            </div>
            <div className="flex-1 min-w-[160px] space-y-2">
              <div className="text-sm text-muted-foreground">24h Trading Volume</div>
              <div className="text-lg font-semibold">{commodity.tradingVolume.toLocaleString()} units</div>
            </div>
          </div>
          <div>
            <div className="font-semibold mb-1">Recent Trading Activity</div>
            <div className="overflow-x-auto">
              <table className="min-w-full text-xs">
                <thead>
                  <tr>
                    <th className="pr-3 py-1 text-muted-foreground text-left">ID</th>
                    <th className="pr-3 py-1 text-muted-foreground text-left">Type</th>
                    <th className="pr-3 py-1 text-muted-foreground text-left">Amount</th>
                    <th className="pr-3 py-1 text-muted-foreground text-left">Price</th>
                    <th className="pr-3 py-1 text-muted-foreground text-left">Date</th>
                  </tr>
                </thead>
                <tbody>
                  {commodity.recentTrades.map((trade) => (
                    <tr key={trade.id} className="border-b last:border-none">
                      <td className="pr-3 py-1">{trade.id}</td>
                      <td className="pr-3 py-1">
                        <span className={`font-medium ${trade.type === "Buy" ? "text-green-500" : "text-red-500"}`}>
                          {trade.type}
                        </span>
                      </td>
                      <td className="pr-3 py-1">{trade.amount.toLocaleString()}</td>
                      <td className="pr-3 py-1">{trade.price.toLocaleString()} UGX</td>
                      <td className="pr-3 py-1">{trade.date}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div>
            <div className="font-semibold mb-1">News & Updates</div>
            <ul className="list-disc pl-4 space-y-1">
              {commodity.news.map((news, i) => (
                <li key={i} className="text-sm text-muted-foreground">{news}</li>
              ))}
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CommodityDetailsModal;
