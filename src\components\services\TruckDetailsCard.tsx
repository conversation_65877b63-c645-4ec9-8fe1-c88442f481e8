
import React from "react";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger
} from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

export interface TruckType {
  id: string;
  name: string;
  capacity: string;
  capacityValue: number; // Added numeric capacity for validation
  suitable: string[];
  icon: React.ReactNode;
  availableCount: number;
  registrationDetails?: {
    registrationNumber: string;
    yearOfManufacture: string;
    make: string;
    model: string;
  };
  driverInfo?: {
    name: string;
    experience: string;
    rating: number;
    phone?: string;
  };
  condition?: {
    lastService: string;
    nextService: string;
    condition: "Excellent" | "Good" | "Fair";
  };
}

interface TruckDetailsCardProps {
  truck: TruckType;
  selected: boolean;
  onClick: () => void;
  disabled?: boolean;
}

const TruckDetailsCard: React.FC<TruckDetailsCardProps> = ({
  truck,
  selected,
  onClick,
  disabled = false
}) => {
  return (
    <Card
      className={`border transition-colors ${
        disabled 
          ? "opacity-60 cursor-not-allowed bg-muted/30" 
          : `cursor-pointer hover:bg-secondary/50 ${selected ? "border-primary bg-primary/5" : "border-border"}`
      }`}
      onClick={disabled ? undefined : onClick}
    >
      <CardContent className="p-0">
        <div className="flex items-start p-4">
          <div className="mr-4">{truck.icon}</div>
          <div className="flex-1">
            <h4 className="font-medium">{truck.name}</h4>
            <p className="text-sm text-muted-foreground">{truck.capacity}</p>
            <div className="mt-1 text-xs text-primary">
              {truck.availableCount} available
            </div>
            <div className="mt-1 text-xs">
              Best for: {truck.suitable.join(", ")}
            </div>
            {disabled && (
              <Badge variant="outline" className="mt-1 bg-red-100 text-red-800 border-red-200">
                Capacity insufficient
              </Badge>
            )}
          </div>
        </div>

        {selected && (
          <Tabs defaultValue="registration" className="p-4 pt-0 border-t mt-2">
            <TabsList className="w-full">
              <TabsTrigger value="registration" className="flex-1">Registration</TabsTrigger>
              <TabsTrigger value="driver" className="flex-1">Driver</TabsTrigger>
              <TabsTrigger value="condition" className="flex-1">Condition</TabsTrigger>
            </TabsList>

            <TabsContent value="registration" className="pt-2">
              {truck.registrationDetails && (
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Reg Number:</span>
                    <span className="font-medium">{truck.registrationDetails.registrationNumber}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Make & Model:</span>
                    <span className="font-medium">{truck.registrationDetails.make} {truck.registrationDetails.model}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Year:</span>
                    <span className="font-medium">{truck.registrationDetails.yearOfManufacture}</span>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="driver" className="pt-2">
              {truck.driverInfo && (
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name:</span>
                    <span className="font-medium">{truck.driverInfo.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Experience:</span>
                    <span className="font-medium">{truck.driverInfo.experience}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Rating:</span>
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <span key={i} className={`text-xs ${i < truck.driverInfo.rating ? "text-yellow-500" : "text-gray-300"}`}>★</span>
                      ))}
                    </div>
                  </div>
                  {truck.driverInfo.phone && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Contact:</span>
                      <span className="font-medium">{truck.driverInfo.phone}</span>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="condition" className="pt-2">
              {truck.condition && (
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Condition:</span>
                    <span className={`font-medium ${
                      truck.condition.condition === "Excellent" 
                        ? "text-green-600" 
                        : truck.condition.condition === "Good" 
                          ? "text-blue-600" 
                          : "text-yellow-600"
                    }`}>
                      {truck.condition.condition}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Last Service:</span>
                    <span className="font-medium">{truck.condition.lastService}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Next Service:</span>
                    <span className="font-medium">{truck.condition.nextService}</span>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
};

export default TruckDetailsCard;
