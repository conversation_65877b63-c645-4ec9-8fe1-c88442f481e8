
import Hero from "@/components/home/<USER>";
import Features from "@/components/home/<USER>";
import MarketDashboard from "@/components/home/<USER>";
import Testimonials from "@/components/home/<USER>";
import CtaSection from "@/components/home/<USER>";
import MarketMarquee from "@/components/home/<USER>";
import LiveMarketUpdates from "@/components/home/<USER>";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { fetchCommodities } from "@/services/commoditiesService";

const Index = () => {
  const [dataError, setDataError] = useState<string | null>(null);
  
  // Check data integration on component mount
  useEffect(() => {
    const checkDataIntegration = async () => {
      try {
        // Test the commodities service
        await fetchCommodities();
        setDataError(null);
      } catch (error) {
        console.error("Data integration check failed:", error);
        setDataError("There was an issue loading commodity data. Some features may be limited.");
      }
    };
    
    checkDataIntegration();
  }, []);
  
  return (
    <div className="space-y-12">
      {dataError && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Data Error</AlertTitle>
          <AlertDescription>
            {dataError}
          </AlertDescription>
        </Alert>
      )}
      
      <Hero />
      
      <div className="py-4">
     
      </div>
      
      <Features />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <MarketDashboard />
        </div>
        <div>
          <LiveMarketUpdates />
        </div>
      </div>
      
      <Testimonials />
      <CtaSection />
    </div>
  );
};

export default Index;