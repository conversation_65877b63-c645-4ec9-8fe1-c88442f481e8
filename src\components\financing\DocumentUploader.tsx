
import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Upload, FileCheck, X, FileText } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

interface DocumentUploaderProps {
  documentType: string;
  onFileUpload: (documentType: string, file: File) => void;
  uploadedFile: File | null;
}

const DocumentUploader = ({
  documentType,
  onFileUpload,
  uploadedFile
}: DocumentUploaderProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleFileDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files[0]);
    }
  };

  const handleFileUpload = (file: File) => {
    setIsUploading(true);

    // Simulate file upload with progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      setUploadProgress(progress);
      
      if (progress >= 100) {
        clearInterval(interval);
        setIsUploading(false);
        onFileUpload(documentType, file);
      }
    }, 200);
  };

  const removeFile = () => {
    onFileUpload(documentType, null as unknown as File);
  };

  return (
    <div className="space-y-2">
      <div className="text-sm font-medium">{documentType}</div>
      
      {!uploadedFile && !isUploading ? (
        <div
          className={cn(
            "border-2 border-dashed rounded-md p-4 text-center cursor-pointer transition-colors",
            isDragging ? "border-primary bg-primary/5" : "border-muted-foreground/25 hover:border-primary/50"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleFileDrop}
        >
          <div className="flex flex-col items-center gap-2">
            <Upload className="h-5 w-5 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              Drag and drop your file here or{" "}
              <label className="text-primary hover:underline cursor-pointer">
                browse
                <input
                  type="file"
                  className="hidden"
                  onChange={handleFileSelect}
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                />
              </label>
            </p>
            <p className="text-xs text-muted-foreground">
              PDF, JPG, PNG or DOC (max. 5MB)
            </p>
          </div>
        </div>
      ) : isUploading ? (
        <div className="border rounded-md p-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">Uploading...</span>
            </div>
            <span className="text-sm text-muted-foreground">{uploadProgress}%</span>
          </div>
          <Progress value={uploadProgress} className="h-1" />
        </div>
      ) : (
        <div className="border rounded-md p-4 bg-muted/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <FileCheck className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-sm font-medium truncate">
                  {uploadedFile?.name}
                </p>
                <p className="text-xs text-muted-foreground">
                  {Math.round(uploadedFile?.size / 1024)} KB
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={removeFile}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentUploader;
