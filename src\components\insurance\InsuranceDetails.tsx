
import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { FileText, Shield, CheckCircle2, AlertCircle, FileCheck } from 'lucide-react';

interface InsuranceDetailsProps {
  insuranceType: "crop" | "livestock" | "transport";
}

const InsuranceDetails = ({ insuranceType }: InsuranceDetailsProps) => {
  // Insurance details based on type
  const details = {
    crop: {
      description: "Comprehensive protection for your crops against natural disasters, pests, and market price fluctuations.",
      eligibility: [
        "Own or lease agricultural land in Uganda",
        "Crop must be planted according to recommended agricultural practices",
        "Must be registered with the local agricultural office",
        "Minimum 1 acre of land under cultivation"
      ],
      coverage: [
        "Up to 80% of expected crop value for premium coverage",
        "Deductible varies from 10-20% based on coverage level",
        "Maximum coverage of 50,000,000 UGX per policy"
      ],
      covered: [
        "Drought damage",
        "Flood damage",
        "Fire damage",
        "Hail damage",
        "Pest infestations",
        "Plant diseases",
        "Premium plan includes market price protection"
      ],
      exclusions: [
        "Damage due to negligence or poor farming practices",
        "Pre-existing crop conditions",
        "War, riots, or civil commotions",
        "Intentional destruction",
        "Nuclear incidents or radiation"
      ],
      claims: [
        "Report damage within 72 hours of discovery",
        "Complete claim form available online or at local offices",
        "Provide photographic evidence of damage",
        "Assessment visit by insurance agent within 7 working days",
        "Claim settlement within 30 days of completed assessment"
      ],
      terms: [
        "Policy period typically aligns with crop growing season",
        "Premium must be paid in full before coverage begins",
        "Assessment is required before coverage approval",
        "Claims must be filed within the policy period",
        "Policy is non-transferable without written permission"
      ]
    },
    livestock: {
      description: "Protection for your livestock against diseases, accidents, theft, and more to secure your farming investment.",
      eligibility: [
        "Must own the livestock being insured",
        "Animals must be in good health at the time of application",
        "Proper identification (tags, brands) required for all insured animals",
        "Must keep updated vaccination and health records"
      ],
      coverage: [
        "Up to 100% of market value for premium coverage",
        "Deductible ranges from 5-15% based on coverage level",
        "Maximum coverage of 30,000,000 UGX per animal"
      ],
      covered: [
        "Death due to diseases",
        "Accidental injuries",
        "Theft (with police report)",
        "Emergency veterinary expenses (standard and premium plans)",
        "Loss of production due to illness (premium plan only)"
      ],
      exclusions: [
        "Pre-existing health conditions",
        "Neglect or improper care",
        "War, civil unrest or terrorism",
        "Intentional injuries or slaughter",
        "Deaths occurring within 14 days of policy start (waiting period)"
      ],
      claims: [
        "Notify insurance company within 24 hours of death or injury",
        "Submit veterinary reports and evidence",
        "Allow inspection of dead animals before disposal",
        "Provide police report in case of theft",
        "Claim processed within 21 days of complete documentation"
      ],
      terms: [
        "One-year policy term with renewal option",
        "30-day grace period for premium payment",
        "Regular health checks required for insured livestock",
        "Insurance company has the right to inspect animals at any time",
        "Failure to follow recommended care practices may void coverage"
      ]
    },
    transport: {
      description: "Insurance coverage for your agricultural goods in transit and transportation vehicles to mitigate losses.",
      eligibility: [
        "Must own or legally operate the transport vehicle",
        "Vehicle must meet safety and roadworthiness standards",
        "Driver must have valid license and clean record",
        "Proper documentation for transported goods required"
      ],
      coverage: [
        "Up to vehicle market value for vehicle coverage",
        "Up to declared value for goods in transit",
        "Deductible of 5-10% depending on coverage option",
        "Maximum coverage of 100,000,000 UGX per incident"
      ],
      covered: [
        "Vehicle damage in transit",
        "Damage to transported agricultural goods",
        "Theft of vehicle or goods (with police report)",
        "Third-party liability",
        "Fire, explosion, or weather-related damage"
      ],
      exclusions: [
        "Damage due to improper packaging or loading",
        "Violations of traffic laws or regulations",
        "Driving under influence of alcohol or drugs",
        "Mechanical breakdown without accident",
        "Natural deterioration or spoilage of goods"
      ],
      claims: [
        "Report incident immediately to police (if applicable)",
        "Notify insurance company within 24 hours",
        "Complete claim form with all relevant details",
        "Provide photographs of damage and incident scene",
        "Claims typically processed within 14 days"
      ],
      terms: [
        "Policy valid for one year from issue date",
        "Premium must be paid in full or as per agreed schedule",
        "Vehicle must be used for declared purposes only",
        "Changes in vehicle use or condition must be reported",
        "Claim settlement based on actual cash value at time of loss"
      ]
    }
  };

  const currentDetails = details[insuranceType];

  return (
    <div className="space-y-4 my-6">
      <div className="bg-muted/50 p-4 rounded-md flex items-start gap-3">
        <Shield className="h-5 w-5 text-primary mt-0.5" />
        <p className="text-sm">{currentDetails.description}</p>
      </div>
      
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="eligibility">
          <AccordionTrigger className="text-sm font-medium">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-primary" />
              Eligibility Requirements
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <ul className="text-sm space-y-2 pl-6 list-disc">
              {currentDetails.eligibility.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="coverage">
          <AccordionTrigger className="text-sm font-medium">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-primary" />
              Coverage Limits & Deductibles
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <ul className="text-sm space-y-2 pl-6 list-disc">
              {currentDetails.coverage.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="covered">
          <AccordionTrigger className="text-sm font-medium">
            <div className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              Covered Risks
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <ul className="text-sm space-y-2 pl-6 list-disc">
              {currentDetails.covered.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="exclusions">
          <AccordionTrigger className="text-sm font-medium">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-destructive" />
              Exclusions
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <ul className="text-sm space-y-2 pl-6 list-disc">
              {currentDetails.exclusions.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="claims">
          <AccordionTrigger className="text-sm font-medium">
            <div className="flex items-center gap-2">
              <FileCheck className="h-4 w-4 text-primary" />
              Claims Process
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <ul className="text-sm space-y-2 pl-6 list-disc">
              {currentDetails.claims.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="terms">
          <AccordionTrigger className="text-sm font-medium">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-primary" />
              Terms & Conditions
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <ul className="text-sm space-y-2 pl-6 list-disc">
              {currentDetails.terms.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default InsuranceDetails;
