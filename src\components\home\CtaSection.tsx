
import { <PERSON><PERSON> } from "@/components/ui/button";
import { NavLink } from "react-router-dom";
import { ArrowR<PERSON>, Coins, Wallet, TrendingUp } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const CtaSection = () => {
  return (
    <section className="py-16">
      <Card className="overflow-hidden border-2 border-border">
        <div className="bg-gradient-to-br from-[#8BC34A]/10 via-muted to-card p-8 md:p-12">
          <div className="text-center max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to start trading?</h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto mb-8">
              Join thousands of traders on our platform and gain access to tokenized commodities with just a few clicks.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
              <div className="bg-card/80 backdrop-blur-sm p-6 rounded-lg border border-border flex flex-col items-center">
                <div className="h-12 w-12 rounded-full bg-[#8BC34A]/10 flex items-center justify-center mb-4">
                  <Wallet className="h-6 w-6 text-[#8BC34A]" />
                </div>
                <h3 className="font-medium text-lg mb-2">Create an Account</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Sign up in minutes and verify your identity to get started.
                </p>
              </div>
              
              <div className="bg-card/80 backdrop-blur-sm p-6 rounded-lg border border-border flex flex-col items-center">
                <div className="h-12 w-12 rounded-full bg-[#8BC34A]/10 flex items-center justify-center mb-4">
                  <Coins className="h-6 w-6 text-[#8BC34A]" />
                </div>
                <h3 className="font-medium text-lg mb-2">Fund Your Wallet</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Deposit funds using multiple payment methods in your local currency.
                </p>
              </div>
              
              <div className="bg-card/80 backdrop-blur-sm p-6 rounded-lg border border-border flex flex-col items-center">
                <div className="h-12 w-12 rounded-full bg-[#8BC34A]/10 flex items-center justify-center mb-4">
                  <TrendingUp className="h-6 w-6 text-[#8BC34A]" />
                </div>
                <h3 className="font-medium text-lg mb-2">Start Trading</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Buy and sell tokenized commodities with real-time market prices.
                </p>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <NavLink to="/signup">
                <Button size="lg" className="gap-2 bg-[#8BC34A] hover:bg-[#8BC34A]/90 text-white">
                  Create an Account <ArrowRight className="h-4 w-4" />
                </Button>
              </NavLink>
              <NavLink to="/contact">
                <Button size="lg" variant="outline">Contact Sales</Button>
              </NavLink>
            </div>
            
            <div className="mt-8 text-sm text-muted-foreground">
              By signing up, you agree to our{" "}
              <NavLink to="/terms" className="underline hover:text-[#8BC34A]">
                Terms of Service
              </NavLink>{" "}
              and{" "}
              <NavLink to="/privacy" className="underline hover:text-[#8BC34A]">
                Privacy Policy
              </NavLink>
            </div>
          </div>
        </div>
      </Card>
    </section>
  );
};

export default CtaSection;
