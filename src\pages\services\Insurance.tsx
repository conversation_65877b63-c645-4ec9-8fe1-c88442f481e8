
import React, { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { FileText, ShieldCheck, FileBarChart, AlertCircle } from "lucide-react";

import InsuranceDetails from '@/components/insurance/InsuranceDetails';
import QuoteApplication from '@/components/insurance/QuoteApplication';
import InsuranceProductsList from '@/components/insurance/InsuranceProductsList';
import QuoteCalculator, { insuranceProducts } from '@/components/insurance/QuoteCalculator';
import PolicyList from '@/components/insurance/PolicyList';
import ClaimsList from '@/components/insurance/ClaimsList';

const InsuranceServices = () => {
  const [activeTab, setActiveTab] = useState("explore");
  const [selectedProduct, setSelectedProduct] = useState("ins-01");
  const [showDetails, setShowDetails] = useState<string | null>(null);
  const [showApplication, setShowApplication] = useState(false);
  
  const product = insuranceProducts.find(p => p.id === selectedProduct) || insuranceProducts[0];
  
  const handleLearnMore = (productId: string) => {
    setSelectedProduct(productId);
    setShowDetails(productId);
    setActiveTab("explore");
    setShowApplication(false);
  };

  const handleShowApplication = () => {
    setShowDetails(null);
    setShowApplication(true);
  };

  const handleBackToQuote = () => {
    setShowDetails(null);
    setShowApplication(false);
  };

  const handleFileClaimClick = () => {
    setActiveTab("claims");
  };

  const handleExploreClick = () => {
    setActiveTab("explore");
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <ShieldCheck className="h-8 w-8 mr-2 text-primary" />
        <h1 className="text-3xl font-bold">Insurance Services</h1>
      </div>
      
      <p className="text-muted-foreground mb-8">
        Protect your agricultural business with tailored insurance solutions for crops, livestock, and equipment.
      </p>

      <InsuranceProductsList onLearnMore={handleLearnMore} />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="explore" className="flex items-center gap-2">
            <FileText className="h-4 w-4" /> Insurance Products
          </TabsTrigger>
          <TabsTrigger value="policies" className="flex items-center gap-2">
            <FileBarChart className="h-4 w-4" /> My Policies
          </TabsTrigger>
          <TabsTrigger value="claims" className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" /> Claims Management
          </TabsTrigger>
        </TabsList>

        {/* Insurance Products Tab */}
        <TabsContent value="explore">
          <Card>
            {showDetails ? (
              <>
                <CardContent className="p-6">
                  <div className="flex justify-between items-center mb-4">
                    <div>
                      <h2 className="text-2xl font-semibold">{product.name} Details</h2>
                      <p className="text-muted-foreground">Comprehensive information about our {product.name.toLowerCase()} offerings</p>
                    </div>
                    <div>
                      <button 
                        className="text-sm text-primary hover:underline"
                        onClick={handleBackToQuote}
                      >
                        Back to Quote
                      </button>
                    </div>
                  </div>
                  <InsuranceDetails 
                    insuranceType={product.type as "crop" | "livestock" | "transport"} 
                  />
                </CardContent>
              </>
            ) : showApplication ? (
              <>
                <CardContent className="p-6">
                  <div className="flex justify-between items-center mb-4">
                    <div>
                      <h2 className="text-2xl font-semibold">{product.name} Application</h2>
                      <p className="text-muted-foreground">Complete the form below to apply for coverage</p>
                    </div>
                    <div>
                      <button 
                        className="text-sm text-primary hover:underline"
                        onClick={handleBackToQuote}
                      >
                        Back to Quote
                      </button>
                    </div>
                  </div>
                  <QuoteApplication 
                    insuranceType={product.name}
                    coverageLevel={product.coverageOptions[0]}
                    premium={product.type === "crop" 
                      ? product.yearlyRatePerAcre[0]
                      : product.type === "livestock"
                      ? product.yearlyRatePerHead[0]
                      : product.yearlyRatePercent[0] * 5000000 / 100}
                    details={
                      product.type === "crop" 
                        ? {cropType: "Coffee (Arabica)", acreage: 1}
                        : product.type === "livestock"
                        ? {livestockType: "Cattle", numberOfAnimals: 1}
                        : {equipmentType: "Tractor", equipmentCount: 1}
                    }
                  />
                </CardContent>
              </>
            ) : (
              <QuoteCalculator 
                selectedProduct={selectedProduct} 
                onProductChange={setSelectedProduct}
                onShowDetails={setShowDetails}
                onShowApplication={handleShowApplication}
              />
            )}
          </Card>
        </TabsContent>

        {/* My Policies Tab */}
        <TabsContent value="policies">
          <PolicyList 
            onFileClaimClick={handleFileClaimClick}
            onExploreClick={handleExploreClick}
          />
        </TabsContent>

        {/* Claims Management Tab */}
        <TabsContent value="claims">
          <ClaimsList />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default InsuranceServices;
