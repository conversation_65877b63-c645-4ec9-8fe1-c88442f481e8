import Stripe from 'stripe';
import config from '../config/env';
import logger from '../utils/logger';
import { ApiError } from '../middlewares/errorHandler';

export class PaymentService {
  private stripe: Stripe;

  constructor() {
    // Initialize Stripe if API key is available
    if (config.STRIPE_SECRET_KEY) {
      this.stripe = new Stripe(config.STRIPE_SECRET_KEY, {
        apiVersion: '2022-11-15',
      });
    }
  }

  /**
   * Create Stripe payment intent
   */
  async createStripePaymentIntent(
    amount: number,
    currency = 'ugx',
    metadata: Record<string, string> = {}
  ) {
    try {
      if (!this.stripe) {
        throw new ApiError(500, 'Stripe is not configured');
      }

      // Convert amount to cents/smallest currency unit
      const amountInCents = Math.round(amount * 100);

      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: amountInCents,
        currency,
        metadata,
      });

      return {
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
      };
    } catch (error) {
      logger.error('Error creating Stripe payment intent:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(500, 'Failed to create payment intent');
    }
  }

  /**
   * Handle Stripe webhook events
   */
  async handleStripeWebhook(event: any) {
    try {
      switch (event.type) {
        case 'payment_intent.succeeded':
          return await this.handleStripePaymentSuccess(event.data.object);
        case 'payment_intent.payment_failed':
          return await this.handleStripePaymentFailure(event.data.object);
        default:
          logger.info(`Unhandled Stripe event type: ${event.type}`);
          return { received: true };
      }
    } catch (error) {
      logger.error('Error processing Stripe webhook:', error);
      throw new ApiError(500, 'Failed to process payment webhook');
    }
  }

  /**
   * Handle Stripe payment success
   */
  private async handleStripePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
    // Implement your business logic here
    // For example:
    // 1. Find transaction by paymentIntent.id
    // 2. Update transaction status to COMPLETED
    // 3. Update wallet balance

    logger.info(`Stripe payment succeeded: ${paymentIntent.id}`);
    return { success: true, paymentIntentId: paymentIntent.id };
  }

  /**
   * Handle Stripe payment failure
   */
  private async handleStripePaymentFailure(paymentIntent: Stripe.PaymentIntent) {
    // Implement your business logic here
    // For example:
    // 1. Find transaction by paymentIntent.id
    // 2. Update transaction status to FAILED
    
    logger.info(`Stripe payment failed: ${paymentIntent.id}`);
    return { success: false, paymentIntentId: paymentIntent.id };
  }

  /**
   * Initiate mobile money payment (MTN)
   */
  async initiateMtnMobileMoneyPayment(
    phoneNumber: string,
    amount: number,
    reference: string,
    description: string
  ) {
    try {
      // This is a placeholder for MTN Mobile Money API integration
      // Normally, you would implement the actual API call here

      if (!config.MTN_API_KEY || !config.MTN_API_SECRET) {
        throw new ApiError(500, 'MTN Mobile Money is not configured');
      }

      // Mock successful response
      const transactionId = `mtn-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      
      logger.info(`MTN Mobile Money payment initiated: ${transactionId}`);
      
      return {
        success: true,
        transactionId,
        status: 'PENDING',
        provider: 'mtn',
        phoneNumber,
      };
    } catch (error) {
      logger.error('Error initiating MTN Mobile Money payment:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(500, 'Failed to initiate mobile money payment');
    }
  }

  /**
   * Initiate mobile money payment (Airtel)
   */
  async initiateAirtelMoneyPayment(
    phoneNumber: string,
    amount: number,
    reference: string,
    description: string
  ) {
    try {
      // This is a placeholder for Airtel Money API integration
      // Normally, you would implement the actual API call here

      if (!config.AIRTEL_API_KEY || !config.AIRTEL_API_SECRET) {
        throw new ApiError(500, 'Airtel Money is not configured');
      }

      // Mock successful response
      const transactionId = `airtel-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      
      logger.info(`Airtel Money payment initiated: ${transactionId}`);
      
      return {
        success: true,
        transactionId,
        status: 'PENDING',
        provider: 'airtel',
        phoneNumber,
      };
    } catch (error) {
      logger.error('Error initiating Airtel Money payment:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(500, 'Failed to initiate mobile money payment');
    }
  }

  /**
   * Check mobile money payment status
   */
  async checkMobileMoneyStatus(transactionId: string, provider: string) {
    try {
      // This is a placeholder for checking payment status
      // Normally, you would implement the actual API call to check status

      // For demo purposes, we'll randomly decide if the payment was successful
      const isSuccessful = Math.random() > 0.3; // 70% chance of success
      
      return {
        transactionId,
        provider,
        status: isSuccessful ? 'SUCCESSFUL' : 'FAILED',
        message: isSuccessful ? 'Payment completed successfully' : 'Payment failed or was cancelled',
      };
    } catch (error) {
      logger.error('Error checking mobile money status:', error);
      throw new ApiError(500, 'Failed to check payment status');
    }
  }
} 