
import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";

interface BuyAssetsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBuy: (asset: string, quantity: number, amount: number) => void;
  availableAssets: Array<{ id: string; name: string; symbol: string; price: number }>;
  cashBalance: number;
}

export const BuyAssetsModal = ({ isOpen, onClose, onBuy, availableAssets, cashBalance }: BuyAssetsModalProps) => {
  // Filter out any assets containing "bean" in the id (case insensitive)
  const filteredAssets = availableAssets.filter(
    asset => !asset.id.toLowerCase().includes("bean")
  );
  
  const [selectedAsset, setSelectedAsset] = useState<string>("");
  const [quantity, setQuantity] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  const selectedAssetDetails = filteredAssets.find(asset => asset.id === selectedAsset);
  const totalCost = selectedAssetDetails && !isNaN(parseFloat(quantity)) 
    ? selectedAssetDetails.price * parseFloat(quantity) 
    : 0;

  const handleBuy = () => {
    if (!selectedAsset || !quantity || parseFloat(quantity) <= 0) {
      toast({
        title: "Invalid input",
        description: "Please select an asset and enter a valid quantity",
        variant: "destructive"
      });
      return;
    }

    if (totalCost > cashBalance) {
      toast({
        title: "Insufficient funds",
        description: "You don't have enough cash to complete this purchase",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    
    // Simulate API call
    setTimeout(() => {
      onBuy(selectedAsset, parseFloat(quantity), totalCost);
      setIsProcessing(false);
      toast({
        title: "Purchase successful",
        description: `You've purchased ${quantity} ${selectedAssetDetails?.symbol} for $${totalCost.toFixed(2)}`,
      });
      resetForm();
      onClose();
    }, 1500);
  };

  const resetForm = () => {
    setSelectedAsset("");
    setQuantity("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        resetForm();
        onClose();
      }
    }}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Buy Assets</DialogTitle>
          <DialogDescription>
            Purchase agricultural commodities for your portfolio
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="asset">Select Asset</Label>
            <Select 
              value={selectedAsset} 
              onValueChange={setSelectedAsset}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an asset" />
              </SelectTrigger>
              <SelectContent>
                {filteredAssets.map((asset) => (
                  <SelectItem key={asset.id} value={asset.id}>
                    {asset.name} ({asset.symbol}) - ${asset.price.toFixed(2)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="quantity">Quantity</Label>
            <Input
              id="quantity"
              type="number"
              placeholder="Enter quantity"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              min="0"
              step="0.01"
            />
          </div>
          
          {selectedAsset && quantity && !isNaN(parseFloat(quantity)) && (
            <div className="grid gap-2">
              <Label>Transaction Summary</Label>
              <div className="p-3 bg-secondary/30 rounded-md">
                <div className="flex justify-between text-sm">
                  <span>Asset:</span>
                  <span className="font-medium">{selectedAssetDetails?.name}</span>
                </div>
                <div className="flex justify-between text-sm mt-1">
                  <span>Price per unit:</span>
                  <span className="font-medium">{selectedAssetDetails?.price.toLocaleString()} UGX</span>
                </div>
                <div className="flex justify-between text-sm mt-1">
                  <span>Quantity:</span>
                  <span className="font-medium">{parseFloat(quantity)}</span>
                </div>
                <div className="flex justify-between font-medium mt-2 pt-2 border-t border-secondary">
                  <span>Total cost:</span>
                  <span className="text-primary">{totalCost.toLocaleString()} UGX</span>
                </div>
              </div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleBuy} disabled={isProcessing}>
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              "Buy Now"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
