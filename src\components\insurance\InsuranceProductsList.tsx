
import React from 'react';
import InsuranceProductCard from './InsuranceProductCard';
import { getServicesByCategory } from '@/data/servicesData';

interface InsuranceProductsListProps {
  onLearnMore: (productId: string) => void;
}

const InsuranceProductsList = ({ onLearnMore }: InsuranceProductsListProps) => {
  const insuranceServices = getServicesByCategory("insurance");
  
  const handleLearnMore = (serviceId: string) => {
    const productId = serviceId === "crop-insurance" ? "ins-01" : 
                      serviceId === "livestock-insurance" ? "ins-02" : "ins-03";
    onLearnMore(productId);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      {insuranceServices.map((service) => (
        <InsuranceProductCard 
          key={service.id}
          id={service.id}
          title={service.title}
          description={service.description}
          onLearnMore={handleLearnMore}
        />
      ))}
    </div>
  );
};

export default InsuranceProductsList;
