import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  AreaChart, Area, PieChart, Pie, Cell, Legend, Sector // Sector for active shape
} from "recharts";
import { ArrowUpRight, ArrowDownRight, Wallet, CreditCard, TrendingUp, TrendingDown, MinusCircle } from "lucide-react";
import ServicesSummaryCard from "@/components/services/ServicesSummaryCard";
import { useState, useEffect } from "react";
import { commodities as allCommodities } from "@/data/commodityData"; // Renamed for clarity, still needed for historical/comparison data
import { formatCurrency } from "@/utils/currencyFormatter";
// import { Transaction } from "@/pages/Wallet"; // No longer needed, import from context
// import { useTransactionContext } from "@/context/TransactionContext"; // No longer needed
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils"; // Import cn utility

// Import the new context hook and types
import { useWalletContext } from "@/context/WalletContext"; // Import types if needed

// Mock data for Active Trades
interface ActiveTrade {
  id: string;
  commodityName: string;
  symbol: string;
  quantity: number;
  entryPrice: number;
  currentPrice: number;
  value: number; // Calculated as quantity * currentPrice
  trend: "up" | "down" | "neutral";
  priceChangePercent: number;
  color: string;
}

const mockActiveTrades: ActiveTrade[] = [
  {
    id: "trade1",
    commodityName: "Coffee (Arabica)",
    symbol: "COF-A",
    quantity: 5,
    entryPrice: 17500,
    currentPrice: 18000,
    value: 5 * 18000,
    trend: "up",
    priceChangePercent: ((18000 - 17500) / 17500) * 100,
    color: "#8B4513",
  },
  {
    id: "trade2",
    commodityName: "Cocoa",
    symbol: "COC",
    quantity: 2,
    entryPrice: 27200,
    currentPrice: 27000,
    value: 2 * 27000,
    trend: "down",
    priceChangePercent: ((27000 - 27200) / 27200) * 100,
    color: "#4B371C",
  },
  {
    id: "trade3",
    commodityName: "Maize",
    symbol: "MAZ",
    quantity: 10,
    entryPrice: 1200,
    currentPrice: 1200,
    value: 10 * 1200,
    trend: "neutral",
    priceChangePercent: 0,
    color: "#F0E68C",
  },
];

const Dashboard = () => {
  // Use the new wallet context to get dynamic data
  const { userAssets, totalPortfolioValue, transactions, cashBalance } = useWalletContext();

  const [selectedCommodity, setSelectedCommodity] = useState(allCommodities[0]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [showActiveTradesDetails, setShowActiveTradesDetails] = useState(false); // State for active trades details
  const [tradeVolumeData, setTradeVolumeData] = useState([
    { date: "Jan", volume: 4000 },
    { date: "Feb", volume: 3000 },
    { date: "Mar", volume: 2000 },
    { date: "Apr", volume: 2780 },
    { date: "May", volume: 1890 },
    { date: "Jun", volume: 2390 },
    { date: "Jul", volume: 3490 },
  ]);
  const [userActivityData, setUserActivityData] = useState([
    { date: "Jan", users: 200 },
    { date: "Feb", users: 300 },
    { date: "Mar", users: 400 },
    { date: "Apr", users: 380 },
    { date: "May", users: 590 },
    { date: "Jun", users: 400 },
    { date: "Jul", users: 690 },
  ]);

  // --- Generate portfolio data *dynamically* from userAssets ---
  // Filter assets with balance > 0 and format for the pie chart
  const portfolioChartData = userAssets
    .filter(asset => asset.balance > 0) // Only show assets with a balance
    .map(asset => ({
      name: asset.name,
      value: asset.value,
      color: asset.color || '#A9A9A9', // Use color from userAsset (which comes from commodity data)
    }));

  // Sort portfolio data for consistent legend/rendering (optional but good)
  portfolioChartData.sort((a, b) => b.value - a.value);

  // --- End Dynamic Portfolio Data ---


  // Format price history data from commodities (keep existing logic, it uses static commodity data)
  const generatePriceHistoryData = () => {
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun"]; // Assuming 6 months for this mock data
    const result = [];

    for (let i = 0; i < months.length; i++) {
      const monthData: { [key: string]: string | number | null } = { month: months[i] }; // Use more specific types
      
      allCommodities.forEach(commodity => {
        // Find data point by month, using a fallback if month data isn't available
        const dataPoint = commodity.data.find(d => d.month === months[i]);
         monthData[commodity.id] = dataPoint ? dataPoint.price : null; // Use null if no data for the month
      });
      
      result.push(monthData);
    }
    
    return result;
  };

  // Recalculate price history if commodities data changes (unlikely in this mock)
   const priceHistoryData = generatePriceHistoryData();


  // Filter commodities to show in comparison chart (top 5 based on initial data)
  // This could potentially be filtered based on user holdings if needed, but sticking to top 5 overall is simpler.
  const topCommodities = allCommodities.slice(0, 5);

  // Active segment handler for pie chart
  const onPieEnter = (_: unknown, index: number) => {
    setActiveIndex(index);
  };

  // Custom active shape for pie chart (keep existing logic)
  const renderActiveShape = (props: {
    cx: number;
    cy: number;
    innerRadius: number;
    outerRadius: number;
    startAngle: number;
    endAngle: number;
    fill: string;
    payload: { name: string; value: number };
    percent: number;
    value: number;
  }) => {
    const { cx, cy, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent, value } = props;

    return (
      <g>
        <text x={cx} y={cy - 24} dy={8} textAnchor="middle" fill="hsl(var(--foreground))" className="text-base font-semibold">
          {payload.name}
        </text>
        <text x={cx} y={cy} dy={8} textAnchor="middle" fill="hsl(var(--foreground))" fontSize="20" fontWeight="bold">
          {formatCurrency(value)}
        </text>
         <text x={cx} y={cy + 20} dy={8} textAnchor="middle" fill="hsl(var(--muted-foreground))" className="text-sm">
          {`( ${(percent * 100).toFixed(1)}% of portfolio )`}
        </text>
        {/* Sector effect for active slice */}
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius + 10} // Make active slice pop out more
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
          style={{ transition: 'all .3s ease-out' }} // Add smooth transition
        />
        {/* Optional: Add a second, slightly larger sector for a layered effect */}
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 12} // Slightly larger than the main active sector
          outerRadius={outerRadius + 14}
          fill={fill} // Use the same color
          opacity={0.2} // Make it semi-transparent
           style={{ transition: 'all .3s ease-out' }} // Add smooth transition
        />
      </g>
    );
  };

  // Use transactions from context
  const recentTransactions = transactions;


  // Prepare data for the Portfolio Summary table
   // It should use the *actual* userAssets with balances > 0
   const portfolioSummaryData = userAssets
    .filter(asset => asset.balance > 0) // Filter for holdings
    .map(asset => {
        // Find the corresponding commodity for current price and trend
        const commodity = allCommodities.find(c => c.id === asset.id);
        return {
            ...asset, // Include name, value, color, etc. from userAsset
            currentPrice: commodity?.currentPrice ?? asset.price, // Use latest price from commodity if available
            trending: commodity?.trending ?? 'neutral', // Use trend from commodity
            priceChange: commodity?.priceChange ?? 0, // Use change from commodity
        };
    });

   // Sort table data by value (optional but good)
   portfolioSummaryData.sort((a, b) => b.value - a.value);


  return (
    <div className="space-y-6 p-4 md:p-6"> {/* Added padding for better layout */}
      <h1 className="text-3xl font-bold mb-6">Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Total Portfolio Value</CardTitle>
            <CardDescription>All assets combined</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{formatCurrency(totalPortfolioValue)}</div> {/* Use dynamic total */}
            {/* Note: Growth percentage is hardcoded mock data. Consider calculating based on changes in userAssets value over time if historical data is available */}
            <div className="text-sm text-up flex items-center mt-1 text-green-500"> {/* Used green-500 class for clarity */}
              <ArrowUpRight className="h-4 w-4 mr-1" /> +2.5% ({formatCurrency(320)}) {/* This is still mock */}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Available Balance</CardTitle>
            <CardDescription>Funds ready to trade</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{formatCurrency(cashBalance)}</div> {/* Use cashBalance from context */}
            <div className="flex items-center mt-1">
              <Wallet className="h-4 w-4 mr-1 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Available for withdrawal</span>
            </div>
            <Link to="/wallet" className="mt-4 inline-flex items-center text-sm font-medium text-primary hover:underline">
              View Wallet
            </Link>
          </CardContent>
        </Card>

        <Card 
          className={cn(
            "transition-all duration-300 cursor-pointer",
            showActiveTradesDetails ? "shadow-lg shadow-green-500/30 ring-2 ring-green-400" : "hover:shadow-md"
          )}
          onClick={() => setShowActiveTradesDetails(!showActiveTradesDetails)}
        >
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Active Trades</CardTitle>
            <CardDescription>Currently open positions</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Note: This is still hardcoded '8'. You'd need actual trading position data */}
            <div className="text-3xl font-bold">{mockActiveTrades.length}</div> {/* Changed to mock data length */}
            <div className="flex items-center mt-1">
              <CreditCard className="h-4 w-4 mr-1 text-muted-foreground" />
              {/* This is still mock based on unique commodities in mock data */}
              <span className="text-sm text-muted-foreground">Across {new Set(mockActiveTrades.map(t => t.commodityName)).size} commodities</span> 
            </div>
          </CardContent>
          {showActiveTradesDetails && (
            <CardContent className="pt-4 border-t mt-2">
              <h4 className="text-sm font-semibold mb-3">Trade Details:</h4>
              <div className="space-y-3 max-h-48 overflow-y-auto">
                {mockActiveTrades.map(trade => (
                  <div key={trade.id} className="p-2 border rounded-md bg-background hover:bg-muted/50">
                    <div className="flex justify-between items-center mb-1">
                      <div className="flex items-center">
                         <div className="w-2 h-2 rounded-full mr-2 flex-shrink-0" style={{ backgroundColor: trade.color }}></div>
                        <span className="font-medium text-sm">{trade.commodityName} ({trade.symbol})</span>
                      </div>
                      <Badge variant={trade.trend === "up" ? "default" : trade.trend === "down" ? "destructive" : "outline"}
                        className={cn(
                            "text-xs px-1.5 py-0.5 font-normal",
                            trade.trend === "up" && "bg-green-100 text-green-700 border-green-300",
                            trade.trend === "down" && "bg-red-100 text-red-700 border-red-300",
                            trade.trend === "neutral" && "bg-gray-100 text-gray-700 border-gray-300"
                        )}
                      >
                        {trade.trend.charAt(0).toUpperCase() + trade.trend.slice(1)}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-x-2 gap-y-1 text-xs">
                      <span className="text-muted-foreground">Qty:</span><span>{trade.quantity}</span>
                      <span className="text-muted-foreground">Entry:</span><span>{formatCurrency(trade.entryPrice)}</span>
                      <span className="text-muted-foreground">Current:</span><span>{formatCurrency(trade.currentPrice)}</span>
                      <span className="text-muted-foreground">Value:</span><span className="font-semibold">{formatCurrency(trade.value)}</span>
                      <span className="text-muted-foreground">P/L:</span>
                      <span className={cn("font-medium", 
                        (trade.currentPrice - trade.entryPrice) * trade.quantity >= 0 ? "text-green-600" : "text-red-600"
                      )}>
                        {formatCurrency((trade.currentPrice - trade.entryPrice) * trade.quantity)}
                        <span className="ml-1 text-xxs">({trade.priceChangePercent.toFixed(2)}%)</span>
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          )}
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Tabs defaultValue="portfolio" className="w-full">
            <TabsList>
              <TabsTrigger value="portfolio">Portfolio</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger> {/* Renamed from 'overview' based on content */}
            </TabsList>
            <TabsContent value="portfolio">
              <Card>
                <CardHeader>
                  <CardTitle>Asset Distribution</CardTitle>
                  <CardDescription>Your current holdings breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                     {/* Show pie chart only if there are assets in the portfolio */}
                    {portfolioChartData.length > 0 ? (
                        <div className="h-96 w-full">
                        {/* Enhanced Pie Chart for Asset Distribution */}
                        <ResponsiveContainer width="100%" height="100%">
                          <PieChart>
                            <Pie
                              activeIndex={activeIndex}
                              activeShape={renderActiveShape} // Use the custom active shape
                              data={portfolioChartData} // Use dynamic data
                              cx="50%"
                              cy="50%"
                              innerRadius={80} // Adjusted inner radius slightly for better look
                              outerRadius={105} // Adjusted outer radius slightly
                              paddingAngle={3} // Added padding between slices for clarity/aesthetics
                              dataKey="value"
                              onMouseEnter={onPieEnter}
                            >
                              {portfolioChartData.map((entry, index) => 
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              )}
                            </Pie>
                            <Legend
                              layout="vertical"
                              verticalAlign="middle"
                              align="right"
                              wrapperStyle={{ right: -20, top: '50%', transform: 'translateY(-50%)' }} // Adjust legend position
                              formatter={(value, entry, index) => {
                                // Use data from portfolioChartData for legend text
                                const item = portfolioChartData[index];
                                return (
                                  <span style={{ color: entry.color }} className="text-sm font-medium">
                                    {item.name}: <span className="font-bold">{formatCurrency(item.value)}</span>
                                    <span className="text-xs opacity-80"> ({((item.value / totalPortfolioValue) * 100).toFixed(1)}%)</span>
                                  </span>
                                );
                              }}
                            />
                          </PieChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                         <div className="py-8 text-center text-muted-foreground">No assets in your portfolio yet.</div>
                    )}

                    {/* Portfolio Summary Table */}
                    {portfolioSummaryData.length > 0 && ( // Only show table if there are holdings
                    <div className="mt-8">
                      <h3 className="font-medium mb-3">Portfolio Summary</h3>
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b border-border text-left">
                              <th className="pb-2 text-muted-foreground font-medium">Commodity</th>
                              <th className="pb-2 text-muted-foreground font-medium text-right">Value</th>
                              <th className="pb-2 text-muted-foreground font-medium text-right">% of Portfolio</th>
                              <th className="pb-2 text-muted-foreground font-medium text-right">Current Price</th>
                              <th className="pb-2 text-muted-foreground font-medium text-right">24h Change</th>
                            </tr>
                          </thead>
                          <tbody>
                            {/* Use the prepared portfolioSummaryData */}
                            {portfolioSummaryData.map((item, index) => {
                              const percentage = totalPortfolioValue > 0 ? ((item.value / totalPortfolioValue) * 100).toFixed(1) : "0.0";
                              return (
                                <tr key={item.id} className="border-b border-border hover:bg-muted/50 transition-colors">
                                  <td className="py-3 pr-2">
                                    <div className="flex items-center">
                                      <div className="w-3 h-3 rounded-full mr-2 flex-shrink-0" style={{ backgroundColor: item.color }}></div>
                                      <span className="font-medium">{item.name}</span>
                                    </div>
                                  </td>
                                  <td className="py-3 pr-2 text-right">{formatCurrency(item.value)}</td>
                                  <td className="py-3 pr-2 text-right">{percentage}%</td>
                                   <td className="py-3 pr-2 text-right">{formatCurrency(item.currentPrice)}</td>
                                  <td className="py-3 pr-2 text-right">
                                     <div className="flex items-center justify-end">
                                        {item.trending === "up" ? (
                                          <ArrowUpRight className="h-4 w-4 mr-1 text-green-500 flex-shrink-0" />
                                        ) : item.trending === "down" ? (
                                          <ArrowDownRight className="h-4 w-4 mr-1 text-red-500 flex-shrink-0" />
                                        ) : (
                                          <MinusCircle className="h-4 w-4 mr-1 text-gray-400 flex-shrink-0" />
                                        )}
                                        <span className={`text-sm ${ // Ensure text color also reflects trend
                                          item.trending === "up" ? "text-green-500" :
                                          item.trending === "down" ? "text-red-500" : "text-gray-500"
                                        }`}>
                                          {item.trending === "neutral" ? "0.00" :
                                           `${item.trending === "up" ? "+" : ""}${item.priceChange.toFixed(2)}`}% 
                                        </span>
                                      </div>
                                  </td>
                                </tr>
                              );
                            })}
                          </tbody>
                        </table>
                      </div>
                    </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            <TabsContent value="performance"> {/* Renamed tab content */}
              <Card>
                <CardHeader>
                  <CardTitle>Price History</CardTitle>
                  <CardDescription>Historical price movement for selected assets.</CardDescription> {/* Adjusted description */}
                </CardHeader>
                <CardContent>
                  {/* Commodity Selector */}
                  <div className="mb-4 overflow-x-auto">
                    <div className="flex space-x-2">
                      {topCommodities.map((commodity) => (
                        <button
                          key={commodity.id}
                          onClick={() => setSelectedCommodity(commodity)}
                          className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                            selectedCommodity.id === commodity.id
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted hover:bg-muted/80"
                          }`}
                        >
                          {commodity.name}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Selected Commodity Info */}
                  <div className="mt-2 mb-4 flex justify-between items-center">
                    <div>
                      <h3 className="text-xl font-semibold flex items-center">
                        {selectedCommodity.name}
                        <span
                          className="inline-block w-3 h-3 rounded-full ml-2"
                          style={{ backgroundColor: selectedCommodity.color }}
                        ></span>
                      </h3>
                      <div className="flex items-center mt-1">
                        <span className="text-2xl font-bold mr-2">
                          {formatCurrency(selectedCommodity.currentPrice)}
                        </span>
                        <div className={`flex items-center text-sm ${
                          selectedCommodity.trending === "up" ? "text-green-500" :
                          selectedCommodity.trending === "down" ? "text-red-500" : "text-gray-500"
                        }`}>
                          {selectedCommodity.trending === "up" ? (
                            <ArrowUpRight className="h-4 w-4 mr-1" />
                          ) : selectedCommodity.trending === "down" ? (
                            <ArrowDownRight className="h-4 w-4 mr-1" />
                          ) : null}
                          {selectedCommodity.trending === "up" ? "+" : ""}
                          {selectedCommodity.priceChange.toFixed(2)}% {/* Added toFixed(2) */}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-muted-foreground">Previous: {formatCurrency(selectedCommodity.previousPrice)}</div>
                      <div className="text-sm text-muted-foreground">6-Month Change:
                        {(() => {
                           // Ensure data exists before calculating change
                          if (!selectedCommodity.data || selectedCommodity.data.length < 2) return <span className="text-muted-foreground"> N/A</span>;

                          const firstPrice = selectedCommodity.data[0].price;
                          const lastPrice = selectedCommodity.data[selectedCommodity.data.length - 1].price;
                          // Handle division by zero if firstPrice is 0
                          const change = firstPrice === 0 ? (lastPrice > 0 ? 100 : 0) : ((lastPrice - firstPrice) / firstPrice) * 100;
                          return (
                            <span className={change >= 0 ? " text-green-500" : " text-red-500"}>
                              {" "}{change >= 0 ? "+" : ""}{change.toFixed(2)}%
                            </span>
                          );
                        })()}
                      </div>
                    </div>
                  </div>

                  <div className="h-96 rounded-lg bg-muted p-6">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={selectedCommodity.data}
                        margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                      >
                        <defs>
                          <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor={selectedCommodity.color} stopOpacity={0.8}/>
                            <stop offset="95%" stopColor={selectedCommodity.color} stopOpacity={0.1}/>
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted))" />
                        <XAxis dataKey="month" stroke="hsl(var(--muted-foreground))" />
                         {/* Add domain prop to YAxis to ensure it starts from a reasonable minimum */}
                        <YAxis stroke="hsl(var(--muted-foreground))" domain={['dataMin * 0.95', 'dataMax * 1.05']} />
                        <Tooltip
                          contentStyle={{
                            backgroundColor: "hsl(var(--card))",
                            borderColor: "hsl(var(--border))",
                            color: "hsl(var(--card-foreground))"
                          }}
                          formatter={(value) => [formatCurrency(Number(value)), "Price"]}
                        />
                        <Area
                          type="monotone"
                          dataKey="price"
                          stroke={selectedCommodity.color}
                          fillOpacity={1}
                          fill="url(#colorPrice)"
                          strokeWidth={2}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Comparison Chart */}
                  <div className="mt-8">
                    <h3 className="font-medium mb-4">Price Comparison</h3>
                    <div className="h-80 rounded-lg bg-muted p-6">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={priceHistoryData}
                          margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted))" />
                          <XAxis dataKey="month" stroke="hsl(var(--muted-foreground))" />
                           {/* Add domain prop to YAxis */}
                          <YAxis stroke="hsl(var(--muted-foreground))" domain={['dataMin * 0.95', 'dataMax * 1.05']} />
                          <Tooltip
                            contentStyle={{
                              backgroundColor: "hsl(var(--card))",
                              borderColor: "hsl(var(--border))",
                              color: "hsl(var(--card-foreground))"
                            }}
                            formatter={(value, name) => {
                              // Find the commodity by ID (name in this chart's data)
                              const commodity = allCommodities.find(c => c.id === name);
                              return [formatCurrency(Number(value)), commodity ? commodity.name : name];
                            }}
                          />
                          <Legend />
                           {/* Only render lines for commodities that have data points in priceHistoryData */}
                          {topCommodities.filter(c => priceHistoryData.some(d => d[c.id] != null)).map((commodity) => (
                            <Line
                              key={commodity.id}
                              type="monotone"
                              dataKey={commodity.id} // Use commodity ID as dataKey
                              name={commodity.name} // Use commodity name for Legend/Tooltip
                              stroke={commodity.color}
                              strokeWidth={2}
                              dot={false}
                              activeDot={{ r: 6 }}
                            />
                          ))}
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Trade Volume</CardTitle>
                  <CardDescription>Total trade volume over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64 rounded-lg bg-muted p-6">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={tradeVolumeData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted))" /> {/* Use muted stroke */}
                        <XAxis dataKey="date" stroke="hsl(var(--muted-foreground))" /> {/* Use muted-foreground stroke */}
                        <YAxis stroke="hsl(var(--muted-foreground))" /> {/* Use muted-foreground stroke */}
                        <Tooltip
                           contentStyle={{
                            backgroundColor: "hsl(var(--card))",
                            borderColor: "hsl(var(--border))",
                            color: "hsl(var(--card-foreground))"
                          }}
                        />
                        <Line type="monotone" dataKey="volume" stroke="#82ca9d" strokeWidth={2} /> {/* Added strokeWidth */}
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>User Activity</CardTitle>
                  <CardDescription>Number of active users over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64 rounded-lg bg-muted p-6">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={userActivityData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted))" /> {/* Use muted stroke */}
                        <XAxis dataKey="date" stroke="hsl(var(--muted-foreground))" /> {/* Use muted-foreground stroke */}
                        <YAxis stroke="hsl(var(--muted-foreground))" /> {/* Use muted-foreground stroke */}
                        <Tooltip
                          contentStyle={{
                            backgroundColor: "hsl(var(--card))",
                            borderColor: "hsl(var(--border))",
                            color: "hsl(var(--card-foreground))"
                          }}
                        />
                        <Line type="monotone" dataKey="users" stroke="#8884d8" strokeWidth={2} /> {/* Added strokeWidth */}
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
        <div>
          <ServicesSummaryCard />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>Your latest trading activity</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Use transactions from context */}
          {transactions.length === 0 ? (
              <div className="py-4 text-center text-muted-foreground">No recent transactions.</div>
          ) : (
            <div className="overflow-x-auto max-h-64"> {/* Added max height for scroll */}
              <table className="w-full">
                <thead className="sticky top-0 bg-card z-10"> {/* Added sticky header */}
                  <tr className="border-b border-border text-left">
                    <th className="pb-3 text-muted-foreground font-medium">Transaction ID</th>
                    <th className="pb-3 text-muted-foreground font-medium">Type</th>
                    <th className="pb-3 text-muted-foreground font-medium">Asset</th>
                    <th className="pb-3 text-muted-foreground font-medium text-right">Amount/Qty</th> {/* Aligned right */}
                    <th className="pb-3 text-muted-foreground font-medium text-right">Value</th> {/* Aligned right */}
                    <th className="pb-3 text-muted-foreground font-medium text-right">Date</th> {/* Aligned right */}
                  </tr>
                </thead>
                <tbody>
                  {transactions.slice(0, 5).map((tx) => ( // Show only top 5 in dashboard
                    <tr key={tx.id} className="border-b border-border">
                      <td className="py-3 text-sm font-medium pr-4">{tx.id}</td> {/* Added padding */}
                      <td className="py-3 pr-4"> {/* Added padding */}
                        <Badge variant={tx.type === "buy" || tx.type === "deposit" ? "outline" : "secondary"} className={
                            tx.type === "buy" ? "bg-blue-500/10 border-blue-500/30 text-blue-700 dark:bg-blue-500/20 dark:text-blue-400 dark:border-blue-500/40" :
                            tx.type === "sell" ? "bg-purple-500/10 border-purple-500/30 text-purple-700 dark:bg-purple-500/20 dark:text-purple-400 dark:border-purple-500/40" :
                            tx.type === "deposit" ? "bg-green-500/10 border-green-500/30 text-green-700 dark:bg-green-500/20 dark:text-green-400 dark:border-green-500/40" :
                            tx.type === "withdraw" ? "bg-red-500/10 border-red-500/30 text-red-700 dark:bg-red-500/20 dark:text-red-400 dark:border-red-500/40" :
                            "" // Default empty class if type is unknown
                            }>
                          {tx.type.charAt(0).toUpperCase() + tx.type.slice(1)}
                        </Badge>
                      </td>
                      <td className="py-3 pr-4">{tx.method || tx.asset}</td> {/* Added padding */}
                       <td className="py-3 pr-4 text-right">{tx.type === "buy" || tx.type === "sell" ? tx.amount.toLocaleString() : formatCurrency(tx.amount)}</td> {/* Use tx.amount for UGX */}
                       <td className="py-3 pr-4 text-right">{formatCurrency(tx.value)}</td>
                      <td className="py-3 text-right text-muted-foreground text-xs">
                          {new Date(tx.date).toLocaleDateString()}<br/>{new Date(tx.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Dashboard;