import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Sparkles } from "lucide-react";

interface Commodity {
  id: string;
  name: string;
  price: number;
  change: number;
  volume: number;
  lastUpdated: string;
}

// Option 1: Convert Date to string in the interface
interface PriceData {
  id: string;
  name: string;
  price: number;
  change: number;
  volume: number;
  lastUpdated: string; // Changed from Date to string
}

const commodities: PriceData[] = [
  {
    id: "cmd-01",
    name: "Coffee (Arabica)",
    price: 7200,
    change: 0.02,
    volume: 12000,
    lastUpdated: "2024-05-03T10:00:00Z",
  },
  {
    id: "cmd-02",
    name: "Coffee (Robusta)",
    price: 5500,
    change: -0.01,
    volume: 8000,
    lastUpdated: "2024-05-03T09:30:00Z",
  },
  {
    id: "cmd-03",
    name: "Cocoa",
    price: 2800,
    change: 0.03,
    volume: 15000,
    lastUpdated: "2024-05-03T10:15:00Z",
  },
  {
    id: "cmd-04",
    name: "Sesame",
    price: 3200,
    change: 0.015,
    volume: 9500,
    lastUpdated: "2024-05-03T09:45:00Z",
  },
  {
    id: "cmd-05",
    name: "Sunflower",
    price: 2500,
    change: -0.005,
    volume: 11000,
    lastUpdated: "2024-05-03T10:30:00Z",
  },
];

const PriceTable = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Commodity Prices</CardTitle>
        <CardDescription>
          Real-time prices for key agricultural commodities
        </CardDescription>
      </CardHeader>
      <CardContent className="grid gap-4">
        {commodities.map((commodity) => (
          <div
            key={commodity.id}
            className="border rounded-md p-4 flex items-center justify-between"
          >
            <div>
              <h3 className="font-semibold">{commodity.name}</h3>
              <p className="text-sm text-muted-foreground">
                Volume: {commodity.volume}
              </p>
            </div>
            <div className="text-right">
              <p className="text-lg font-bold">{commodity.price} UGX/ton</p>
              <p
                className={`text-sm ${
                  commodity.change >= 0 ? "text-green-500" : "text-red-500"
                }`}
              >
                {commodity.change >= 0 ? "+" : ""}
                {commodity.change * 100}%
              </p>
            </div>
          </div>
        ))}
        <div className="flex items-center p-4 rounded-md bg-muted">
          <Sparkles className="h-4 w-4 mr-2 text-muted-foreground" />
          <p className="text-sm text-muted-foreground">
            Prices updated as of today
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default PriceTable;
