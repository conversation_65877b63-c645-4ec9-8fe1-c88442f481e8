import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Layout from "./components/layout/Layout";
import Index from "./pages/Index";
import Login from "./pages/Login";
import SignUp from "./pages/SignUp";
import Dashboard from "./pages/Dashboard";
import Trade from "./pages/Trade";
import Wallet from "./pages/Wallet";
import NotFound from "./pages/NotFound";
import Settings from "./pages/Settings";
import Contact from "./pages/Contact";
import Terms from "./pages/Terms";
import Privacy from "./pages/Privacy";
import AboutUs from "./pages/AboutUs";
import Careers from "./pages/Careers";

// Services pages
import ServicesIndex from "./pages/services/ServicesIndex";
import Warehousing from "./pages/services/Warehousing";
import Financing from "./pages/services/Financing";
import Insurance from "./pages/services/Insurance";
import Leasing from "./pages/services/Leasing";
import Futures from "./pages/services/Futures";
import Transportation from "./pages/services/Transportation";
import Equipment from "./pages/services/Equipment";
import Agronomy from "./pages/services/Agronomy";

// Import the new WalletProvider
import { WalletProvider } from "./context/WalletContext";



const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster /> {/* Shadcn V1 Toaster */}
      <Sonner /> {/* Shadcn V2 Sonner Toaster (keep both if needed) */}
      <BrowserRouter>
        {/*
          Wrap the routes that need access to the WalletContext.
          Since Dashboard, Wallet, Trade, etc., are within the Layout,
          wrapping the Layout route element with WalletProvider is efficient.
          Login, Signup, and NotFound typically don't need wallet data
          at this top level, so they can remain outside the WalletProvider.
        */}
        <Routes>
           {/* Routes outside the main Layout and WalletProvider */}
           <Route path="/login" element={<Login />} />
           <Route path="/signup" element={<SignUp />} />
           {/* The catch-all NotFound route should usually be outside the Layout */}
           <Route path="*" element={<NotFound />} />


           {/*
             This is the main route that uses the Layout component.
             Wrap the <Layout /> element with the <WalletProvider>.
             All nested routes will then have access to the WalletContext.
           */}
          <Route element={<WalletProvider><Layout /></WalletProvider>}>
            <Route path="/" element={<Index />} /> {/* Index is within the layout */}
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/trade" element={<Trade />} />
            <Route path="/wallet" element={<Wallet />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/about-us" element={<AboutUs />} />
            <Route path="/careers" element={<Careers />} />

            {/* Services routes - assuming these might also need wallet/user data */}
            <Route path="/services" element={<ServicesIndex />} />
            <Route path="/services/warehousing" element={<Warehousing />} />
            <Route path="/services/financing" element={<Financing />} />
            <Route path="/services/insurance" element={<Insurance />} />
            <Route path="/services/leasing" element={<Leasing />} />
            <Route path="/services/futures" element={<Futures />} />
            <Route path="/services/transportation" element={<Transportation />} />
            <Route path="/services/equipment" element={<Equipment />} />
            <Route path="/services/agronomy" element={<Agronomy />} />

            {/* Removed the '*' route from here as it's placed above */}

          </Route>
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;