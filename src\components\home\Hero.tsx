import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { NavLink } from "react-router-dom";
import { useState, useEffect } from "react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from "recharts";
import { commodities, Commodity } from "@/data/commodityData";

const Hero = () => {
  const [chartType, setChartType] = useState<"bar" | "line">("bar");
  const [selectedCommodity, setSelectedCommodity] = useState<Commodity>(commodities[0]);

  const handleCommodityChange = (commodityId: string) => {
    const commodity = commodities.find(c => c.id === commodityId);
    if (commodity) {
      setSelectedCommodity(commodity);
    }
  };

  // Determine the price change class based on the trend
  const getPriceChangeClass = (change: number) => {
    if (change > 0) return "text-green-500";
    if (change < 0) return "text-red-500";
    return "text-yellow-500";
  };

  return (
    <div className="py-12 md:py-24 lg:py-32 flex flex-col items-center text-center">
      <div className="space-y-4 max-w-3xl">
        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tighter">
          <span className="text-primary" style={{ color: '#8BC34A' }}>Trade Agro Commodities</span> with Next-Gen Tokenization
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Securely invest in physical commodities through tokenized ownership on our advanced trading platform.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
          <NavLink to="/signup">
            <Button size="lg" className="gap-2" style={{ backgroundColor: '#8BC34A', color: 'white' }}>
              Get Started <ArrowRight className="h-4 w-4" />
            </Button>
          </NavLink>
          <NavLink to="/trade">
            <Button size="lg" variant="outline">
              Explore Markets
            </Button>
          </NavLink>
        </div>
      </div>

      {/* Visualization */}
      <div className="mt-16 w-full max-w-5xl mx-auto relative">
        <div className="aspect-[16/9] rounded-lg overflow-hidden bg-secondary border border-border relative">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/5" />
          
          {/* Commodity Price Chart with Toggle */}
          <div className="absolute inset-0 p-8 flex flex-col">
            {/* Chart Controls */}
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-4">
                <div className="bg-card bg-opacity-70 backdrop-blur-sm px-4 py-2 rounded-md border border-border">
                  <Select 
                    defaultValue={selectedCommodity.id}
                    onValueChange={handleCommodityChange}
                  >
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select Commodity" />
                    </SelectTrigger>
                    <SelectContent>
                      {commodities.map(commodity => (
                        <SelectItem key={commodity.id} value={commodity.id}>
                          {commodity.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="bg-card bg-opacity-70 backdrop-blur-sm px-4 py-2 rounded-md border border-border">
                  <span className="text-sm font-medium">{selectedCommodity.name}</span>
                  <div className="flex items-center">
                    <span className="text-lg font-bold">{selectedCommodity.currentPrice.toLocaleString()} UGX/kg</span>
                    <span className={`ml-2 text-xs ${getPriceChangeClass(selectedCommodity.priceChange)}`}>
                      {selectedCommodity.priceChange > 0 ? "+" : ""}{selectedCommodity.priceChange}%
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex space-x-2">
                <ToggleGroup type="single" value={chartType} onValueChange={(value) => value && setChartType(value as "bar" | "line")}>
                  <ToggleGroupItem value="bar" aria-label="Bar Chart">
                    Bar
                  </ToggleGroupItem>
                  <ToggleGroupItem value="line" aria-label="Line Chart">
                    Line
                  </ToggleGroupItem>
                </ToggleGroup>
              </div>
            </div>
            
            {/* Chart */}
            <div className="w-full h-full">
              <ResponsiveContainer width="100%" height="100%">
                {chartType === "bar" ? (
                  <BarChart data={selectedCommodity.data}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                    <XAxis dataKey="month" stroke="#888" />
                    <YAxis stroke="#888" />
                    <Tooltip
                      contentStyle={{ backgroundColor: "#222", border: "1px solid #444" }}
                      formatter={(value: any) => [`${value.toLocaleString()} UGX/kg`, "Price"]}
                    />
                    <Bar 
                      dataKey="price" 
                      fill={selectedCommodity.color} 
                      name={`${selectedCommodity.name} Price`} 
                      radius={[4, 4, 0, 0]} 
                    />
                  </BarChart>
                ) : (
                  <LineChart data={selectedCommodity.data}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#444" />
                    <XAxis dataKey="month" stroke="#888" />
                    <YAxis stroke="#888" />
                    <Tooltip
                      contentStyle={{ backgroundColor: "#222", border: "1px solid #444" }}
                      formatter={(value: any) => [`${value.toLocaleString()} UGX/kg`, "Price"]}
                    />
                    <Line
                      type="monotone"
                      dataKey="price"
                      stroke={selectedCommodity.color}
                      strokeWidth={3}
                      dot={{ fill: selectedCommodity.color, r: 4 }}
                      activeDot={{ r: 6, fill: "#F97316" }}
                      name={`${selectedCommodity.name} Price`}
                    />
                  </LineChart>
                )}
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
