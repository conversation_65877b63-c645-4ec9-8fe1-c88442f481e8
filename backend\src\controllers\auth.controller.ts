import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { ApiError } from '../middlewares/errorHandler';
import config from '../config/env';
import { AuthService } from '../services/auth.service';
import { EmailService } from '../services/email.service';
import logger from '../utils/logger';

// Define Role type based on Prisma schema
type Role = 'USER' | 'FARMER' | 'TRADER' | 'TRANSPORTER' | 'WAREHOUSE' | 'ADMIN' | 'SUPER_ADMIN';

export class AuthController {
  private authService: AuthService;
  private emailService: EmailService;
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
    this.authService = new AuthService();
    this.emailService = new EmailService();
  }

  /**
   * Register a new user
   */
  register = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { firstName, lastName, email, password, phoneNumber, country, city, address, role } = req.body;

      // Check if user already exists
      const existingUser = await this.prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        throw new ApiError(409, 'User with this email already exists');
      }

      // Validate role if provided
      if (role && !['USER', 'FARMER', 'TRADER', 'TRANSPORTER', 'WAREHOUSE', 'ADMIN', 'SUPER_ADMIN'].includes(role)) {
        throw new ApiError(400, 'Invalid role specified');
      }

      // Hash password
      const passwordHash = await bcrypt.hash(password, config.BCRYPT_SALT_ROUNDS);

      // Create user with role
      const user = await this.prisma.user.create({
        data: {
          firstName,
          lastName,
          email,
          passwordHash,
          phoneNumber,
          country,
          city,
          address,
          role: (role as Role) || 'USER', // Use provided role or default to USER
          wallet: {
            create: {
              balance: 0,
            },
          },
        },
      });

      // Generate tokens
      const { accessToken, refreshToken } = this.authService.generateTokens(user);

      // Save refresh token
      await this.prisma.token.create({
        data: {
          userId: user.id,
          token: refreshToken,
          type: 'REFRESH',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
      });

      // Try to send verification email, but don't fail registration if it fails
      try {
        // Generate email verification token
        const verificationToken = await this.prisma.token.create({
          data: {
            userId: user.id,
            token: uuidv4(),
            type: 'EMAIL_VERIFICATION',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
          },
        });

        // Send verification email
        await this.emailService.sendVerificationEmail(
          user.email,
          user.firstName,
          verificationToken.token
        );
      } catch (emailError) {
        logger.error('Failed to send verification email:', emailError);
        // Continue with registration even if email fails
      }

      res.status(201).json({
        status: 'success',
        message: 'User registered successfully. Please verify your email.',
        data: {
          user: {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            role: user.role,
            isEmailVerified: user.isEmailVerified,
          },
          tokens: {
            accessToken,
            refreshToken,
          },
        },
      });
    } catch (error) {
      logger.error('Registration error:', error);
      next(error);
    }
  };

  /**
   * Login user
   */
  login = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { email, password } = req.body;

      // Find user
      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        throw new ApiError(401, 'Invalid email or password');
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
      if (!isPasswordValid) {
        throw new ApiError(401, 'Invalid email or password');
      }

      // Generate tokens
      const { accessToken, refreshToken } = this.authService.generateTokens(user);

      // Save refresh token
      await this.prisma.token.create({
        data: {
          userId: user.id,
          token: refreshToken,
          type: 'REFRESH',
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
      });

      res.status(200).json({
        status: 'success',
        message: 'Login successful',
        data: {
          user: {
            id: user.id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
            isEmailVerified: user.isEmailVerified,
          },
          tokens: {
            accessToken,
            refreshToken,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Refresh access token
   */
  refreshToken = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { refreshToken } = req.body;

      // Verify refresh token
      const tokenDoc = await this.prisma.token.findUnique({
        where: { token: refreshToken },
        include: { user: true },
      });

      if (!tokenDoc || tokenDoc.type !== 'REFRESH' || new Date() > tokenDoc.expiresAt) {
        throw new ApiError(401, 'Invalid or expired refresh token');
      }

      // Generate new tokens
      const { accessToken, refreshToken: newRefreshToken } = this.authService.generateTokens(tokenDoc.user);

      // Update refresh token
      await this.prisma.token.update({
        where: { id: tokenDoc.id },
        data: {
          token: newRefreshToken,
          expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        },
      });

      res.status(200).json({
        status: 'success',
        message: 'Token refreshed successfully',
        data: {
          tokens: {
            accessToken,
            refreshToken: newRefreshToken,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Logout user
   */
  logout = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { refreshToken } = req.body;

      // Delete refresh token
      await this.prisma.token.deleteMany({
        where: {
          token: refreshToken,
          type: 'REFRESH',
        },
      });

      res.status(200).json({
        status: 'success',
        message: 'Logout successful',
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Send password reset email
   */
  forgotPassword = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { email } = req.body;

      // Find user
      const user = await this.prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        // Don't reveal that the user doesn't exist
        return res.status(200).json({
          status: 'success',
          message: 'If your email is registered, you will receive a password reset link',
        });
      }

      // Generate password reset token
      const resetToken = await this.prisma.token.create({
        data: {
          userId: user.id,
          token: uuidv4(),
          type: 'PASSWORD_RESET',
          expiresAt: new Date(Date.now() + 1 * 60 * 60 * 1000), // 1 hour
        },
      });

      // Send password reset email
      await this.emailService.sendPasswordResetEmail(
        user.email,
        user.firstName,
        resetToken.token
      );

      res.status(200).json({
        status: 'success',
        message: 'If your email is registered, you will receive a password reset link',
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Reset password with token
   */
  resetPassword = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { token, password } = req.body;

      // Verify token
      const tokenDoc = await this.prisma.token.findFirst({
        where: {
          token,
          type: 'PASSWORD_RESET',
          expiresAt: {
            gt: new Date(),
          },
        },
        include: { user: true },
      });

      if (!tokenDoc) {
        throw new ApiError(400, 'Invalid or expired token');
      }

      // Hash new password
      const passwordHash = await bcrypt.hash(password, config.BCRYPT_SALT_ROUNDS);

      // Update user password
      await this.prisma.user.update({
        where: { id: tokenDoc.userId },
        data: { passwordHash },
      });

      // Delete token
      await this.prisma.token.delete({
        where: { id: tokenDoc.id },
      });

      // Delete all refresh tokens for this user (force logout on all devices)
      await this.prisma.token.deleteMany({
        where: {
          userId: tokenDoc.userId,
          type: 'REFRESH',
        },
      });

      res.status(200).json({
        status: 'success',
        message: 'Password reset successful',
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Verify email with token
   */
  verifyEmail = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { token } = req.body;

      // Verify token
      const tokenDoc = await this.prisma.token.findFirst({
        where: {
          token,
          type: 'EMAIL_VERIFICATION',
          expiresAt: {
            gt: new Date(),
          },
        },
      });

      if (!tokenDoc) {
        throw new ApiError(400, 'Invalid or expired token');
      }

      // Update user
      await this.prisma.user.update({
        where: { id: tokenDoc.userId },
        data: { isEmailVerified: true },
      });

      // Delete token
      await this.prisma.token.delete({
        where: { id: tokenDoc.id },
      });

      res.status(200).json({
        status: 'success',
        message: 'Email verified successfully',
      });
    } catch (error) {
      next(error);
    }
  };
} 