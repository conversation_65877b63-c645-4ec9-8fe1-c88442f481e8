
declare module 'jspdf-autotable' {
  import { jsPDF } from 'jspdf';
  
  interface AutoTableOptions {
    head?: any[][];
    body?: any[][];
    foot?: any[][];
    startY?: number;
    margin?: any;
    theme?: string;
    styles?: any;
    headStyles?: any;
    bodyStyles?: any;
    footStyles?: any;
    horizontalPageBreak?: boolean;
    horizontalPageBreakRepeat?: number;
    includeHiddenHtml?: boolean;
    [key: string]: any;
  }

  interface jsPDF {
    autoTable(options: AutoTableOptions): void;
    autoTable(theme: string, body: any[][], footer?: any[][]): void;
  }
  
  function applyPlugin(jsPDF: any): void;
  export default applyPlugin;
}
