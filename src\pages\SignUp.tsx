import { useState } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { AlertCircle, Eye, EyeOff, Tractor, Store, Truck, Warehouse } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import api from "@/config/api";
import { AxiosError } from "axios";

type ProfessionalIdentity = "farmer" | "trader" | "transporter" | "warehouse";

interface ApiErrorResponse {
  status: string;
  message: string;
  errors?: { field?: string; message: string }[];
  stack?: string;
}

const SignUp = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    phoneNumber: "",
    country: "",
    city: "",
    address: "",
  });
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [identity, setIdentity] = useState<ProfessionalIdentity | "">("");
  const { toast } = useToast();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.id]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    
    // Enhanced validation
    if (!formData.firstName.trim()) {
      setError("First name is required.");
      return;
    }
    
    if (!formData.lastName.trim()) {
      setError("Last name is required.");
      return;
    }
    
    if (!formData.email.trim()) {
      setError("Email is required.");
      return;
    }
    
    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError("Please enter a valid email address.");
      return;
    }
    
    if (!formData.password) {
      setError("Password is required.");
      return;
    }
    
    if (formData.password.length < 8) {
      setError("Password must be at least 8 characters long.");
      return;
    }
    
    // Password complexity validation
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])/;
    if (!passwordRegex.test(formData.password)) {
      setError("Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.");
      return;
    }
    
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match.");
      return;
    }
    
    if (!agreeTerms) {
      setError("You must agree to the terms and conditions.");
      return;
    }
    
    if (!identity) {
      setError("Please select your professional identity.");
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Make API call to register endpoint
      const response = await api.post('/auth/register', {
        firstName: formData.firstName.trim(),
        lastName: formData.lastName.trim(),
        email: formData.email.trim(),
        password: formData.password,
        phoneNumber: formData.phoneNumber?.trim(),
        country: formData.country?.trim(),
        city: formData.city?.trim(),
        address: formData.address?.trim(),
        role: identity.toUpperCase(), // Convert to uppercase to match backend Role enum
      });

      // Store tokens in localStorage
      localStorage.setItem('accessToken', response.data.data.tokens.accessToken);
      localStorage.setItem('refreshToken', response.data.data.tokens.refreshToken);

      toast({
        title: "Registration successful",
        description: "Please check your email to verify your account.",
      });

      // Redirect to email verification page
      navigate('/verify-email');
    } catch (error) {
      console.error('Registration error:', error);
      const axiosError = error as AxiosError<ApiErrorResponse>;
      
      // Enhanced error handling
      let errorMessage = "An error occurred during registration";
      
      if (axiosError.response?.data?.message) {
        errorMessage = axiosError.response.data.message;
      } else if (axiosError.message) {
        errorMessage = axiosError.message;
      }
      
      setError(errorMessage);
      toast({
        title: "Registration failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const identityOptions = [
    {
      id: "farmer",
      title: "Farmer",
      description: "I grow crops and want to sell my produce",
      icon: <Tractor className="h-6 w-6 text-[#8BC34A]" />
    },
    {
      id: "trader",
      title: "Trader",
      description: "I buy and sell agricultural commodities",
      icon: <Store className="h-6 w-6 text-[#8BC34A]" />
    },
    {
      id: "transporter",
      title: "Transporter",
      description: "I provide logistics and transportation services",
      icon: <Truck className="h-6 w-6 text-[#8BC34A]" />
    },
    {
      id: "warehouse",
      title: "Warehouse Operator",
      description: "I manage storage facilities for agricultural goods",
      icon: <Warehouse className="h-6 w-6 text-[#8BC34A]" />
    }
  ];

  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-8rem)] py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold">Create an account</h1>
          <p className="mt-2 text-muted-foreground">Sign up to start trading on Fotis Agro</p>
        </div>

        <Card>
          <form onSubmit={handleSubmit}>
            <CardHeader>
              <CardTitle>Sign Up</CardTitle>
              <CardDescription>Enter your information to create an account</CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  type="text"
                  placeholder="John"
                  value={formData.firstName}
                  onChange={handleChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  type="text"
                  placeholder="Doe"
                  value={formData.lastName}
                  onChange={handleChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={handleChange}
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  Password must be at least 8 characters long
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  type={showPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                />
              </div>

              <div className="space-y-3 pt-4">
                <Label>Select your professional identity</Label>
                <RadioGroup 
                  value={identity} 
                  onValueChange={(value) => setIdentity(value as ProfessionalIdentity)}
                  className="grid grid-cols-1 gap-4 pt-2"
                >
                  {identityOptions.map((option) => (
                    <div key={option.id} className="flex items-center space-x-2">
                      <RadioGroupItem 
                        value={option.id} 
                        id={option.id}
                        className="peer sr-only"
                      />
                      <Label 
                        htmlFor={option.id}
                        className="flex items-center gap-4 rounded-md border-2 border-muted p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-[#8BC34A] [&:has([data-state=checked])]:border-[#8BC34A] cursor-pointer w-full"
                      >
                        <div className="flex-shrink-0 rounded-full bg-muted/20 p-2">
                          {option.icon}
                        </div>
                        <div>
                          <p className="font-medium leading-none">{option.title}</p>
                          <p className="text-sm text-muted-foreground mt-1">{option.description}</p>
                        </div>
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <div className="flex items-center space-x-2 pt-2">
                <Checkbox 
                  id="terms" 
                  checked={agreeTerms}
                  onCheckedChange={(checked) => setAgreeTerms(checked === true)}
                />
                <label
                  htmlFor="terms"
                  className="text-sm text-muted-foreground leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  I agree to the{" "}
                  <NavLink to="/terms" className="text-primary hover:underline">
                    terms and conditions
                  </NavLink>
                </label>
              </div>
            </CardContent>
            
            <CardFooter className="flex flex-col gap-4">
              <Button 
                type="submit" 
                className="w-full" 
                disabled={isLoading}
              >
                {isLoading ? "Creating account..." : "Create Account"}
              </Button>
              <p className="text-sm text-center text-muted-foreground">
                Already have an account?{" "}
                <NavLink to="/login" className="text-primary hover:underline">
                  Sign in
                </NavLink>
              </p>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default SignUp;

