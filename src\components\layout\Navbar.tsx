
import { useState } from "react";
import { NavLink } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Menu, X, LogIn } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import MarketMarquee from "../home/<USER>";
import NavMenu from "./NavMenu";

export const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = useIsMobile();
  const isAuthenticated = false; // Will be replaced with auth state

  const toggleMenu = () => setIsOpen(!isOpen);

  return (
    <header className="sticky top-0 z-50 w-full bg-card border-b border-border">
      <div className="container flex h-20 items-center px-4 sm:px-6">
        <NavLink 
          to="/" 
          className="flex items-center mr-8 hover:scale-105 hover:brightness-110 transition-transform transition-filter"
        >
          <img 
            src="/lovable-uploads/aa3d1364-5e25-4eeb-8811-52dd7d593425.png" 
            alt="Fotis Agro" 
            width={300}
            height={300}
            className="object-contain"
            style={{ maxHeight: '150px', width: 'auto' }}
            loading="eager"
          />
        </NavLink>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-3 flex-1">
          <NavMenu />
        </nav>

        <div className="hidden md:flex ml-auto items-center space-x-3">
          {isAuthenticated ? (
            <Button variant="outline" className="ml-2">
              Account
            </Button>
          ) : (
            <>
              <NavLink to="/login">
                <Button variant="outline" className="font-medium">Login</Button>
              </NavLink>
              <NavLink to="/signup">
                <Button style={{ backgroundColor: '#8BC34A', color: 'white' }} className="font-medium">Sign Up</Button>
              </NavLink>
            </>
          )}
        </div>

        {/* Mobile Navigation */}
        <div className="md:hidden flex flex-1 justify-end">
          <Button variant="ghost" onClick={toggleMenu}>
            {isOpen ? <X /> : <Menu />}
          </Button>
        </div>
      </div>

      {/* Market Marquee */}
      <MarketMarquee />

      {/* Mobile Menu */}
      {isMobile && isOpen && (
        <div className="md:hidden absolute top-20 inset-x-0 bg-card border-b border-border z-50">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <NavMenu mobile />

            {!isAuthenticated && (
              <div className="pt-4 pb-3 border-t border-border">
                <NavLink
                  to="/login"
                  onClick={() => setIsOpen(false)}
                >
                  <Button variant="outline" className="w-full mb-2">
                    <LogIn className="mr-2 h-4 w-4" /> Login
                  </Button>
                </NavLink>
                <NavLink
                  to="/signup"
                  onClick={() => setIsOpen(false)}
                >
                  <Button className="w-full" style={{ backgroundColor: '#8BC34A', color: 'white' }}>Sign Up</Button>
                </NavLink>
              </div>
            )}
          </div>
        </div>
      )}
    </header>
  );
};

export default Navbar;
