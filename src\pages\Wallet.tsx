import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowUpRight, ArrowDownRight, Wallet as WalletIcon, Plus, AlertCircle, History, CreditCard, University, Landmark } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { BuyAssetsModal } from "@/components/wallet/BuyAssetsModal";
import { SellAssetModal } from "@/components/wallet/SellAssetModal";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { commodities } from "@/data/commodityData";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { formatCurrency } from "@/utils/currencyFormatter";
import { cn } from "@/lib/utils";

// Import types and context hook from the new files
import { useWalletContext } from "@/context/WalletContext";
import { UserAsset, Transaction, PaymentMethod, PaymentMethodKey } from "@/types/wallet";


// Custom SVG Icons for Mobile Money for better visual appeal (keep these)
const MtnIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="12" cy="12" r="10" fill="#FFCC00"/>
    <path d="M6.5 12.5L9 10L12 13L15 10L17.5 12.5" stroke="#004B8D" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M9 15.5H15" stroke="#D90000" strokeWidth="1.5" strokeLinecap="round"/>
  </svg>
);

const AirtelIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="24" height="24" rx="4" fill="#ED1C24"/>
    <path d="M6 12H10L12 8L14 12H18" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M12 12V16" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const SafaricomIcon = () => (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="24" height="24" rx="4" fill="#4CAF50"/>
        <path d="M12 6C9.79086 6 8 7.79086 8 10C8 12.2091 9.79086 14 12 14C14.2091 14 16 12.2091 16 10C16 7.79086 14.2091 6 12 6Z" fill="white"/>
        <path d="M12 14V18L10 16.5M12 18L14 16.5" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
);

// Keep the payment methods data locally in Wallet as it's specific to the UI,
// but ensure its type matches the imported PaymentMethod
const paymentMethods: PaymentMethod[] = [
  { key: "mtn", name: "MTN Mobile Money", type: "mobile_money", icon: <MtnIcon />, description: "Uganda, Rwanda", placeholder: "e.g., 077XXXXXXX", note: "Ensure your MTN line is active and has sufficient funds for transaction fees if any." },
  { key: "airtel", name: "Airtel Money", type: "mobile_money", icon: <AirtelIcon />, description: "Uganda, Kenya", placeholder: "e.g., 075XXXXXXX / 073XXXXXXX", note: "You will receive a prompt on your phone to enter your PIN." },
  { key: "safaricom", name: "Safaricom M-PESA", type: "mobile_money", icon: <SafaricomIcon />, description: "Kenya", placeholder: "e.g., 07XXXXXXXX", note: "Use the M-PESA STK push or PayBill option as instructed." },
  { key: "bank", name: "Bank Transfer", type: "bank", icon: <Landmark /> , description: "All Supported Regions"},
  { key: "card", name: "Credit/Debit Card", type: "card", icon: <CreditCard /> , description: "Visa, Mastercard"},
  { key: "xentripay", name: "XentriPay", type: "digital_wallet", icon: <WalletIcon className="text-blue-500" />, comingSoon: true, region: "Rwanda", description: "Digital Wallet (Coming Soon)" },
];


const Wallet = () => {
  // Use context instead of local state for shared data
  const {
      cashBalance,
      userAssets,
      transactions,
      totalPortfolioValue,
      activeTransaction,
      isLoadingTransactions,
      depositFunds,
      withdrawFunds,
      buyAsset,
      sellAsset,
      loadTransactionHistory,
  } = useWalletContext();

  const { toast } = useToast();

  // Keep local state for modal visibility and form inputs
  const [isBuyModalOpen, setIsBuyModalOpen] = useState(false);
  const [selectedAssetToSell, setSelectedAssetToSell] = useState<UserAsset | null>(null);
  const [transactionAmount, setTransactionAmount] = useState("");
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null); // Use imported type
  const [accountDetails, setAccountDetails] = useState<{ [key: string]: string }>({});

  // No longer need useEffect here to load transactions; the context does it

  // Handlers call context actions
  const handleBuyAssetClick = (assetId: string, quantity: number, totalCost: number) => {
      buyAsset(assetId, quantity, totalCost); // Call context action
      setIsBuyModalOpen(false); // Close modal locally
  };

  const handleSellAssetClick = (quantity: number, proceeds: number) => {
      if (!selectedAssetToSell) return;
      sellAsset(selectedAssetToSell.id, quantity, proceeds); // Call context action
      setSelectedAssetToSell(null); // Close modal locally
  };

  const handleDepositWithdrawSubmit = (type: "deposit" | "withdraw") => {
       const amount = parseFloat(transactionAmount);
       // Basic UI validation before calling context
       if (isNaN(amount) || amount <= 0) { toast({ title: "Invalid Amount", variant: "destructive" }); return; }
       if (!selectedPaymentMethod) { toast({ title: "No Payment Method Selected", variant: "destructive" }); return; }
       if (type === "withdraw" && amount > cashBalance) { toast({ title: "Insufficient Balance", variant: "destructive" }); return; }

       // Call the appropriate context action, passing the selected PaymentMethod object
       if (type === "deposit") {
           depositFunds(amount, selectedPaymentMethod, accountDetails);
       } else {
           withdrawFunds(amount, selectedPaymentMethod, accountDetails);
       }

       // Clear form fields *after* initiating the transaction
       setTransactionAmount("");
       setSelectedPaymentMethod(null);
       setAccountDetails({});
  };


  const renderPaymentMethodForm = (type: "deposit" | "withdraw") => {
    if (!selectedPaymentMethod) return null;
    return (
      <div className="mt-4 space-y-4 p-4 border rounded-md bg-card shadow-sm">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center">
            {selectedPaymentMethod.icon && React.isValidElement(selectedPaymentMethod.icon) ?
                React.cloneElement(selectedPaymentMethod.icon as React.ReactElement, { className: "mr-2 h-6 w-6 flex-shrink-0" })
                : null
            }
            <h3 className="font-semibold text-lg">{selectedPaymentMethod.name}</h3>
          </div>
          {/* Disable change button if transaction is active */}
          <Button variant="ghost" size="sm" onClick={() => {setSelectedPaymentMethod(null); setTransactionAmount(""); setAccountDetails({});}} disabled={!!activeTransaction}>Change</Button>
        </div>

        {selectedPaymentMethod.comingSoon ? (
             <Alert variant="default" className="bg-amber-50 border-amber-200 text-amber-700 dark:bg-amber-900/30 dark:border-amber-700 dark:text-amber-400">
                <AlertCircle className="h-4 w-4 !text-amber-600 dark:!text-amber-400" />
                <AlertDescription>
                {selectedPaymentMethod.name} is {selectedPaymentMethod.description}.
                </AlertDescription>
            </Alert>
        ) : ( <>
            <div>
                <Label htmlFor="amount" className="text-sm font-medium">Amount (UGX)</Label>
                {/* Disable input if transaction is active */}
                <Input id="amount" type="number" value={transactionAmount} onChange={(e) => setTransactionAmount(e.target.value)} placeholder="Enter amount" className="mt-1" disabled={!!activeTransaction}/>
            </div>
            {selectedPaymentMethod.type === "mobile_money" && (
                <div>
                    <Label htmlFor="phone" className="text-sm font-medium">Phone Number</Label>
                    <Input id="phone" type="tel" value={accountDetails.phone || ""} onChange={(e) => setAccountDetails(prev => ({...prev, phone: e.target.value}))} placeholder={selectedPaymentMethod.placeholder || "e.g., 07XXXXXXXX"} className="mt-1" disabled={!!activeTransaction}/>
                    {selectedPaymentMethod.note && <p className="text-xs text-muted-foreground mt-1">{selectedPaymentMethod.note}</p>}
                </div>
            )}
             {selectedPaymentMethod.type === "bank" && (
                 <>
                    <div>
                        <Label htmlFor="bankName" className="text-sm font-medium">Bank Name</Label>
                        <Input id="bankName" value={accountDetails.bankName || ""} onChange={(e) => setAccountDetails(prev => ({...prev, bankName: e.target.value}))} placeholder="e.g., Stanbic Bank" className="mt-1" disabled={!!activeTransaction}/>
                    </div>
                    <div>
                        <Label htmlFor="accountNumber" className="text-sm font-medium">Account Number</Label>
                        <Input id="accountNumber" value={accountDetails.accountNumber || ""} onChange={(e) => setAccountDetails(prev => ({...prev, accountNumber: e.target.value}))} placeholder="Your bank account number" className="mt-1" disabled={!!activeTransaction}/>
                    </div>
                    <div>
                        <Label htmlFor="accountName" className="text-sm font-medium">Account Holder Name</Label>
                        <Input id="accountName" value={accountDetails.accountName || ""} onChange={(e) => setAccountDetails(prev => ({...prev, accountName: e.target.value}))} placeholder="Full name as on bank account" className="mt-1" disabled={!!activeTransaction}/>
                    </div>
                </>
            )}
            {selectedPaymentMethod.type === "card" && (
                <>
                    <div>
                        <Label htmlFor="cardNumber" className="text-sm font-medium">Card Number</Label>
                        <Input id="cardNumber" value={accountDetails.cardNumber || ""} onChange={(e) => setAccountDetails(prev => ({...prev, cardNumber: e.target.value}))} placeholder="XXXX XXXX XXXX XXXX" className="mt-1" disabled={!!activeTransaction}/>
                    </div>
                   <div className="grid grid-cols-2 gap-4">
                        <div>
                            <Label htmlFor="expiryDate" className="text-sm font-medium">Expiry Date</Label>
                            <Input id="expiryDate" value={accountDetails.expiryDate || ""} onChange={(e) => setAccountDetails(prev => ({...prev, expiryDate: e.target.value}))} placeholder="MM/YY" className="mt-1" disabled={!!activeTransaction}/>
                        </div>
                        <div>
                            <Label htmlFor="cvv" className="text-sm font-medium">CVV</Label>
                            <Input id="cvv" value={accountDetails.cvv || ""} onChange={(e) => setAccountDetails(prev => ({...prev, cvv: e.target.value}))} placeholder="123" className="mt-1" disabled={!!activeTransaction}/>
                        </div>
                   </div>
                </>
            )}
            {/* Disable button if transaction is active */}
            <Button onClick={() => handleDepositWithdrawSubmit(type)} className="w-full bg-[#8BC34A] hover:bg-[#8BC34A]/90 text-white mt-2" disabled={!!activeTransaction}>
                {type === "deposit" ? "Confirm Deposit" : "Confirm Withdrawal"}
            </Button>
             {type === "deposit" && selectedPaymentMethod.type === "mobile_money" && (
                    <Alert className="mt-2">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                        After confirming, you may receive a prompt on your phone to enter your Mobile Money PIN to authorize the payment.
                        </AlertDescription>
                    </Alert>
                )}
        </> )}
      </div>
    );
  };

  return (
    <div className="space-y-6 p-4 md:p-6">
      <h1 className="text-3xl font-bold">My Wallet</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2 shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="text-2xl">Asset Portfolio</CardTitle>
            <CardDescription>Overview of your tokenized agricultural assets.</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Show if there are assets with a balance greater than 0 */}
            {userAssets.some(asset => asset.balance > 0) ? (
                <div className="space-y-3">
                {/* Filter for assets with balance > 0 */}
                {userAssets.filter(asset => asset.balance > 0).map((asset) => (
                    <div key={asset.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer" onClick={() => setSelectedAssetToSell(asset)}>
                    <div className="flex items-center">
                        <div className="h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-3 text-[#8BC34A] font-bold text-lg">
                        {asset.symbol.charAt(0)}
                        </div>
                        <div>
                        <div className="font-semibold">{asset.name} <span className="text-xs text-muted-foreground">({asset.symbol})</span></div>
                        {/* Ensure balance is displayed correctly */}
                        <div className="text-xs text-muted-foreground">{asset.balance.toFixed(2)} units @ {formatCurrency(asset.price)}/unit</div>
                        </div>
                    </div>
                    <div className="text-right">
                        {/* Ensure value is displayed correctly */}
                        <div className="font-semibold text-md">{formatCurrency(asset.value)}</div>
                        <div className={`text-xs flex items-center justify-end ${asset.change >= 0 ? "text-green-600" : "text-red-600"}`}>
                        {asset.change >= 0 ? <ArrowUpRight className="h-3 w-3" /> : <ArrowDownRight className="h-3 w-3" />}
                        {Math.abs(asset.change).toFixed(2)}%
                        </div>
                    </div>
                    </div>
                ))}
                </div>
            ) : (
                <p className="text-muted-foreground text-center py-4">No assets in your portfolio yet. Start by buying some!</p>
            )}
          </CardContent>
          <CardFooter className="flex flex-col sm:flex-row justify-between items-start sm:items-center pt-4 border-t">
            <div className="mb-4 sm:mb-0">
              <div className="text-sm text-muted-foreground">Total Portfolio Value</div>
              <div className="text-2xl font-bold text-[#8BC34A]">{formatCurrency(totalPortfolioValue)}</div>
            </div>
            <Button onClick={() => setIsBuyModalOpen(true)} className="w-full sm:w-auto bg-[#8BC34A] hover:bg-[#8BC34A]/90 text-white">
              <Plus className="mr-2 h-4 w-4" /> Buy Agricultural Assets
            </Button>
          </CardFooter>
        </Card>

        <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="text-2xl">Cash Balance</CardTitle>
            <CardDescription>Funds available in UGX.</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col">
            <div className="text-center py-6 border-b mb-4">
              <WalletIcon className="h-16 w-16 mx-auto mb-3 text-[#8BC34A]" />
              <div className="text-4xl font-bold text-[#8BC34A]">{formatCurrency(cashBalance)}</div>
            </div>

            {activeTransaction && (
                <Alert className={cn("mb-4", activeTransaction.status === "failed" ? "border-red-500 text-red-700" : activeTransaction.status === "completed" ? "border-green-500 text-green-700" : "border-blue-500 text-blue-700")}> {/* Added completed status styling */}
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                         Transaction ({activeTransaction.id}) is {activeTransaction.status}...
                    </AlertDescription>
                </Alert>
            )}

            <Tabs defaultValue="deposit" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-4">
                <TabsTrigger value="deposit" className="text-sm data-[state=active]:bg-[#8BC34A]/20 data-[state=active]:text-[#8BC34A] data-[state=active]:font-semibold" disabled={!!activeTransaction}>Deposit Funds</TabsTrigger>
                <TabsTrigger value="withdraw" className="text-sm data-[state=active]:bg-[#8BC34A]/20 data-[state=active]:text-[#8BC34A] data-[state=active]:font-semibold" disabled={!!activeTransaction}>Withdraw Funds</TabsTrigger>
              </TabsList>

              {/* renderPaymentMethodForm handles button/input disabling based on activeTransaction */}
              <TabsContent value="deposit">
                {!selectedPaymentMethod ? (
                  <div className="space-y-2 animate-fadeIn">
                    <p className="text-sm text-muted-foreground mb-3">Choose a method to add funds:</p>
                    {paymentMethods.map(method => (
                      <Button key={method.key} variant="outline" className="w-full justify-start h-14 text-left p-3 hover:border-[#8BC34A]" onClick={() => setSelectedPaymentMethod(method)} disabled={(method.key === 'xentripay' && method.comingSoon) || !!activeTransaction} >
                        {method.icon && React.isValidElement(method.icon) ?
                            React.cloneElement(method.icon as React.ReactElement, { className: "mr-3 h-6 w-6 flex-shrink-0" })
                            : null
                        }
                        <div className="flex-grow">
                            <span className="font-medium">{method.name}</span>
                            {method.description && <p className="text-xs text-muted-foreground">{method.description}</p>}
                        </div>
                        {method.key === 'xentripay' && method.comingSoon && <Badge variant="outline" className="ml-auto bg-amber-100 text-amber-700 border-amber-300 text-xs">Soon</Badge>}
                      </Button>
                    ))}
                  </div>
                ) : renderPaymentMethodForm("deposit")}
              </TabsContent>

              <TabsContent value="withdraw">
              {!selectedPaymentMethod ? (
                  <div className="space-y-2 animate-fadeIn">
                    <p className="text-sm text-muted-foreground mb-3">Choose a method to withdraw funds:</p>
                    {/* Filter out 'card' type for withdrawals if not supported */}
                    {paymentMethods.filter(m => m.type !== 'card').map(method => (
                      <Button key={method.key} variant="outline" className="w-full justify-start h-14 text-left p-3 hover:border-[#8BC34A]" onClick={() => setSelectedPaymentMethod(method)} disabled={(method.key === 'xentripay' && method.comingSoon) || !!activeTransaction} >
                        {method.icon && React.isValidElement(method.icon) ?
                            React.cloneElement(method.icon as React.ReactElement, { className: "mr-3 h-6 w-6 flex-shrink-0" })
                            : null
                        }
                         <div className="flex-grow">
                            <span className="font-medium">{method.name}</span>
                            {method.description && <p className="text-xs text-muted-foreground">{method.description}</p>}
                        </div>
                        {method.key === 'xentripay' && method.comingSoon && <Badge variant="outline" className="ml-auto bg-amber-100 text-amber-700 border-amber-300 text-xs">Soon</Badge>}
                      </Button>
                    ))}
                  </div>
                ) : renderPaymentMethodForm("withdraw")}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
        <CardHeader className="flex flex-row items-center justify-between pb-3 border-b">
          <div>
            <CardTitle className="text-xl">Transaction History</CardTitle>
            <CardDescription>Your recent account activities.</CardDescription>
          </div>
          {/* Disable refresh button if transactions are loading or an active tx exists */}
          <Button variant="ghost" size="icon" onClick={loadTransactionHistory} className="text-[#8BC34A] hover:bg-[#8BC34A]/10" disabled={isLoadingTransactions || !!activeTransaction}>
            <History className={cn("h-5 w-5", isLoadingTransactions && "animate-spin")} />
          </Button>
        </CardHeader>
        <CardContent className="pt-4">
          {isLoadingTransactions ? (
            <div className="py-8 text-center"><div className="animate-spin h-8 w-8 border-4 border-[#8BC34A]/20 border-t-[#8BC34A] rounded-full mx-auto mb-4"></div><p className="text-muted-foreground">Loading transactions...</p></div>
          ) : transactions.length === 0 ? (
            <div className="py-10 text-center text-muted-foreground">No transaction history found.</div>
          ) : (
            <div className="max-h-[400px] overflow-y-auto pr-2">
              <table className="w-full text-sm">
                <thead className="sticky top-0 bg-card z-10">
                  <tr className="border-b">
                    <th className="py-2 font-semibold text-left text-muted-foreground">Type</th>
                    <th className="py-2 font-semibold text-left text-muted-foreground">Details</th>
                    <th className="py-2 font-semibold text-right text-muted-foreground">Amount/Qty</th>
                    <th className="py-2 font-semibold text-right text-muted-foreground">Value (UGX)</th>
                    <th className="py-2 font-semibold text-center text-muted-foreground">Status</th>
                    <th className="py-2 font-semibold text-right text-muted-foreground">Date</th>
                  </tr>
                </thead>
                <tbody>
                  {transactions.map((tx) => (
                    <tr key={tx.id} className="border-b hover:bg-muted/30 transition-colors">
                      <td className="py-3 pr-2">
                        <Badge
                          variant="outline"
                          className={cn(
                            "font-medium text-xs py-1 px-2",
                            tx.type === "deposit" && "bg-green-500/10 text-green-700 border-green-500/30 dark:bg-green-500/20 dark:text-green-400 dark:border-green-500/40",
                            tx.type === "withdraw" && "bg-red-500/10 text-red-700 border-red-500/30 dark:bg-red-500/20 dark:text-red-400 dark:border-red-500/40",
                            tx.type === "buy" && "bg-blue-500/10 text-blue-700 border-blue-500/30 dark:bg-blue-500/20 dark:text-blue-400 dark:border-blue-500/40",
                            tx.type === "sell" && "bg-purple-500/10 text-purple-700 border-purple-500/30 dark:bg-purple-500/20 dark:text-purple-400 dark:border-purple-500/40",
                          )}
                        >
                          {tx.type.charAt(0).toUpperCase() + tx.type.slice(1)}
                        </Badge>
                      </td>
                      {/* Display method for deposits/withdrawals, asset name for buy/sell */}
                      <td className="py-3 pr-2">{tx.method || tx.asset}</td>
                       <td className="py-3 pr-2 text-right font-medium">
                           {/* Simplify amount display for buy/sell */}
                           {tx.type === "buy" || tx.type === "sell" ? tx.amount.toLocaleString() : formatCurrency(tx.amount)}
                       </td>
                       <td className="py-3 pr-2 text-right font-medium">{formatCurrency(tx.value)}</td>
                      <td className="py-3 pr-2 text-center">
                        <Badge
                          className={cn(
                            "text-xs py-1 px-2",
                            tx.status === "completed" && "bg-green-600 text-white",
                            tx.status === "pending" && "bg-yellow-500 text-black",
                            tx.status === "processing" && "bg-blue-500 text-white",
                            tx.status === "failed" && "bg-red-600 text-white",
                          )}
                        >
                          {tx.status.charAt(0).toUpperCase() + tx.status.slice(1)}
                        </Badge>
                      </td>
                      <td className="py-3 text-right text-muted-foreground text-xs">{new Date(tx.date).toLocaleDateString()}<br/>{new Date(tx.date).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pass context data and actions to modals */}
      {selectedAssetToSell && ( <SellAssetModal isOpen={!!selectedAssetToSell} onClose={() => setSelectedAssetToSell(null)} onSell={handleSellAssetClick} asset={selectedAssetToSell} /> )}
      <BuyAssetsModal isOpen={isBuyModalOpen} onClose={() => setIsBuyModalOpen(false)} onBuy={handleBuyAssetClick} availableAssets={userAssets} cashBalance={cashBalance} />
    </div>
  );
};

// Remove the local interfaces for UserAsset, Transaction, and the mock useWalletContext hook
// Interface for the asset data
// interface UserAsset { ... } // REMOVE
// Interface for transaction data
// interface Transaction { ... } // REMOVE
// function useWalletContext() { ... } // REMOVE

export default Wallet;