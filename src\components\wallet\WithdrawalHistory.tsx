import { useState, useEffect } from "react";
import { WithdrawalResponse, getWithdrawalHistory } from "@/services/walletService";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, History, ExternalLink } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

interface WithdrawalHistoryProps {
  onViewDetails: (withdrawal: WithdrawalResponse) => void;
}

export const WithdrawalHistory = ({ onViewDetails }: WithdrawalHistoryProps) => {
  const [withdrawals, setWithdrawals] = useState<WithdrawalResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const itemsPerPage = 5;

  const fetchHistory = async (pageNumber: number) => {
    setLoading(true);
    setError(null);
    try {
      const result = await getWithdrawalHistory(pageNumber, itemsPerPage);
      setWithdrawals(result.withdrawals);
      setTotalItems(result.total);
    } catch (err) {
      console.error("Failed to fetch withdrawal history", err);
      setError("Failed to load withdrawal history. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHistory(page);
  }, [page]);

  const handleNextPage = () => {
    if ((page * itemsPerPage) < totalItems) {
      setPage(page + 1);
    }
  };

  const handlePrevPage = () => {
    if (page > 1) {
      setPage(page - 1);
    }
  };

  const getStatusBadge = (status: WithdrawalResponse["status"]) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-up/10 border-up/30 text-up">Completed</Badge>;
      case "failed":
        return <Badge className="bg-down/10 border-down/30 text-down">Failed</Badge>;
      case "processing":
        return <Badge className="bg-amber-500/10 border-amber-500/30 text-amber-500">Processing</Badge>;
      case "pending":
      default:
        return <Badge className="bg-muted border-muted-foreground/30 text-muted-foreground">Pending</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <History className="mr-2 h-5 w-5" /> 
              Withdrawal History
            </CardTitle>
            <CardDescription>View your previous withdrawal requests</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="text-down text-center py-4">
            {error}
            <Button 
              variant="link" 
              onClick={() => fetchHistory(page)}
              className="ml-2"
            >
              Try Again
            </Button>
          </div>
        )}

        {loading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex justify-between items-center p-4 border border-border rounded-lg">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-24" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-6 w-16" />
                </div>
              </div>
            ))}
          </div>
        ) : withdrawals.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No withdrawal history found
          </div>
        ) : (
          <div className="space-y-3">
            {withdrawals.map((withdrawal) => (
              <div 
                key={withdrawal.id} 
                className="flex flex-col sm:flex-row sm:items-center justify-between p-4 border border-border rounded-lg bg-card hover:border-primary/40 transition-colors"
              >
                <div className="mb-3 sm:mb-0">
                  <div className="font-medium">
                    ${withdrawal.amount.toFixed(2)}
                    <span className="text-xs text-muted-foreground ml-1">
                      (Fee: ${withdrawal.fee.toFixed(2)})
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatDate(withdrawal.dateInitiated)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Ref: {withdrawal.reference}
                  </div>
                </div>
                <div className="flex flex-col items-end space-y-2">
                  {getStatusBadge(withdrawal.status)}
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-xs h-8 px-2" 
                    onClick={() => onViewDetails(withdrawal)}
                  >
                    <ExternalLink className="h-3 w-3 mr-1" /> Details
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      {!loading && withdrawals.length > 0 && (
        <CardFooter className="flex justify-between">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handlePrevPage}
            disabled={page === 1}
          >
            Previous
          </Button>
          <span className="text-sm text-muted-foreground">
            Page {page} of {Math.ceil(totalItems / itemsPerPage)}
          </span>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleNextPage}
            disabled={(page * itemsPerPage) >= totalItems}
          >
            Next
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}; 