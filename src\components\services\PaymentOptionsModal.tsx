
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { AlertCircle, Phone, CreditCard, Info, CheckCircle, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface PaymentOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  disabled?: boolean;
  comingSoon?: boolean;
}

interface PaymentOptionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  onPaymentComplete: () => void;
  insufficientFunds?: boolean;
  walletBalance?: number;
}

const PaymentOptionsModal: React.FC<PaymentOptionsModalProps> = ({
  isOpen,
  onClose,
  amount,
  onPaymentComplete,
  insufficientFunds = false,
  walletBalance = 0
}) => {
  const { toast } = useToast();
  const [selectedPayment, setSelectedPayment] = React.useState<string | null>(null);
  const [processing, setProcessing] = React.useState(false);
  const [paymentComplete, setPaymentComplete] = React.useState(false);

  // Reset state when modal opens/closes
  React.useEffect(() => {
    if (isOpen) {
      setSelectedPayment(null);
      setProcessing(false);
      setPaymentComplete(false);
    }
  }, [isOpen]);

  const paymentOptions: PaymentOption[] = [
    {
      id: "wallet",
      name: "Wallet Balance",
      description: `Use your wallet balance (${walletBalance.toLocaleString()} UGX available)`,
      icon: <div className="h-9 w-9 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 12V7H5a2 2 0 0 1 0-4h14v4"/><path d="M3 5v14a2 2 0 0 0 2 2h16v-5"/><path d="M18 12a2 2 0 0 0 0 4h4v-4Z"/></svg>
            </div>,
      disabled: insufficientFunds
    },
    {
      id: "mtn",
      name: "MTN Mobile Money",
      description: "Pay using MTN Mobile Money service",
      icon: <div className="h-9 w-9 rounded-full bg-yellow-400 flex items-center justify-center text-yellow-800">
              <Phone size={18} />
            </div>
    },
    {
      id: "airtel",
      name: "Airtel Money",
      description: "Pay using Airtel Money service",
      icon: <div className="h-9 w-9 rounded-full bg-red-100 flex items-center justify-center text-red-500">
              <Phone size={18} />
            </div>
    },
    {
      id: "visa",
      name: "Visa Payment",
      description: "Pay using Visa credit/debit card",
      icon: <div className="h-9 w-9 rounded-full bg-blue-50 flex items-center justify-center text-blue-700">
              <CreditCard size={18} />
            </div>
    },
    {
      id: "centripay",
      name: "Centripay",
      description: "Coming soon to Rwanda",
      icon: <div className="h-9 w-9 rounded-full bg-gray-100 flex items-center justify-center text-gray-500">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/><path d="M12 17h.01"/></svg>
            </div>,
      comingSoon: true,
      disabled: true
    }
  ];

  const handleSelectPayment = (id: string) => {
    // Only allow selection if not disabled
    const option = paymentOptions.find(opt => opt.id === id);
    if (option && !option.disabled) {
      setSelectedPayment(id);
    }
  };

  const handleProceedPayment = async () => {
    if (!selectedPayment) return;
    
    setProcessing(true);
    
    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    if (selectedPayment === "wallet") {
      if (walletBalance < amount) {
        toast({
          title: "Insufficient funds",
          description: "Please top up your wallet or select another payment method.",
          variant: "destructive"
        });
        setProcessing(false);
        return;
      }
      
      // Payment successful
      setPaymentComplete(true);
      toast({
        title: "Payment successful",
        description: `${amount.toLocaleString()} UGX has been deducted from your wallet.`,
      });
      
      // Wait a moment before closing
      setTimeout(() => {
        onPaymentComplete();
        onClose();
      }, 1500);
    } else {
      // For other payment methods, show instructions or redirect
      const paymentMethod = paymentOptions.find(p => p.id === selectedPayment)?.name;
      toast({
        title: `${paymentMethod} selected`,
        description: "You will be redirected to complete the payment.",
      });
      
      // Simulate redirection or payment process
      setTimeout(() => {
        setPaymentComplete(true);
        setTimeout(() => {
          onPaymentComplete();
          onClose();
        }, 1500);
      }, 1500);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Payment Options</DialogTitle>
          <DialogDescription>
            Select a payment method to complete your booking
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Payment amount */}
          <div className="p-4 bg-primary/5 rounded-lg">
            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-1">Total Amount</p>
              <p className="text-2xl font-bold">{amount.toLocaleString()} UGX</p>
            </div>
          </div>
          
          {/* Insufficient funds warning */}
          {insufficientFunds && selectedPayment === "wallet" && (
            <div className="flex items-center p-3 text-sm bg-yellow-50 border border-yellow-200 rounded-lg text-yellow-800">
              <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
              Your wallet balance is insufficient. Please select another payment method or top up your wallet.
            </div>
          )}
          
          {/* Payment options */}
          <div className="space-y-3">
            <p className="text-sm font-medium">Select Payment Method</p>
            
            {paymentOptions.map((option) => (
              <HoverCard key={option.id}>
                <HoverCardTrigger asChild>
                  <Card
                    className={`p-3 flex items-center hover:bg-secondary/40 cursor-pointer ${
                      option.disabled ? "opacity-60 cursor-not-allowed" : ""
                    } ${selectedPayment === option.id ? "border-primary bg-primary/5" : "border-border"}`}
                    onClick={() => handleSelectPayment(option.id)}
                  >
                    <div className="flex items-center flex-1">
                      {option.icon}
                      <div className="ml-3">
                        <p className="font-medium">{option.name}</p>
                        <p className="text-xs text-muted-foreground">{option.description}</p>
                      </div>
                    </div>
                    {option.comingSoon && (
                      <span className="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600">
                        Coming Soon
                      </span>
                    )}
                  </Card>
                </HoverCardTrigger>
                <HoverCardContent className="w-72" align="start">
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center">
                      <Info className="h-4 w-4 mr-2 text-muted-foreground" />
                      {option.id === "wallet" ? (
                        <span className="text-sm">
                          {insufficientFunds
                            ? "Your wallet balance is insufficient for this transaction."
                            : "Pay directly from your wallet balance."}
                        </span>
                      ) : option.id === "mtn" || option.id === "airtel" ? (
                        <span className="text-sm">
                          You'll receive a prompt on your phone to authorize payment.
                        </span>
                      ) : option.id === "visa" ? (
                        <span className="text-sm">
                          Securely pay using your Visa card.
                        </span>
                      ) : option.id === "centripay" ? (
                        <span className="text-sm">
                          This payment method is coming soon to Rwanda.
                        </span>
                      ) : (
                        <span className="text-sm">Select to proceed with this payment option.</span>
                      )}
                    </div>
                  </div>
                </HoverCardContent>
              </HoverCard>
            ))}
          </div>
          
          {/* Processing or completion indicator */}
          {processing && !paymentComplete && (
            <div className="flex items-center justify-center p-3">
              <Loader2 className="h-5 w-5 animate-spin mr-2" />
              <span>Processing your payment...</span>
            </div>
          )}
          
          {paymentComplete && (
            <div className="flex items-center justify-center p-3 text-green-600 bg-green-50 rounded-md">
              <CheckCircle className="h-5 w-5 mr-2" />
              <span>Payment complete!</span>
            </div>
          )}
          
          {/* Action buttons */}
          <div className="flex justify-end space-x-2 pt-2">
            <Button variant="outline" onClick={onClose} disabled={processing}>
              Cancel
            </Button>
            <Button 
              onClick={handleProceedPayment} 
              disabled={!selectedPayment || processing || paymentComplete}
            >
              {processing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Processing...
                </>
              ) : "Proceed to Pay"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentOptionsModal;
