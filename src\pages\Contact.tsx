
import { Mail, Phone } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";

const Contact = () => {
  return (
    <div className="max-w-4xl mx-auto py-16">
      <h1 className="text-4xl font-bold text-center mb-8">Contact Our Sales Team</h1>
      <Card>
        <CardHeader>
          <CardTitle>Get in Touch</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-3">
            <Phone className="h-5 w-5 text-primary" />
            <a href="tel:+256757220113" className="hover:text-primary">
              +256 757 220 113
            </a>
          </div>
          <div className="flex items-center gap-3">
            <Mail className="h-5 w-5 text-primary" />
            <a href="mailto:<EMAIL>" className="hover:text-primary">
              <EMAIL>
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Contact;
