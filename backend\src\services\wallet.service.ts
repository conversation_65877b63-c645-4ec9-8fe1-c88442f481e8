import { PrismaClient } from '@prisma/client';
import { ApiError } from '../middlewares/errorHandler';
import logger from '../utils/logger';

// Define types from Prisma schema
type Transaction = {
  id: string;
  amount: number;
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'TRANSFER' | 'PAYMENT' | 'REFUND';
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  reference?: string;
  description?: string;
  walletId: string;
  paymentMethod?: string;
  paymentDetails?: Record<string, unknown>;
  externalReference?: string;
  createdAt: Date;
  updatedAt: Date;
};

// Define the correct transaction client type
type TransactionClient = Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>;

interface MobileMoneyDetails {
  phoneNumber: string;
  provider: string;
}

interface CreditCardDetails {
  transactionId: string;
}

interface BankTransferDepositDetails {
  accountNumber: string;
  bankName: string;
}

interface BankTransferWithdrawalDetails {
  accountNumber: string;
  bankName: string;
  accountName: string;
}

type PaymentDetails = MobileMoneyDetails | CreditCardDetails | BankTransferDepositDetails | BankTransferWithdrawalDetails;

export class WalletService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Get wallet by user ID
   */
  async getWalletByUserId(userId: string) {
    try {
      const wallet = await this.prisma.wallet.findUnique({
        where: { userId },
        include: {
          transactions: {
            orderBy: { createdAt: 'desc' },
            take: 10,
          },
        },
      });

      if (!wallet) {
        throw new ApiError(404, 'Wallet not found');
      }

      return wallet;
    } catch (error) {
      logger.error('Error fetching wallet:', error);
      throw error;
    }
  }

  /**
   * Get all transactions for a wallet
   */
  async getTransactions(walletId: string, page = 1, limit = 20) {
    try {
      const skip = (page - 1) * limit;

      const [transactions, totalCount] = await Promise.all([
        this.prisma.transaction.findMany({
          where: { walletId },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        this.prisma.transaction.count({
          where: { walletId },
        }),
      ]);

      return {
        data: transactions,
        meta: {
          total: totalCount,
          page,
          limit,
          pages: Math.ceil(totalCount / limit),
        },
      };
    } catch (error) {
      logger.error('Error fetching transactions:', error);
      throw error;
    }
  }

  /**
   * Deposit funds to wallet
   * FIXED: Corrected transaction client type
   */
  async depositFunds(
    userId: string,
    amount: number,
    paymentMethod: string,
    paymentDetails: PaymentDetails,
    reference?: string
  ) {
    if (amount <= 0) {
      throw new ApiError(400, 'Amount must be greater than zero');
    }

    return await this.prisma.$transaction(async (tx: TransactionClient) => {
      // Find the wallet
      const wallet = await tx.wallet.findUnique({
        where: { userId },
      });

      if (!wallet) {
        throw new ApiError(404, 'Wallet not found');
      }

      // Create transaction record
      const transaction = await tx.transaction.create({
        data: {
          amount,
          type: 'DEPOSIT',
          status: 'COMPLETED', // Assuming instant completion
          reference,
          description: `Deposit via ${paymentMethod}`,
          paymentMethod,
          // Properly handle paymentDetails as JSON
          paymentDetails: JSON.parse(JSON.stringify(paymentDetails)),
          wallet: {
            connect: { id: wallet.id },
          },
        },
      });

      // Update wallet balance
      await tx.wallet.update({
        where: { id: wallet.id },
        data: {
          balance: {
            increment: amount,
          },
        },
      });

      return transaction;
    });
  }

  /**
   * Withdraw funds from wallet
   * FIXED: Corrected transaction client type
   */
  async withdrawFunds(
    userId: string,
    amount: number,
    paymentMethod: string,
    paymentDetails: PaymentDetails,
    reference?: string
  ) {
    if (amount <= 0) {
      throw new ApiError(400, 'Amount must be greater than zero');
    }

    return await this.prisma.$transaction(async (tx: TransactionClient) => {
      // Find the wallet
      const wallet = await tx.wallet.findUnique({
        where: { userId },
      });

      if (!wallet) {
        throw new ApiError(404, 'Wallet not found');
      }

      // Check if sufficient funds - Basic check, more detailed check in validateWithdrawal
      if (wallet.balance < amount) {
         // This case should ideally be caught by validateWithdrawal before initiating
         logger.warn(`Withdrawal initiated with insufficient funds for user ${userId}`);
         throw new ApiError(400, 'Insufficient funds');
      }

      // Create transaction record
      const transaction = await tx.transaction.create({
        data: {
          amount,
          type: 'WITHDRAWAL',
          status: 'PROCESSING', // Withdrawals often require processing
          reference,
          description: `Withdrawal via ${paymentMethod}`,
          paymentMethod,
          // Properly handle paymentDetails as JSON
          paymentDetails: JSON.parse(JSON.stringify(paymentDetails)),
          wallet: {
            connect: { id: wallet.id },
          },
        },
      });

      // Update wallet balance
      await tx.wallet.update({
        where: { id: wallet.id },
        data: {
          balance: {
            decrement: amount,
          },
        },
      });

      return transaction;
    });
  }

  /**
   * Process a payment (deduct from wallet)
   * FIXED: Corrected transaction client type
   */
  async processPayment(
    userId: string,
    amount: number,
    description: string,
    reference?: string
  ) {
    if (amount <= 0) {
      throw new ApiError(400, 'Amount must be greater than zero');
    }

    return await this.prisma.$transaction(async (tx: TransactionClient) => {
      // Find the wallet
      const wallet = await tx.wallet.findUnique({
        where: { userId },
      });

      if (!wallet) {
        throw new ApiError(404, 'Wallet not found');
      }

      // Check if sufficient funds
      if (wallet.balance < amount) {
        throw new ApiError(400, 'Insufficient funds');
      }

      // Create transaction record
      const transaction = await tx.transaction.create({
        data: {
          amount,
          type: 'PAYMENT',
          status: 'COMPLETED',
          reference,
          description,
          wallet: {
            connect: { id: wallet.id },
          },
        },
      });

      // Update wallet balance
      await tx.wallet.update({
        where: { id: wallet.id },
        data: {
          balance: {
            decrement: amount,
          },
        },
      });

      return transaction;
    });
  }

  /**
   * Update transaction status
   */
  async updateTransactionStatus(transactionId: string, status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED') {
    try {
      const transaction = await this.prisma.transaction.findUnique({
        where: { id: transactionId },
        include: { wallet: true },
      });

      if (!transaction) {
        throw new ApiError(404, 'Transaction not found');
      }

      // If transaction is being cancelled or failed, and it was previously deducted from the wallet,
      // we need to refund the amount
      if (
        (status === 'CANCELLED' || status === 'FAILED') &&
        (transaction.status === 'PROCESSING' || transaction.status === 'COMPLETED') &&
        (transaction.type === 'WITHDRAWAL' || transaction.type === 'PAYMENT')
      ) {
        await this.prisma.wallet.update({
          where: { id: transaction.walletId },
          data: {
            balance: {
              increment: transaction.amount,
            },
          },
        });
      }

      return await this.prisma.transaction.update({
        where: { id: transactionId },
        data: { status },
      });
    } catch (error) {
      logger.error('Error updating transaction status:', error);
      throw error;
    }
  }

  /**
   * Validate a withdrawal request
   */
  async validateWithdrawal(
    userId: string,
    amount: number,
    paymentMethod: string,
    recipientDetails: Omit<PaymentDetails, 'transactionId'>
  ): Promise<{ valid: boolean; message?: string }> {
    logger.info(`Validating withdrawal for user ${userId}, amount ${amount}, method ${paymentMethod}`);

    if (amount <= 0) {
      return { valid: false, message: 'Amount must be greater than zero' };
    }

    const wallet = await this.prisma.wallet.findUnique({
      where: { userId },
    });

    if (!wallet) {
      return { valid: false, message: 'Wallet not found' };
    }

    if (wallet.balance < amount) {
      return { valid: false, message: 'Insufficient funds' };
    }

    // TODO: Add more specific validation based on paymentMethod and recipientDetails
    // This might involve checking required fields for bank transfers vs mobile money
    // and potentially calling external services for validation.

    // If all checks pass
    return { valid: true };
  }

  /**
   * Get withdrawal status
   */
  async getWithdrawalStatus(withdrawalId: string): Promise<Transaction | null> {
     logger.info(`Fetching withdrawal status for transaction ID ${withdrawalId}`);
     // Use Prisma to fetch the transaction by ID and type
     const transaction = await this.prisma.transaction.findUnique({
         where: { id: withdrawalId, type: 'WITHDRAWAL' },
     });
     // Transform the Prisma transaction to match the service's Transaction type
     return transaction ? {
       ...transaction,
       reference: transaction.reference ?? undefined,
       description: transaction.description ?? undefined,
       paymentMethod: transaction.paymentMethod ?? undefined,
       paymentDetails: transaction.paymentDetails as Record<string, unknown> | undefined,
       externalReference: transaction.externalReference ?? undefined
     } : null;
  }

  /**
   * Get deposit status
   */
   async getDepositStatus(depositId: string): Promise<Transaction | null> {
     logger.info(`Fetching deposit status for transaction ID ${depositId}`);
     // Use Prisma to fetch the transaction by ID and type
     const transaction = await this.prisma.transaction.findUnique({
         where: { id: depositId, type: 'DEPOSIT' },
     });
     // Transform the Prisma transaction to match the service's Transaction type
     return transaction ? {
       ...transaction,
       reference: transaction.reference ?? undefined,
       description: transaction.description ?? undefined,
       paymentMethod: transaction.paymentMethod ?? undefined,
       paymentDetails: transaction.paymentDetails as Record<string, unknown> | undefined,
       externalReference: transaction.externalReference ?? undefined
     } : null;
   }

   /**
    * Get withdrawal history
    */
   async getWithdrawalHistory(
     userId: string,
     page: number,
     limit: number
   ): Promise<{ data: Transaction[], meta: { total: number, page: number, limit: number, pages: number } }> {
     logger.info(`Fetching withdrawal history for user ${userId} with page ${page} and limit ${limit}`);
     
     const wallet = await this.prisma.wallet.findUnique({
       where: { userId },
     });

     if (!wallet) {
       // Return empty history if wallet not found
       return { data: [], meta: { total: 0, page, limit, pages: 0 } };
     }

     const skip = (page - 1) * limit;

     const [transactions, totalCount] = await Promise.all([
       this.prisma.transaction.findMany({
         where: { walletId: wallet.id, type: 'WITHDRAWAL' },
         orderBy: { createdAt: 'desc' },
         skip,
         take: limit,
       }),
       this.prisma.transaction.count({
         where: { walletId: wallet.id, type: 'WITHDRAWAL' },
       }),
     ]);
     
     // TODO: Potentially transform transaction objects to match frontend's WithdrawalResponse structure

     return {
       data: transactions.map(transaction => ({
         ...transaction,
         reference: transaction.reference ?? undefined,
         description: transaction.description ?? undefined,
         paymentMethod: transaction.paymentMethod ?? undefined,
         paymentDetails: transaction.paymentDetails as Record<string, unknown> | undefined,
         externalReference: transaction.externalReference ?? undefined
       })),
       meta: {
         total: totalCount,
         page,
         limit,
         pages: Math.ceil(totalCount / limit),
       },
     };
   }

   /**
    * Get deposit history
    */
   async getDepositHistory(
     userId: string,
     page: number,
     limit: number
   ): Promise<{ data: Transaction[], meta: { total: number, page: number, limit: number, pages: number } }> {
     logger.info(`Fetching deposit history for user ${userId} with page ${page} and limit ${limit}`);
      
      const wallet = await this.prisma.wallet.findUnique({
        where: { userId },
      });

      if (!wallet) {
        // Return empty history if wallet not found
        return { data: [], meta: { total: 0, page, limit, pages: 0 } };
      }

      const skip = (page - 1) * limit;

      const [transactions, totalCount] = await Promise.all([
        this.prisma.transaction.findMany({
          where: { walletId: wallet.id, type: 'DEPOSIT' },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        this.prisma.transaction.count({
          where: { walletId: wallet.id, type: 'DEPOSIT' },
        }),
      ]);
      
      // TODO: Potentially transform transaction objects to match frontend's DepositResponse structure

      return {
        data: transactions.map(transaction => ({
          ...transaction,
          reference: transaction.reference ?? undefined,
          description: transaction.description ?? undefined,
          paymentMethod: transaction.paymentMethod ?? undefined,
          paymentDetails: transaction.paymentDetails as Record<string, unknown> | undefined,
          externalReference: transaction.externalReference ?? undefined
        })),
        meta: {
          total: totalCount,
          page,
          limit,
          pages: Math.ceil(totalCount / limit),
        },
      };
   }

   /**
    * Cleanup method to disconnect from database
    */
   async disconnect() {
     await this.prisma.$disconnect();
   }
}
