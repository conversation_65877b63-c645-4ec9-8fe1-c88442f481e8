import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { Warehouse, PiggyBank, ShieldCheck, LandPlot, BarChart3 } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface ServiceStatusProps {
  label: string;
  value: string | number;
  status?: "positive" | "negative" | "neutral" | "warning";
  icon: React.ReactNode;
  details?: string;
}

const ServiceStatus: React.FC<ServiceStatusProps> = ({ label, value, status = "neutral", icon, details }) => {
  const content = (
    <div className="flex items-center p-3 rounded-lg bg-card border hover:shadow-lg transition-shadow duration-200 cursor-pointer">
      <div className={`flex-shrink-0 rounded-full p-2 mr-3
        ${status === "positive" ? "bg-green-100 text-green-700" : 
          status === "negative" ? "bg-red-100 text-red-700" : 
          status === "warning" ? "bg-yellow-100 text-yellow-700" : 
          "bg-primary/10 text-primary"}`}>
        {icon}
      </div>
      <div className="min-w-0">
        <p className="text-sm text-muted-foreground truncate">{label}</p>
        <p className="text-lg font-semibold truncate">{value}</p>
      </div>
    </div>
  );

  if (details) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>{content}</TooltipTrigger>
          <TooltipContent>
            <p>{details}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return content;
};

const ServicesSummaryCard = () => {
  const navigate = useNavigate();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Services Overview</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          <ServiceStatus
            label="Warehoused Inventory"
            value="850 kg"
            status="positive"
            icon={<Warehouse size={18} />}
            details="Total volume of goods currently stored in our warehouse facilities."
          />
          <ServiceStatus
            label="Active Loans"
            value="2"
            icon={<PiggyBank size={18} />}
            details="Number of ongoing loans provided to clients or taken by the business."
          />
          <ServiceStatus
            label="Insurance Policies"
            value="1"
            icon={<ShieldCheck size={18} />}
            details="Active insurance policies covering various assets or operations."
          />
          <ServiceStatus
            label="Leased Land"
            value="10 acres"
            icon={<LandPlot size={18} />}
            details="Total area of land currently under lease agreements for agricultural use."
          />
          <ServiceStatus
            label="Futures Positions"
            value="5 contracts"
            status="warning"
            icon={<BarChart3 size={18} />}
            details="Number of open futures contracts for commodity trading."
          />
        </div>
        
        <div className="mt-6 p-4 rounded-lg bg-muted">
          <h3 className="font-medium mb-2">Upcoming Service Actions</h3>
          <ul className="space-y-2 text-sm">
            <li className="flex justify-between items-center">
              <div className="flex items-center">
                <PiggyBank className="h-4 w-4 mr-2 text-primary" />
                <span>Loan payment due</span>
              </div>
              <span className="text-muted-foreground">3 days</span>
            </li>
            <li className="flex justify-between items-center">
              <div className="flex items-center">
                <ShieldCheck className="h-4 w-4 mr-2 text-primary" />
                <span>Insurance policy renewal</span>
              </div>
              <span className="text-muted-foreground">14 days</span>
            </li>
            <li className="flex justify-between items-center">
              <div className="flex items-center">
                <LandPlot className="h-4 w-4 mr-2 text-primary" />
                <span>Land lease payment</span>
              </div>
              <span className="text-muted-foreground">21 days</span>
            </li>
          </ul>
        </div>
      </CardContent>
      <CardFooter>
        <Button variant="outline" onClick={() => navigate("/services")} className="w-full">
          View All Services
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ServicesSummaryCard;
