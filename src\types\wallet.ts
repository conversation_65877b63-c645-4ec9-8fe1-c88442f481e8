// Moved from Wallet.tsx
export type PaymentMethodKey = "mtn" | "airtel" | "safaricom" | "bank" | "card" | "xentripay";

// Moved and corrected from Wallet.tsx
export interface PaymentMethod {
  key: PaymentMethodKey;
  name: string;
  type: "mobile_money" | "bank" | "card" | "digital_wallet"; // Added "card" and "digital_wallet"
  icon: React.ReactNode; // Assuming React element icons
  description?: string;
  comingSoon?: boolean;
  region?: string;
  placeholder?: string;
  note?: string;
}


export interface Transaction {
    id: string;
    type: 'deposit' | 'withdraw' | 'buy' | 'sell';
    asset: string; // Could be asset name or "UGX"
    amount: number; // Number of units for asset, or UGX amount for deposit/withdraw
    value: number; // Total UGX value of the transaction
    status: 'pending' | 'processing' | 'completed' | 'failed'; // Added 'processing' as used in context/wallet
    date: string;
    method?: string; // Method used for deposit/withdraw
    price?: number; // Price per unit for buy/sell
}

// UserAsset interface
export interface UserAsset {
    id: string; // Commodity ID (e.g., "coffee-arabica")
    name: string; // Commodity Name
    symbol: string; // Short symbol (e.g., "COF")
    balance: number; // Number of units held
    price: number; // Current price per unit
    value: number; // Calculated value (balance * price)
    change: number; // Price change percentage
    iconName: string; // For display/icon mapping
    color: string; // Color associated with the asset
}

