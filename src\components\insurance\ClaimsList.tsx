
import React, { useState } from 'react';
import { Card, CardHeader, CardContent, CardDescription, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AlertCircle, FileText, Upload, Plus, X, FileSearch } from "lucide-react";
import { insuranceProducts } from './QuoteCalculator';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import DocumentUploader from "../financing/DocumentUploader";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// Mock active policies
const activePolicies = [
  { 
    id: "pol-101", 
    productId: "ins-01", 
    coverageType: "Standard", 
    startDate: "2023-11-01", 
    endDate: "2024-10-31", 
    premium: 400000,
    paidAmount: 400000,
    status: "Active", 
    details: {
      cropType: "Coffee (Arabica)",
      acreage: 5,
      location: "Eastern Region"
    }
  },
  { 
    id: "pol-102", 
    productId: "ins-02", 
    coverageType: "Basic", 
    startDate: "2023-09-15", 
    endDate: "2024-09-14", 
    premium: 120000,
    paidAmount: 60000,
    status: "Active", 
    details: {
      livestockType: "Cattle",
      numberOfAnimals: 6,
      location: "Northern Region"
    }
  },
];

// Define the interface for uploaded documents
interface UploadedDocuments {
  [key: string]: File | null;
}

// Define interface for claims with all required properties
interface Claim {
  id: string;
  policyId: string;
  dateSubmitted: string;
  incidentDate?: string;
  damageType: string;
  estimatedLoss: number;
  status: string;
  documents: number;
  approvalAmount?: number;
  description?: string;
  uploads?: UploadedDocuments;
}

// Mock claims
const initialClaims: Claim[] = [
  { 
    id: "clm-01", 
    policyId: "pol-101", 
    dateSubmitted: "2024-03-10", 
    damageType: "Drought", 
    estimatedLoss: 2000000, 
    status: "In Review", 
    documents: 3,
    incidentDate: "2024-03-05",
    description: "Severe drought has affected my coffee plantation, causing significant crop loss.",
    uploads: {
      "Damage Evidence": null,
      "Ownership Proof": null,
      "Policy Document": null
    }
  },
  { 
    id: "clm-02", 
    policyId: "pol-102", 
    dateSubmitted: "2024-02-15", 
    damageType: "Disease", 
    estimatedLoss: 500000, 
    status: "Approved", 
    documents: 5, 
    approvalAmount: 450000,
    incidentDate: "2024-02-10",
    description: "Livestock disease affected several animals in my herd.",
    uploads: {
      "Damage Evidence": null,
      "Veterinary Report": null,
      "Policy Document": null
    }
  },
];

// Damage types by insurance type
const damageTypes = {
  "ins-01": ["Drought", "Flood", "Pests", "Fire", "Hail", "Wind"],
  "ins-02": ["Disease", "Accident", "Theft", "Predator Attack", "Natural Disaster"],
  "ins-03": ["Accident", "Theft", "Damage", "Breakdown", "Natural Disaster"]
};

const ClaimsList = () => {
  const { toast } = useToast();
  const [claims, setClaims] = useState<Claim[]>(initialClaims);
  const [showNewClaimDialog, setShowNewClaimDialog] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<string>("");
  const [damageType, setDamageType] = useState<string>("");
  const [estimatedLoss, setEstimatedLoss] = useState<string>("");
  const [incidentDate, setIncidentDate] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [uploadedDocuments, setUploadedDocuments] = useState<UploadedDocuments>({
    "Damage Evidence": null,
    "Ownership Proof": null,
    "Policy Document": null
  });
  const [showClaimDetails, setShowClaimDetails] = useState<string | null>(null);
  const [showAddDocuments, setShowAddDocuments] = useState<string | null>(null);

  // Get policy product id
  const getPolicyProductId = (policyId: string) => {
    const policy = activePolicies.find(p => p.id === policyId);
    return policy?.productId || "";
  };

  // Handle document upload
  const handleFileUpload = (documentType: string, file: File) => {
    setUploadedDocuments(prev => ({
      ...prev,
      [documentType]: file
    }));
  };

  // Submit new claim
  const handleSubmitClaim = () => {
    if (!selectedPolicy || !damageType || !estimatedLoss || !incidentDate || !description) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Please fill in all required fields to submit a claim."
      });
      return;
    }

    const hasAtLeastOneDocument = Object.values(uploadedDocuments).some(doc => doc !== null);
    if (!hasAtLeastOneDocument) {
      toast({
        variant: "destructive",
        title: "Documents required",
        description: "Please upload at least one supporting document for your claim."
      });
      return;
    }

    // Create new claim
    const newClaim: Claim = {
      id: `clm-${Math.floor(100 + Math.random() * 900)}`,
      policyId: selectedPolicy,
      dateSubmitted: new Date().toISOString().split('T')[0],
      incidentDate,
      damageType,
      estimatedLoss: parseFloat(estimatedLoss),
      status: "In Review",
      documents: Object.values(uploadedDocuments).filter(doc => doc !== null).length,
      description,
      uploads: uploadedDocuments
    };

    // Add new claim
    setClaims(prev => [newClaim, ...prev]);
    
    // Reset form and close dialog
    setSelectedPolicy("");
    setDamageType("");
    setEstimatedLoss("");
    setIncidentDate("");
    setDescription("");
    setUploadedDocuments({
      "Damage Evidence": null,
      "Ownership Proof": null,
      "Policy Document": null
    });
    setShowNewClaimDialog(false);
    
    toast({
      title: "Claim submitted",
      description: `Your claim #${newClaim.id} has been successfully submitted and is now under review.`
    });
  };

  // Add documents to existing claim
  const handleAddDocumentsToExistingClaim = () => {
    const hasAnyDocument = Object.values(uploadedDocuments).some(doc => doc !== null);
    if (!hasAnyDocument) {
      toast({
        variant: "destructive",
        title: "No documents selected",
        description: "Please upload at least one document to add."
      });
      return;
    }

    // Update claim with new documents
    setClaims(prev => prev.map(claim => {
      if (claim.id === showAddDocuments) {
        const currentDocCount = claim.documents || 0;
        const newDocCount = Object.values(uploadedDocuments).filter(doc => doc !== null).length;
        
        return {
          ...claim,
          documents: currentDocCount + newDocCount,
          uploads: { ...(claim.uploads || {}), ...uploadedDocuments }
        };
      }
      return claim;
    }));

    // Reset and close dialog
    setUploadedDocuments({
      "Damage Evidence": null,
      "Ownership Proof": null,
      "Policy Document": null
    });
    setShowAddDocuments(null);
    
    toast({
      title: "Documents added",
      description: "Your documents have been successfully added to the claim."
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Claims Management</CardTitle>
        <CardDescription>File and track insurance claims</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium">Your Claims</h3>
          <Button onClick={() => setShowNewClaimDialog(true)}>
            <Plus className="h-4 w-4 mr-2" /> File New Claim
          </Button>
        </div>

        {claims.length > 0 ? (
          <div className="space-y-4">
            {claims.map(claim => {
              const policy = activePolicies.find(p => p.id === claim.policyId);
              const productId = policy?.productId || '';
              const product = insuranceProducts.find(p => p.id === productId) || insuranceProducts[0];
              
              return (
                <div key={claim.id} className="bg-muted p-4 rounded-lg">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">Claim #{claim.id}</h4>
                      <p className="text-sm text-muted-foreground">{product.name} | Submitted on {claim.dateSubmitted}</p>
                    </div>
                    <div className={`px-3 py-1 rounded-full text-xs ${
                      claim.status === "Approved" ? "bg-green-100 text-green-800" : 
                      claim.status === "Rejected" ? "bg-red-100 text-red-800" : 
                      "bg-blue-100 text-blue-800"
                    }`}>
                      {claim.status}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mt-4 text-sm">
                    <div>
                      <span className="text-muted-foreground block">Damage Type:</span>
                      <span>{claim.damageType}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground block">Estimated Loss:</span>
                      <span>{claim.estimatedLoss.toLocaleString()} UGX</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground block">Documents:</span>
                      <span>{claim.documents} files uploaded</span>
                    </div>
                    
                    {claim.status === "Approved" && claim.approvalAmount && (
                      <div>
                        <span className="text-muted-foreground block">Approved Amount:</span>
                        <span className="font-medium text-green-600">{claim.approvalAmount.toLocaleString()} UGX</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex justify-end mt-4 gap-2">
                    <Button variant="outline" size="sm" onClick={() => setShowClaimDetails(claim.id)}>
                      <FileSearch className="h-4 w-4 mr-1" /> View Details
                    </Button>
                    {claim.status === "In Review" && (
                      <Button size="sm" onClick={() => setShowAddDocuments(claim.id)}>
                        <Upload className="h-4 w-4 mr-1" /> Add Documents
                      </Button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-8 border rounded-lg">
            <AlertCircle className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
            <h3 className="text-lg font-medium">No Claims Filed</h3>
            <p className="text-muted-foreground mb-4 max-w-md mx-auto">
              You haven't filed any insurance claims yet. If you've experienced a covered loss, file a claim to begin the process.
            </p>
            <Button onClick={() => setShowNewClaimDialog(true)}>
              <Plus className="h-4 w-4 mr-2" /> File First Claim
            </Button>
          </div>
        )}
      </CardContent>

      {/* New Claim Dialog */}
      <Dialog open={showNewClaimDialog} onOpenChange={setShowNewClaimDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>File a New Insurance Claim</DialogTitle>
            <DialogDescription>
              Please provide details about your loss and supporting documentation.
            </DialogDescription>
          </DialogHeader>
          
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="details">Claim Details</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
            </TabsList>
            
            <TabsContent value="details" className="space-y-4">
              <div className="grid gap-4">
                <div className="grid grid-cols-1 gap-2">
                  <Label htmlFor="policy">Select Policy</Label>
                  <Select 
                    value={selectedPolicy} 
                    onValueChange={setSelectedPolicy}
                  >
                    <SelectTrigger id="policy">
                      <SelectValue placeholder="Select a policy" />
                    </SelectTrigger>
                    <SelectContent>
                      {activePolicies.map(policy => {
                        const product = insuranceProducts.find(p => p.id === policy.productId);
                        return (
                          <SelectItem key={policy.id} value={policy.id}>
                            {product?.name} - {policy.id}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="damageType">Type of Damage/Loss</Label>
                    <Select 
                      value={damageType} 
                      onValueChange={setDamageType}
                      disabled={!selectedPolicy}
                    >
                      <SelectTrigger id="damageType">
                        <SelectValue placeholder="Select damage type" />
                      </SelectTrigger>
                      <SelectContent>
                        {selectedPolicy && 
                          damageTypes[getPolicyProductId(selectedPolicy) as keyof typeof damageTypes]?.map(type => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))
                        }
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="incidentDate">Date of Incident</Label>
                    <Input 
                      type="date" 
                      id="incidentDate" 
                      value={incidentDate}
                      onChange={(e) => setIncidentDate(e.target.value)}
                      max={new Date().toISOString().split('T')[0]}
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="estimatedLoss">Estimated Loss Amount (UGX)</Label>
                  <Input 
                    type="number" 
                    id="estimatedLoss"
                    min="0"
                    value={estimatedLoss}
                    onChange={(e) => setEstimatedLoss(e.target.value)}
                    placeholder="Enter the estimated value of your loss"
                  />
                </div>
                
                <div>
                  <Label htmlFor="description">Description of Incident</Label>
                  <Textarea 
                    id="description" 
                    rows={4} 
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Please describe what happened in detail..."
                  />
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="documents" className="space-y-6">
              <p className="text-sm text-muted-foreground mb-4">
                Please upload supporting documents for your claim. At least one document is required.
              </p>
              
              <div className="space-y-6">
                <DocumentUploader 
                  documentType="Damage Evidence"
                  onFileUpload={handleFileUpload}
                  uploadedFile={uploadedDocuments["Damage Evidence"]}
                />
                
                <DocumentUploader 
                  documentType="Ownership Proof"
                  onFileUpload={handleFileUpload}
                  uploadedFile={uploadedDocuments["Ownership Proof"]}
                />
                
                <DocumentUploader 
                  documentType="Policy Document"
                  onFileUpload={handleFileUpload}
                  uploadedFile={uploadedDocuments["Policy Document"]}
                />
              </div>
            </TabsContent>
          </Tabs>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowNewClaimDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleSubmitClaim}>Submit Claim</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Documents Dialog */}
      <Dialog open={showAddDocuments !== null} onOpenChange={() => showAddDocuments && setShowAddDocuments(null)}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Add Documents to Claim</DialogTitle>
            <DialogDescription>
              Upload additional supporting documents for your claim.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            <DocumentUploader 
              documentType="Additional Evidence"
              onFileUpload={handleFileUpload}
              uploadedFile={uploadedDocuments["Damage Evidence"]}
            />
            
            <DocumentUploader 
              documentType="Supporting Documents"
              onFileUpload={handleFileUpload}
              uploadedFile={uploadedDocuments["Ownership Proof"]}
            />
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDocuments(null)}>
              Cancel
            </Button>
            <Button onClick={handleAddDocumentsToExistingClaim}>Add Documents</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Claim Details Dialog */}
      <Dialog open={showClaimDetails !== null} onOpenChange={() => showClaimDetails && setShowClaimDetails(null)}>
        <DialogContent className="max-w-3xl">
          {claims.find(c => c.id === showClaimDetails) && (
            <>
              <DialogHeader>
                <DialogTitle>Claim Details: #{showClaimDetails}</DialogTitle>
                <DialogDescription>
                  Complete information about this claim
                </DialogDescription>
              </DialogHeader>
              
              <div className="mt-4">
                {(() => {
                  const claim = claims.find(c => c.id === showClaimDetails);
                  const policy = activePolicies.find(p => p.id === claim?.policyId);
                  const productId = policy?.productId || '';
                  const product = insuranceProducts.find(p => p.id === productId) || insuranceProducts[0];
                  
                  return (
                    <div className="space-y-6">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-medium text-lg">Claim #{claim?.id}</h3>
                          <p className="text-muted-foreground">Submitted on {claim?.dateSubmitted}</p>
                        </div>
                        <div className={`px-3 py-1 rounded-full text-xs ${
                          claim?.status === "Approved" ? "bg-green-100 text-green-800" : 
                          claim?.status === "Rejected" ? "bg-red-100 text-red-800" : 
                          "bg-blue-100 text-blue-800"
                        }`}>
                          {claim?.status}
                        </div>
                      </div>
                      
                      <div className="border rounded-md p-4 space-y-4">
                        <h4 className="font-medium">Policy Information</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground block">Product:</span>
                            <span>{product.name}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground block">Policy ID:</span>
                            <span>{policy?.id}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground block">Coverage Type:</span>
                            <span>{policy?.coverageType}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground block">Status:</span>
                            <span>{policy?.status}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground block">Start Date:</span>
                            <span>{policy?.startDate}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground block">End Date:</span>
                            <span>{policy?.endDate}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="border rounded-md p-4 space-y-4">
                        <h4 className="font-medium">Claim Details</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground block">Damage Type:</span>
                            <span>{claim?.damageType}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground block">Incident Date:</span>
                            <span>{claim?.incidentDate}</span>
                          </div>
                          <div>
                            <span className="text-muted-foreground block">Estimated Loss:</span>
                            <span>{claim?.estimatedLoss?.toLocaleString()} UGX</span>
                          </div>
                          {claim?.status === "Approved" && claim.approvalAmount && (
                            <div>
                              <span className="text-muted-foreground block">Approved Amount:</span>
                              <span className="font-medium text-green-600">{claim.approvalAmount.toLocaleString()} UGX</span>
                            </div>
                          )}
                        </div>
                        
                        {claim?.description && (
                          <div className="mt-2">
                            <span className="text-muted-foreground block">Description:</span>
                            <p className="text-sm mt-1 p-2 bg-muted rounded">{claim.description}</p>
                          </div>
                        )}
                      </div>

                      <div className="border rounded-md p-4 space-y-4">
                        <h4 className="font-medium">Documents</h4>
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Document Type</TableHead>
                              <TableHead>Filename</TableHead>
                              <TableHead>Size</TableHead>
                              <TableHead>Upload Date</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {claim?.uploads ? (
                              Object.entries(claim.uploads)
                                .filter(([_, file]) => file !== null)
                                .map(([type, file], index) => (
                                  <TableRow key={index}>
                                    <TableCell>{type}</TableCell>
                                    <TableCell>{(file as File)?.name || "Unknown"}</TableCell>
                                    <TableCell>
                                      {(file as File)?.size
                                        ? `${Math.round(((file as File).size || 0) / 1024)} KB`
                                        : "Unknown"}
                                    </TableCell>
                                    <TableCell>{claim.dateSubmitted}</TableCell>
                                  </TableRow>
                                ))
                            ) : (
                              <TableRow>
                                <TableCell colSpan={4} className="text-center py-4">
                                  No documents uploaded
                                </TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </div>
                      
                      {claim?.status === "In Review" && (
                        <div className="border rounded-md border-blue-200 bg-blue-50 p-4">
                          <h4 className="font-medium text-blue-800">Claim Processing</h4>
                          <p className="text-sm text-blue-700 mt-1">
                            Your claim is currently under review by our claims department. You will be notified when there is an update.
                            Average processing time is 5-7 business days.
                          </p>
                        </div>
                      )}

                      {claim?.status === "Approved" && (
                        <div className="border rounded-md border-green-200 bg-green-50 p-4">
                          <h4 className="font-medium text-green-800">Claim Approved</h4>
                          <p className="text-sm text-green-700 mt-1">
                            Your claim has been approved and is being processed for payment. 
                            Payment will be issued according to your chosen payment method within 3-5 business days.
                          </p>
                        </div>
                      )}
                    </div>
                  );
                })()}
              </div>
              
              <DialogFooter>
                <Button onClick={() => setShowClaimDetails(null)}>Close</Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default ClaimsList;
