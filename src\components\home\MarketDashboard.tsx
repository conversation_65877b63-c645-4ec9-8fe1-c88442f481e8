import { useState, useEffect } from "react"; // Added useState and useEffect
import { DoughnutChart } from "@/components/ui/doughnut-chart";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"; // Added CardDescription
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ArrowDownRight, ArrowUpRight, BarChart3, TrendingUp, DollarSign } from "lucide-react"; // Added TrendingUp, DollarSign
// Assuming FuturesContracts might be a simpler display or integrated differently now
// import FuturesContracts from "./FuturesContracts"; 
// LiveMarketUpdates component is removed from Index.tsx, so we'll integrate its purpose here

// Commodity distribution data for doughnut chart
const commodityDistribution = [
  { name: "Coffee (Arabica)", value: 35, color: "#3b82f6" }, // Tailwind blue-500
  { name: "Coffee (Robusta)", value: 25, color: "#6366f1" }, // Tailwind indigo-500
  { name: "Cocoa", value: 20, color: "#8b5cf6" },       // Tailwind violet-500
  { name: "Soybean", value: 12, color: "#10b981" },      // Tailwind emerald-500
  { name: "Sesame", value: 8, color: "#f59e0b" }       // Tailwind amber-500
];

// Mock data for live market prices (USD)
const initialMarketPrices = [
  { 
    commodity: "Coffee (Arabica)", 
    priceUSD: 3.56, 
    change: 0.12, 
    volume: "1.2M",
    unit: "kg"
  },
  { 
    commodity: "Coffee (Robusta)", 
    priceUSD: 2.87, 
    change: -0.09, 
    volume: "950K",
    unit: "kg"
  },
  { 
    commodity: "Cocoa", 
    priceUSD: 12.54, 
    change: 0.34, 
    volume: "780K",
    unit: "MT" // Metric Ton
  },
  { 
    commodity: "Soybean", 
    priceUSD: 5.32, 
    change: -0.17, 
    volume: "2.1M",
    unit: "bushel"
  },
  { 
    commodity: "Sesame", 
    priceUSD: 6.70, 
    change: 0.21, 
    volume: "520K",
    unit: "MT"
  },
];

// Mock Futures Data (simplified)
const futuresData = [
    { commodity: "Coffee Arabica", month: "Dec '24", price: 3.65, change: "+0.02" },
    { commodity: "Cocoa", month: "Mar '25", price: 12.80, change: "+0.15" },
    { commodity: "Soybean", month: "Nov '24", price: 5.40, change: "-0.05" },
];


export const MarketDashboard = () => {
  const [usdToUgxRate, setUsdToUgxRate] = useState<number | null>(null);
  const [marketPrices, setMarketPrices] = useState(initialMarketPrices);

  // Simulate fetching exchange rate and dynamic price updates
  useEffect(() => {
    // Simulate fetching exchange rate
    const fetchExchangeRate = async () => {
      // In a real app, fetch from an API like exchangerate-api.com, openexchangerates.org, etc.
      // For demo purposes, using a static rate and simulating a delay
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call delay
      setUsdToUgxRate(3850); // Example: 1 USD = 3850 UGX
    };

    fetchExchangeRate();

    // Simulate live price updates (optional, for dynamism)
    const intervalId = setInterval(() => {
        setMarketPrices(prevPrices => 
            prevPrices.map(item => {
                const priceChangeFactor = (Math.random() - 0.5) * 0.1; // Small random change
                const newChange = parseFloat((item.change + (Math.random() - 0.5) * 0.05).toFixed(2));
                return {
                    ...item,
                    priceUSD: Math.max(0.1, parseFloat((item.priceUSD + priceChangeFactor).toFixed(2))), // Ensure price doesn't go below 0.1
                    change: newChange,
                };
            })
        );
    }, 5000); // Update prices every 5 seconds

    return () => clearInterval(intervalId); // Cleanup interval on component unmount
  }, []);

  return (
    // Removed the outer <section> and container, as Index.tsx now handles this
    // Increased spacing between dashboard elements
    <div className="space-y-8 md:space-y-10"> 
      <div className="text-center">
        <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-3">Market Dashboard</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto text-sm md:text-base">
          Real-time insights into agricultural commodity markets and trading opportunities.
        </p>
      </div>

      {/* Grid layout for dashboard cards */}
      {/* Adjusted to a 2-column layout for medium screens, 1-column for small */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
        
        {/* Live Market Prices Card (Enhanced) */}
        <Card className="md:col-span-2 shadow-lg hover:shadow-xl transition-shadow duration-300"> {/* Spans full width on medium screens if only two main cards */}
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
                <CardTitle className="text-xl font-semibold flex items-center">
                    <TrendingUp className="h-6 w-6 mr-3 text-primary" />
                    Live Market Prices
                </CardTitle>
                {usdToUgxRate && (
                    <div className="text-xs text-muted-foreground flex items-center">
                        <DollarSign size={14} className="mr-1" /> 1 USD = {usdToUgxRate.toLocaleString()} UGX
                    </div>
                )}
            </div>
            <CardDescription className="text-sm">Current spot prices for key commodities.</CardDescription>
          </CardHeader>
          <CardContent>
            {!usdToUgxRate ? (
                <div className="text-center py-8">
                    <div className="animate-pulse text-muted-foreground">Fetching exchange rates...</div>
                </div>
            ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[30%]">Commodity</TableHead>
                  <TableHead className="text-right">Price (USD)</TableHead>
                  <TableHead className="text-right">Price (UGX)</TableHead>
                  <TableHead className="text-right w-[20%]">Change (USD)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {marketPrices.map((item) => {
                  const priceUGX = item.priceUSD * (usdToUgxRate || 0);
                  return (
                    <TableRow key={item.commodity} className="hover:bg-muted/50 transition-colors">
                      <TableCell>
                        <div className="font-medium">{item.commodity}</div>
                        <div className="text-xs text-muted-foreground">per {item.unit}</div>
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        ${item.priceUSD.toFixed(2)}
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {priceUGX.toLocaleString(undefined, { minimumFractionDigits: 0, maximumFractionDigits: 0 })}
                      </TableCell>
                      <TableCell className="text-right">
                        <div
                          className={`flex items-center justify-end text-sm ${
                            item.change >= 0 ? "text-green-600" : "text-red-600"
                          }`}
                        >
                          {item.change >= 0 ? (
                            <ArrowUpRight className="h-4 w-4 mr-1" />
                          ) : (
                            <ArrowDownRight className="h-4 w-4 mr-1" />
                          )}
                          {Math.abs(item.change).toFixed(2)}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
            )}
          </CardContent>
        </Card>

        {/* Commodity Distribution Doughnut Chart Card */}
        <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold flex items-center">
                <BarChart3 className="h-6 w-6 mr-3 text-primary" />
                Market Distribution
            </CardTitle>
            <CardDescription className="text-sm">Volume share of key traded commodities.</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center items-center h-[300px] md:h-[350px]"> {/* Adjusted height */}
            <DoughnutChart
              title="Commodity Distribution"
              data={commodityDistribution}
              className="max-w-[300px] w-full" // Constrain width for better display
              innerRadius={70} // Slightly larger inner radius
              outerRadius={100} // Slightly larger outer radius
            />
          </CardContent>
        </Card>
        
        {/* Simplified Futures Contracts Display Card */}
        <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
            <CardHeader className="pb-4">
                <CardTitle className="text-xl font-semibold flex items-center">
                    <TrendingUp className="h-6 w-6 mr-3 text-primary" />
                    Futures Outlook
                </CardTitle>
                <CardDescription className="text-sm">Indicative prices for upcoming futures contracts.</CardDescription>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Commodity</TableHead>
                            <TableHead>Month</TableHead>
                            <TableHead className="text-right">Price (USD)</TableHead>
                            <TableHead className="text-right">Change</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {futuresData.map(future => (
                            <TableRow key={`${future.commodity}-${future.month}`} className="hover:bg-muted/50 transition-colors">
                                <TableCell className="font-medium">{future.commodity}</TableCell>
                                <TableCell>{future.month}</TableCell>
                                <TableCell className="text-right">${future.price.toFixed(2)}</TableCell>
                                <TableCell className={`text-right ${future.change.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                                    {future.change}
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </CardContent>
        </Card>

      </div>
    </div>
  );
};

export default MarketDashboard;