
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';
import QRCode from 'qrcode';

// Define types for quote data
export interface InsuranceQuote {
  quoteId: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  insuranceType: string;
  coverageDetails: {
    type: string;
    level: string;
    coverageItems: {
      name: string;
      quantity: number;
      description?: string;
    }[];
  };
  premium: number;
  timestamp: string;
  validUntil: string;
}

/**
 * Generates a unique quote reference number
 */
export const generateQuoteId = (): string => {
  const prefix = 'FINS';
  const timestamp = Date.now().toString().substring(7);
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${prefix}-${timestamp}-${random}`;
};

/**
 * Generates a PDF quote document
 */
export const generateQuotePDF = async (quote: InsuranceQuote): Promise<Blob> => {
  // Create new PDF document
  const doc = new jsPDF();
  
  // Add company logo and header
  doc.setFontSize(20);
  doc.setTextColor(100, 100, 100);
  doc.text('Fotis Agro Trading', 105, 20, { align: 'center' });
  
  doc.setFontSize(16);
  doc.setTextColor(150, 150, 150);
  doc.text('Insurance Quote', 105, 30, { align: 'center' });
  
  // Add quote details
  doc.setFontSize(12);
  doc.setTextColor(0, 0, 0);
  
  // Quote reference and date
  doc.text(`Quote Reference: ${quote.quoteId}`, 20, 45);
  doc.text(`Date Issued: ${quote.timestamp}`, 20, 52);
  doc.text(`Valid Until: ${quote.validUntil}`, 20, 59);
  
  // Customer details
  doc.setFontSize(14);
  doc.text('Customer Details', 20, 70);
  doc.setFontSize(12);
  doc.text(`Name: ${quote.customerName}`, 20, 78);
  doc.text(`Email: ${quote.customerEmail}`, 20, 85);
  doc.text(`Phone: ${quote.customerPhone}`, 20, 92);
  
  // Insurance details
  doc.setFontSize(14);
  doc.text('Insurance Details', 20, 105);
  doc.setFontSize(12);
  doc.text(`Type: ${quote.insuranceType}`, 20, 113);
  doc.text(`Coverage Level: ${quote.coverageDetails.level}`, 20, 120);
  
  // Create table for coverage items
  const tableColumn = ['Item', 'Quantity', 'Description'];
  const tableRows = quote.coverageDetails.coverageItems.map(item => [
    item.name,
    item.quantity.toString(),
    item.description || '-'
  ]);
  
  // @ts-ignore - jspdf-autotable typings
  doc.autoTable({
    startY: 125,
    head: [tableColumn],
    body: tableRows,
  });
  
  const finalY = (doc as any).lastAutoTable.finalY || 150;
  
  // Premium information
  doc.setFontSize(14);
  doc.text('Premium', 20, finalY + 15);
  doc.setFontSize(12);
  doc.text(`Annual Premium: ${quote.premium.toLocaleString()} UGX`, 20, finalY + 23);
  doc.text(`Monthly Payment: ${Math.round(quote.premium / 12).toLocaleString()} UGX`, 20, finalY + 30);
  
  // Legal disclaimer
  doc.setFontSize(9);
  doc.setTextColor(100, 100, 100);
  doc.text('This quote is provided based on the information supplied and is not a contract of insurance.', 20, finalY + 45);
  doc.text('The final premium may be subject to change pending verification of the details provided.', 20, finalY + 50);
  
  // Generate QR code for digital access
  try {
    const qrCodeData = await QRCode.toDataURL(JSON.stringify({
      quoteId: quote.quoteId,
      timestamp: quote.timestamp,
    }));
    
    // Add QR code to PDF
    doc.addImage(qrCodeData, 'PNG', 150, finalY + 20, 30, 30);
    doc.setFontSize(9);
    doc.text('Scan for digital access', 165, finalY + 55, { align: 'center' });
  } catch (err) {
    console.error('Error generating QR code:', err);
  }
  
  // Contact information
  doc.setFontSize(10);
  doc.text('Fotis Agro Trading | +256 700 123456 | <EMAIL> | www.fotisagro.com', 105, 280, { align: 'center' });
  
  // Return the PDF as a blob
  return doc.output('blob');
};

/**
 * Save quote to localStorage for retrieval
 */
export const saveQuote = (quote: InsuranceQuote): void => {
  // Get existing quotes or initialize empty array
  const savedQuotes = JSON.parse(localStorage.getItem('insuranceQuotes') || '[]');
  savedQuotes.push(quote);
  
  // Only store the latest 20 quotes
  if (savedQuotes.length > 20) {
    savedQuotes.shift();
  }
  
  localStorage.setItem('insuranceQuotes', JSON.stringify(savedQuotes));
};

/**
 * Retrieve a quote by its reference ID
 */
export const getQuoteById = (quoteId: string): InsuranceQuote | null => {
  const savedQuotes = JSON.parse(localStorage.getItem('insuranceQuotes') || '[]');
  const quote = savedQuotes.find((q: InsuranceQuote) => q.quoteId === quoteId);
  return quote || null;
};

