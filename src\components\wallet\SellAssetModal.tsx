import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alog<PERSON>it<PERSON> } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Loader2 } from "lucide-react";

interface SellAssetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSell: (quantity: number, amount: number) => void;
  asset: {
    id: string;
    name: string;
    symbol: string;
    balance: number;
    price: number;
  };
}

export const SellAssetModal = ({ isOpen, onClose, onSell, asset }: SellAssetModalProps) => {
  const [quantity, setQuantity] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  const totalValue = !isNaN(parseFloat(quantity)) 
    ? asset.price * parseFloat(quantity) 
    : 0;

  const handleSell = () => {
    if (!quantity || parseFloat(quantity) <= 0) {
      toast({
        title: "Invalid quantity",
        description: "Please enter a valid quantity to sell",
        variant: "destructive"
      });
      return;
    }

    if (parseFloat(quantity) > asset.balance) {
      toast({
        title: "Insufficient balance",
        description: `You only have ${asset.balance} ${asset.symbol} available`,
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    
    // Simulate API call and SMS notification
    setTimeout(() => {
      onSell(parseFloat(quantity), totalValue);
      toast({
        title: "Sale successful",
        description: `You've sold ${quantity} ${asset.symbol} for $${totalValue.toFixed(2)}. An SMS confirmation has been sent to your registered number.`,
      });
      setIsProcessing(false);
      setQuantity("");
      onClose();
    }, 1500);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        setQuantity("");
        onClose();
      }
    }}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Sell {asset.name}</DialogTitle>
          <DialogDescription>
            Enter the quantity you want to sell
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label>Available Balance</Label>
            <div className="p-3 bg-secondary/30 rounded-md">
              <div className="flex justify-between text-sm">
                <span>{asset.balance} {asset.symbol}</span>
                <span>≈ {(asset.balance * asset.price).toLocaleString()} UGX</span>
              </div>
            </div>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="quantity">Quantity to Sell</Label>
            <Input
              id="quantity"
              type="number"
              placeholder={`Enter amount of ${asset.symbol}`}
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              min="0"
              max={asset.balance.toString()}
              step="0.0001"
            />
          </div>
          
          {quantity && !isNaN(parseFloat(quantity)) && (
            <div className="grid gap-2">
              <Label>Transaction Summary</Label>
              <div className="p-3 bg-secondary/30 rounded-md">
                <div className="flex justify-between text-sm">
                  <span>Asset:</span>
                  <span className="font-medium">{asset.name}</span>
                </div>
                <div className="flex justify-between text-sm mt-1">
                  <span>Price per unit:</span>
                  <span className="font-medium">{asset.price.toLocaleString()} UGX</span>
                </div>
                <div className="flex justify-between text-sm mt-1">
                  <span>Quantity:</span>
                  <span className="font-medium">{parseFloat(quantity)}</span>
                </div>
                <div className="flex justify-between font-medium mt-2 pt-2 border-t border-secondary">
                  <span>Total value:</span>
                  <span className="text-primary">{totalValue.toLocaleString()} UGX</span>
                </div>
              </div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSell} disabled={isProcessing}>
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              "Confirm Sale"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
