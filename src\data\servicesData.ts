// Define types for different service categories
export type ServiceCategory = "warehousing" | "financing" | "insurance" | "leasing" | "futures" | "transportation" | "equipment" | "agronomy";

// Service interface
export interface Service {
  id: string;
  category: ServiceCategory;
  title: string;
  description: string;
  icon: string;
  available: boolean;
  forFarmers: boolean;
  forTraders: boolean;
  forTransporters: boolean;
}

// List of available services
export const services: Service[] = [
  // Warehousing Services
  {
    id: "warehouse-storage",
    category: "warehousing",
    title: "Warehouse Storage",
    description: "Secure storage facilities for your agricultural products with inventory tracking",
    icon: "warehouse",
    available: true,
    forFarmers: true,
    forTraders: true,
    forTransporters: false
  },
  {
    id: "inventory-management",
    category: "warehousing",
    title: "Inventory Management",
    description: "Track and manage your stored goods with real-time updates",
    icon: "warehouse",
    available: true,
    forFarmers: true,
    forTraders: true,
    forTransporters: false
  },
  
  // Financing Services
  {
    id: "crop-financing",
    category: "financing",
    title: "Crop Financing",
    description: "Short-term loans for seasonal crop production needs",
    icon: "piggy-bank",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: false
  },
  {
    id: "equipment-loans",
    category: "financing",
    title: "Equipment Financing",
    description: "Medium to long-term loans for farm equipment and machinery",
    icon: "piggy-bank",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: true
  },
  {
    id: "trade-financing",
    category: "financing",
    title: "Trade Financing",
    description: "Financial solutions to support agricultural trading operations",
    icon: "handshake",
    available: true,
    forFarmers: false,
    forTraders: true,
    forTransporters: false
  },
  
  // Insurance Services
  {
    id: "crop-insurance",
    category: "insurance",
    title: "Crop Insurance",
    description: "Protection against crop failure due to natural disasters or market conditions",
    icon: "file-text",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: false
  },
  {
    id: "livestock-insurance",
    category: "insurance",
    title: "Livestock Insurance",
    description: "Coverage for livestock against diseases, accidents, and theft",
    icon: "file-text",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: false
  },
  {
    id: "transport-insurance",
    category: "insurance",
    title: "Transport Insurance",
    description: "Insurance for goods in transit and transportation vehicles",
    icon: "truck",
    available: true,
    forFarmers: false,
    forTraders: true,
    forTransporters: true
  },
  
  // Land Leasing Services
  {
    id: "land-listing",
    category: "leasing",
    title: "Land Listing & Search",
    description: "List available land for lease or find suitable land to rent",
    icon: "land-plot",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: false
  },
  {
    id: "lease-management",
    category: "leasing",
    title: "Lease Agreement Management",
    description: "Create and manage lease contracts with secure digital signatures",
    icon: "file-search",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: false
  },
  
  // Futures Acquisition
  {
    id: "futures-trading",
    category: "futures",
    title: "Futures Trading",
    description: "Buy and sell futures contracts for agricultural commodities",
    icon: "chart-line",
    available: true,
    forFarmers: true,
    forTraders: true,
    forTransporters: false
  },
  {
    id: "market-analysis",
    category: "futures",
    title: "Market Analysis",
    description: "Advanced analysis tools for futures markets and price predictions",
    icon: "chart-bar",
    available: true,
    forFarmers: true,
    forTraders: true,
    forTransporters: false
  },
  
  // Transportation Services
  {
    id: "freight-booking",
    category: "transportation",
    title: "Freight Booking",
    description: "Book trucks and transporters for moving your agricultural commodities",
    icon: "truck",
    available: true,
    forFarmers: true,
    forTraders: true,
    forTransporters: false
  },
  {
    id: "route-optimization",
    category: "transportation",
    title: "Route Optimization",
    description: "Optimize transportation routes for cost and time efficiency",
    icon: "route",
    available: true,
    forFarmers: true,
    forTraders: true,
    forTransporters: true
  },
  {
    id: "load-matching",
    category: "transportation",
    title: "Load Matching",
    description: "Match available trucks with commodity loads that need transport",
    icon: "package",
    available: true,
    forFarmers: false,
    forTraders: true,
    forTransporters: true
  },
  {
    id: "real-time-tracking",
    category: "transportation",
    title: "Real-time Tracking",
    description: "Track your shipments in real-time with GPS technology",
    icon: "navigation",
    available: true,
    forFarmers: true,
    forTraders: true,
    forTransporters: true
  },
  
  // Equipment Leasing Services
  {
    id: "tractor-hire",
    category: "equipment",
    title: "Tractor Hiring",
    description: "Rent tractors for your farming operations with flexible terms",
    icon: "tractor",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: false
  },
  {
    id: "heavy-machinery",
    category: "equipment",
    title: "Heavy Machinery",
    description: "Hire diggers, bulldozers and other heavy equipment for land development",
    icon: "truck",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: true
  },
  {
    id: "irrigation-equipment",
    category: "equipment",
    title: "Irrigation Systems",
    description: "Access modern irrigation equipment to optimize water usage",
    icon: "irrigation",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: false
  },
  {
    id: "equipment-maintenance",
    category: "equipment",
    title: "Equipment Maintenance",
    description: "Regular servicing and maintenance for hired or owned farm equipment",
    icon: "wrench",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: true
  },
  
  // Agronomy Services
  {
    id: "crop-planning",
    category: "agronomy",
    title: "Crop Planning",
    description: "Professional advice for optimal crop selection and rotation planning",
    icon: "calendar",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: false
  },
  {
    id: "soil-analysis",
    category: "agronomy",
    title: "Soil Analysis",
    description: "Comprehensive soil testing and fertility recommendations",
    icon: "soil",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: false
  },
  {
    id: "pest-management",
    category: "agronomy",
    title: "Pest Management",
    description: "Integrated pest management services for crop protection",
    icon: "spray-can",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: false
  },
  {
    id: "manual-labor",
    category: "agronomy",
    title: "Manual Labor",
    description: "Connect with skilled agricultural workers for seasonal operations",
    icon: "shovel",
    available: true,
    forFarmers: true,
    forTraders: false,
    forTransporters: false
  }
];

// Get services by category
export const getServicesByCategory = (category: ServiceCategory): Service[] => {
  return services.filter(service => service.category === category);
};

// Get services by user type
export const getServicesByUserType = (
  userType: "farmer" | "trader" | "transporter"
): Service[] => {
  return services.filter(service => {
    if (userType === "farmer") return service.forFarmers;
    if (userType === "trader") return service.forTraders;
    if (userType === "transporter") return service.forTransporters;
    return false;
  });
};
