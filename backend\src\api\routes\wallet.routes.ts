import express from 'express';
import WalletController from '../../controllers/wallet.controller';
import { WalletService } from '../../services/wallet.service';
import { authenticate } from '../../middlewares/auth';
import { validateRequest } from '../../middlewares/validateRequest';
import { WalletValidation } from '../../validators/wallet.validator';

const router = express.Router();
const walletController = new WalletController(new WalletService());

/**
 * @route GET /api/wallet
 * @desc Get user wallet details
 * @access Private
 */
router.get(
  '/',
  authenticate,
  walletController.getWallet
);

/**
 * @route GET /api/wallet/transactions
 * @desc Get wallet transactions (might be general or removed later)
 * @access Private
 */
router.get(
  '/transactions',
  authenticate,
  walletController.getTransactions
);

/**
 * @route GET /api/wallet/withdraw/history
 * @desc Get withdrawal history
 * @access Private
 */
router.get(
  '/withdraw/history',
  authenticate,
  walletController.getWithdrawalHistory
);

/**
 * @route GET /api/wallet/deposit/history
 * @desc Get deposit history
 * @access Private
 */
router.get(
  '/deposit/history',
  authenticate,
  walletController.getDepositHistory
);

/**
 * @route POST /api/wallet/deposit
 * @desc Deposit funds to wallet
 * @access Private
 */
router.post(
  '/deposit',
  authenticate,
  validateRequest(WalletValidation.deposit),
  walletController.deposit
);

/**
 * @route POST /api/wallet/withdraw
 * @desc Withdraw funds from wallet
 * @access Private
 */
router.post(
  '/withdraw',
  authenticate,
  validateRequest(WalletValidation.withdraw),
  walletController.withdraw
);

/**
 * @route POST /api/wallet/withdraw/validate
 * @desc Validate a withdrawal request
 * @access Private
 */
router.post(
  '/withdraw/validate',
  authenticate,
  validateRequest(WalletValidation.withdrawalValidation),
  walletController.validateWithdrawal
);

/**
 * @route GET /api/wallet/withdraw/:withdrawalId/status
 * @desc Get withdrawal status
 * @access Private
 */
router.get(
  '/withdraw/:withdrawalId/status',
  authenticate,
  walletController.getWithdrawalStatus
);

/**
 * @route GET /api/wallet/deposit/:depositId/status
 * @desc Get deposit status
 * @access Private
 */
router.get(
  '/deposit/:depositId/status',
  authenticate,
  walletController.getDepositStatus
);

export default router; 
