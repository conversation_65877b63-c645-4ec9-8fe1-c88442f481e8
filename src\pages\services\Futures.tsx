
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { AreaChart, Area, LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { BarChart3, TrendingUp, LineChart as LineChartIcon, BarChart as BarChartIcon } from "lucide-react";
import { getServicesByCategory } from '@/data/servicesData';
import { commodities } from '@/data/commodityData';

// Mock price data for different contract months
const futuresPriceData = {
  coffee: [
    { month: "JUN24", price: 34500 },
    { month: "SEP24", price: 35600 },
    { month: "DEC24", price: 36200 },
    { month: "MAR25", price: 36800 },
    { month: "JUN25", price: 37400 },
  ],
  cocoa: [
    { month: "JUN24", price: 125000 },
    { month: "SEP24", price: 127500 },
    { month: "DEC24", price: 130000 },
    { month: "MAR25", price: 132500 },
    { month: "JUN25", price: 135000 },
  ],
  soybean: [
    { month: "JUN24", price: 53000 },
    { month: "SEP24", price: 54000 },
    { month: "DEC24", price: 55000 },
    { month: "MAR25", price: 56000 },
    { month: "JUN25", price: 57000 },
  ],
};

// Mock user's futures positions
const userPositions = [
  { 
    id: "pos-001", 
    commodity: "Coffee (Arabica)", 
    contractMonth: "SEP24", 
    position: "Long", 
    quantity: 5, 
    entryPrice: 35200, 
    currentPrice: 35600, 
    profitLoss: 2000
  },
  { 
    id: "pos-002", 
    commodity: "Cocoa", 
    contractMonth: "DEC24", 
    position: "Short", 
    quantity: 2, 
    entryPrice: 132000, 
    currentPrice: 130000, 
    profitLoss: 4000
  },
];

// Mock market data for analysis
const marketAnalysisData = [
  { date: "2023-11", price: 33800, production: 120, exports: 110, inventory: 80 },
  { date: "2023-12", price: 34200, production: 125, exports: 115, inventory: 90 },
  { date: "2024-01", price: 34600, production: 130, exports: 120, inventory: 100 },
  { date: "2024-02", price: 35000, production: 128, exports: 122, inventory: 106 },
  { date: "2024-03", price: 35400, production: 126, exports: 125, inventory: 107 },
];

const FuturesServices = () => {
  const [activeTab, setActiveTab] = useState("market");
  const [selectedCommodity, setSelectedCommodity] = useState("coffee");
  const futuresServices = getServicesByCategory("futures");
  
  // Order form state
  const [orderType, setOrderType] = useState("buy");
  const [orderQuantity, setOrderQuantity] = useState(1);
  const [selectedContract, setSelectedContract] = useState("SEP24");
  const [orderPrice, setOrderPrice] = useState(futuresPriceData[selectedCommodity as keyof typeof futuresPriceData][1].price);
  
  // Get current contract price
  const getCurrentPrice = () => {
    const contractData = futuresPriceData[selectedCommodity as keyof typeof futuresPriceData];
    const contract = contractData.find(c => c.month === selectedContract);
    return contract ? contract.price : contractData[0].price;
  };
  
  // Calculate order value
  const calculateOrderValue = () => {
    return orderQuantity * orderPrice;
  };
  
  // Handle contract change
  const handleContractChange = (value: string) => {
    setSelectedContract(value);
    const contractData = futuresPriceData[selectedCommodity as keyof typeof futuresPriceData];
    const contract = contractData.find(c => c.month === value);
    setOrderPrice(contract ? contract.price : contractData[0].price);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <BarChart3 className="h-8 w-8 mr-2 text-primary" />
        <h1 className="text-3xl font-bold">Futures Trading</h1>
      </div>
      
      <p className="text-muted-foreground mb-8">
        Access agricultural futures markets to hedge against price volatility or speculate on future commodity prices.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {futuresServices.map((service) => (
          <Card key={service.id} className="bg-card hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle>{service.title}</CardTitle>
              <CardDescription>{service.description}</CardDescription>
            </CardHeader>
            <CardFooter>
              <Button variant="outline" onClick={() => setActiveTab(service.id === "futures-trading" ? "trading" : "analysis")}>
                Access Service
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="market" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" /> Market Overview
          </TabsTrigger>
          <TabsTrigger value="trading" className="flex items-center gap-2">
            <LineChartIcon className="h-4 w-4" /> Trading Platform
          </TabsTrigger>
          <TabsTrigger value="analysis" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" /> Market Analysis
          </TabsTrigger>
        </TabsList>

        {/* Market Overview Tab */}
        <TabsContent value="market">
          <Card>
            <CardHeader>
              <CardTitle>Futures Market Overview</CardTitle>
              <CardDescription>View current futures prices and contract information</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-6">
                <Label>Select Commodity</Label>
                <Select value={selectedCommodity} onValueChange={setSelectedCommodity}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select commodity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="coffee">Coffee (Arabica)</SelectItem>
                    <SelectItem value="cocoa">Cocoa</SelectItem>
                    <SelectItem value="soybean">Soybean</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card className="col-span-1 md:col-span-2">
                  <CardHeader>
                    <CardTitle className="text-lg">Price Chart</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <AreaChart
                          data={futuresPriceData[selectedCommodity as keyof typeof futuresPriceData]}
                          margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
                        >
                          <defs>
                            <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8} />
                              <stop offset="95%" stopColor="#8884d8" stopOpacity={0} />
                            </linearGradient>
                          </defs>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Area
                            type="monotone"
                            dataKey="price"
                            stroke="#8884d8"
                            fillOpacity={1}
                            fill="url(#colorPrice)"
                          />
                        </AreaChart>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Contract Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="text-sm text-muted-foreground">Commodity</div>
                        <div className="font-medium">
                          {selectedCommodity === "coffee" ? "Coffee (Arabica)" : 
                           selectedCommodity === "cocoa" ? "Cocoa" : "Soybean"}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">Contract Size</div>
                        <div className="font-medium">
                          {selectedCommodity === "coffee" ? "100 kg" : 
                           selectedCommodity === "cocoa" ? "50 kg" : "200 kg"}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">Tick Size</div>
                        <div className="font-medium">
                          {selectedCommodity === "coffee" ? "50 UGX" : 
                           selectedCommodity === "cocoa" ? "100 UGX" : "25 UGX"}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">Trading Hours</div>
                        <div className="font-medium">9:00 AM - 4:30 PM EAT</div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">Settlement</div>
                        <div className="font-medium">Physical delivery</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="text-lg">Futures Contracts</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-border text-left">
                          <th className="pb-3 text-muted-foreground font-medium">Contract Month</th>
                          <th className="pb-3 text-muted-foreground font-medium text-right">Last Price</th>
                          <th className="pb-3 text-muted-foreground font-medium text-right">Daily Change</th>
                          <th className="pb-3 text-muted-foreground font-medium text-right">Volume</th>
                          <th className="pb-3 text-muted-foreground font-medium text-right">Open Interest</th>
                          <th className="pb-3 text-muted-foreground font-medium"></th>
                        </tr>
                      </thead>
                      <tbody>
                        {futuresPriceData[selectedCommodity as keyof typeof futuresPriceData].map((contract, index) => (
                          <tr key={contract.month} className="border-b border-border">
                            <td className="py-3 text-sm font-medium">{contract.month}</td>
                            <td className="py-3 text-right">{contract.price.toLocaleString()} UGX</td>
                            <td className={`py-3 text-right ${index % 2 === 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {index % 2 === 0 ? '+' : '-'}{Math.round(50 + Math.random() * 150)} UGX
                            </td>
                            <td className="py-3 text-right">{Math.round(100 + Math.random() * 300)}</td>
                            <td className="py-3 text-right">{Math.round(500 + Math.random() * 1000)}</td>
                            <td className="py-3 text-right">
                              <Button variant="outline" size="sm" onClick={() => {
                                setActiveTab("trading");
                                setSelectedContract(contract.month);
                                setOrderPrice(contract.price);
                              }}>
                                Trade
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Trading Platform Tab */}
        <TabsContent value="trading">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
            <Card className="md:col-span-8">
              <CardHeader>
                <CardTitle>Price Chart</CardTitle>
                <div className="flex items-center gap-2">
                  <Select value={selectedCommodity} onValueChange={setSelectedCommodity}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select commodity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="coffee">Coffee (Arabica)</SelectItem>
                      <SelectItem value="cocoa">Cocoa</SelectItem>
                      <SelectItem value="soybean">Soybean</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={selectedContract} onValueChange={handleContractChange}>
                    <SelectTrigger className="w-[120px]">
                      <SelectValue placeholder="Contract" />
                    </SelectTrigger>
                    <SelectContent>
                      {futuresPriceData[selectedCommodity as keyof typeof futuresPriceData].map(contract => (
                        <SelectItem key={contract.month} value={contract.month}>{contract.month}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={[
                        ...Array(20).fill(0).map((_, i) => {
                          const basePrice = getCurrentPrice();
                          const fluctuation = Math.random() * 300 - 150; // Random fluctuation between -150 and +150
                          return {
                            time: `${8 + Math.floor(i/2)}:${i % 2 === 0 ? '00' : '30'}`,
                            price: basePrice + fluctuation,
                          };
                        })
                      ]}
                      margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" vertical={false} />
                      <XAxis dataKey="time" />
                      <YAxis domain={['auto', 'auto']} />
                      <Tooltip />
                      <Line
                        type="monotone"
                        dataKey="price"
                        stroke="#8884d8"
                        strokeWidth={2}
                        dot={false}
                        activeDot={{ r: 8 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>

                <div className="mt-6">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Your Positions</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {userPositions.length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="w-full">
                            <thead>
                              <tr className="border-b border-border text-left">
                                <th className="pb-2 text-muted-foreground font-medium">Commodity</th>
                                <th className="pb-2 text-muted-foreground font-medium">Contract</th>
                                <th className="pb-2 text-muted-foreground font-medium">Position</th>
                                <th className="pb-2 text-muted-foreground font-medium text-right">Quantity</th>
                                <th className="pb-2 text-muted-foreground font-medium text-right">Entry Price</th>
                                <th className="pb-2 text-muted-foreground font-medium text-right">Current Price</th>
                                <th className="pb-2 text-muted-foreground font-medium text-right">P/L</th>
                                <th className="pb-2 text-muted-foreground font-medium"></th>
                              </tr>
                            </thead>
                            <tbody>
                              {userPositions.map(position => (
                                <tr key={position.id} className="border-b border-border">
                                  <td className="py-2 text-sm font-medium">{position.commodity}</td>
                                  <td className="py-2">{position.contractMonth}</td>
                                  <td className="py-2">
                                    <Badge variant={position.position === "Long" ? "outline" : "secondary"} className={position.position === "Long" ? "bg-green-100 text-green-800 border-green-200" : "bg-red-100 text-red-800 border-red-200"}>
                                      {position.position}
                                    </Badge>
                                  </td>
                                  <td className="py-2 text-right">{position.quantity}</td>
                                  <td className="py-2 text-right">{position.entryPrice.toLocaleString()}</td>
                                  <td className="py-2 text-right">{position.currentPrice.toLocaleString()}</td>
                                  <td className={`py-2 text-right ${position.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                    {position.profitLoss >= 0 ? '+' : ''}{position.profitLoss.toLocaleString()} UGX
                                  </td>
                                  <td className="py-2 text-right">
                                    <Button variant="outline" size="sm">Close</Button>
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-6 text-muted-foreground">
                          No active positions. Place an order to get started.
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>

            <Card className="md:col-span-4">
              <CardHeader>
                <CardTitle>Place Order</CardTitle>
                <CardDescription>
                  {selectedCommodity === "coffee" ? "Coffee (Arabica)" : 
                   selectedCommodity === "cocoa" ? "Cocoa" : "Soybean"} {selectedContract}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <Button
                      type="button"
                      variant={orderType === "buy" ? "default" : "outline"}
                      className={orderType === "buy" ? "bg-green-600 hover:bg-green-700" : ""}
                      onClick={() => setOrderType("buy")}
                    >
                      Buy/Long
                    </Button>
                    <Button
                      type="button"
                      variant={orderType === "sell" ? "default" : "outline"}
                      className={orderType === "sell" ? "bg-red-600 hover:bg-red-700" : ""}
                      onClick={() => setOrderType("sell")}
                    >
                      Sell/Short
                    </Button>
                  </div>

                  <div>
                    <Label htmlFor="quantity">Quantity (contracts)</Label>
                    <Input
                      id="quantity"
                      type="number"
                      min="1"
                      value={orderQuantity}
                      onChange={e => setOrderQuantity(parseInt(e.target.value) || 1)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="price">Price (UGX)</Label>
                    <Input
                      id="price"
                      type="number"
                      value={orderPrice}
                      onChange={e => setOrderPrice(parseInt(e.target.value) || getCurrentPrice())}
                    />
                    <div className="flex justify-between mt-1 text-xs">
                      <span className="text-muted-foreground">Market: {getCurrentPrice().toLocaleString()} UGX</span>
                      <button 
                        type="button"
                        className="text-primary hover:underline"
                        onClick={() => setOrderPrice(getCurrentPrice())}
                      >
                        Use Market Price
                      </button>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t">
                    <div className="flex justify-between mb-2">
                      <span className="text-muted-foreground">Contract Size:</span>
                      <span>
                        {selectedCommodity === "coffee" ? "100 kg" : 
                         selectedCommodity === "cocoa" ? "50 kg" : "200 kg"}
                      </span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="text-muted-foreground">Order Value:</span>
                      <span>{calculateOrderValue().toLocaleString()} UGX</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="text-muted-foreground">Fees:</span>
                      <span>{Math.round(calculateOrderValue() * 0.002).toLocaleString()} UGX</span>
                    </div>
                    <div className="flex justify-between mb-2">
                      <span className="text-muted-foreground">Margin Required:</span>
                      <span>{Math.round(calculateOrderValue() * 0.1).toLocaleString()} UGX</span>
                    </div>
                    <div className="flex justify-between pt-2 font-medium border-t">
                      <span>Total Required:</span>
                      <span>{Math.round(calculateOrderValue() * 0.102).toLocaleString()} UGX</span>
                    </div>
                  </div>

                  <Button type="button" className="w-full">
                    {orderType === "buy" ? "Buy" : "Sell"} {orderQuantity} Contract{orderQuantity > 1 ? 's' : ''}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Market Analysis Tab */}
        <TabsContent value="analysis">
          <Card>
            <CardHeader>
              <CardTitle>Market Analysis</CardTitle>
              <CardDescription>Analyze market trends and factors affecting commodity prices</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <Select value={selectedCommodity} onValueChange={setSelectedCommodity}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select commodity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="coffee">Coffee (Arabica)</SelectItem>
                      <SelectItem value="cocoa">Cocoa</SelectItem>
                      <SelectItem value="soybean">Soybean</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="text-lg">Supply and Demand Analysis</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[300px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart
                          data={marketAnalysisData}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis yAxisId="left" />
                          <YAxis yAxisId="right" orientation="right" />
                          <Tooltip />
                          <Line
                            yAxisId="left"
                            type="monotone"
                            dataKey="production"
                            name="Production"
                            stroke="#8884d8"
                            activeDot={{ r: 8 }}
                          />
                          <Line
                            yAxisId="left"
                            type="monotone"
                            dataKey="exports"
                            name="Exports"
                            stroke="#82ca9d"
                          />
                          <Line
                            yAxisId="left"
                            type="monotone"
                            dataKey="inventory"
                            name="Inventory"
                            stroke="#ffc658"
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    </div>
                    
                    <div className="mt-6 bg-muted p-4 rounded-lg">
                      <h3 className="font-medium mb-2">Market Insights</h3>
                      <p className="text-sm text-muted-foreground mb-2">
                        The {selectedCommodity === "coffee" ? "coffee" : 
                         selectedCommodity === "cocoa" ? "cocoa" : "soybean"} market shows {
                           selectedCommodity === "coffee" ? "increasing production trends with stable demand, suggesting potential price stability in the coming months." : 
                           selectedCommodity === "cocoa" ? "supply constraints against rising demand, indicating bullish price sentiment for future contracts." : 
                           "balanced supply and demand fundamentals, with seasonal fluctuations expected in the next quarter."
                         }
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Global inventory levels {
                           selectedCommodity === "coffee" ? "have increased by 33% year over year, which may put downward pressure on prices." : 
                           selectedCommodity === "cocoa" ? "remain at five-year lows, supporting higher price levels in the near term." : 
                           "indicate adequate supply for current demand, but weather concerns could affect future harvest yields."
                         }
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Price Forecast</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[200px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={[
                            { period: "Current", price: getCurrentPrice() },
                            { period: "3 Month", price: getCurrentPrice() * (1 + (Math.random() * 0.1 - 0.02)) },
                            { period: "6 Month", price: getCurrentPrice() * (1 + (Math.random() * 0.15 - 0.03)) },
                            { period: "12 Month", price: getCurrentPrice() * (1 + (Math.random() * 0.25 - 0.05)) },
                          ]}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="period" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="price" fill="#8884d8" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>

                    <div className="mt-6 space-y-3">
                      <h3 className="font-medium">Key Price Drivers</h3>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-start gap-2">
                          <div className="w-1 h-1 rounded-full bg-primary mt-2"></div>
                          <span>
                            {selectedCommodity === "coffee" ? "Weather conditions in major growing regions" : 
                             selectedCommodity === "cocoa" ? "Supply constraints in West Africa" : 
                             "Global soybean harvest yields"}
                          </span>
                        </li>
                        <li className="flex items-start gap-2">
                          <div className="w-1 h-1 rounded-full bg-primary mt-2"></div>
                          <span>
                            {selectedCommodity === "coffee" ? "Export quotas from producing countries" : 
                             selectedCommodity === "cocoa" ? "Processing capacity constraints" : 
                             "Demand from livestock feed market"}
                          </span>
                        </li>
                        <li className="flex items-start gap-2">
                          <div className="w-1 h-1 rounded-full bg-primary mt-2"></div>
                          <span>Currency fluctuations in producing regions</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <div className="w-1 h-1 rounded-full bg-primary mt-2"></div>
                          <span>Global economic growth outlook</span>
                        </li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="text-lg">Seasonal Price Patterns</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-[250px]">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={[
                          { month: "Jan", thisYear: 33000, lastYear: 31500, fiveYearAvg: 30000 },
                          { month: "Feb", thisYear: 34500, lastYear: 32000, fiveYearAvg: 30500 },
                          { month: "Mar", thisYear: 33800, lastYear: 32500, fiveYearAvg: 31000 },
                          { month: "Apr", thisYear: 32600, lastYear: 33000, fiveYearAvg: 31500 },
                          { month: "May", thisYear: 33200, lastYear: 32800, fiveYearAvg: 32000 },
                          { month: "Jun", thisYear: 34100, lastYear: 33500, fiveYearAvg: 32500 },
                          { month: "Jul", thisYear: 34800, lastYear: 34000, fiveYearAvg: 33000 },
                          { month: "Aug", thisYear: 35200, lastYear: 34500, fiveYearAvg: 33500 },
                          { month: "Sep", thisYear: 35400, lastYear: 35000, fiveYearAvg: 34000 },
                          { month: "Oct", thisYear: 35100, lastYear: 34800, fiveYearAvg: 34200 },
                          { month: "Nov", thisYear: 35400, lastYear: 34600, fiveYearAvg: 34500 },
                          { month: "Dec", thisYear: 35600, lastYear: 34700, fiveYearAvg: 34800 },
                        ]}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip />
                        <Area
                          type="monotone"
                          dataKey="thisYear"
                          name="This Year"
                          stackId="1"
                          stroke="#8884d8"
                          fill="#8884d8"
                        />
                        <Area
                          type="monotone"
                          dataKey="lastYear"
                          name="Last Year"
                          stackId="2"
                          stroke="#82ca9d"
                          fill="#82ca9d"
                        />
                        <Area
                          type="monotone"
                          dataKey="fiveYearAvg"
                          name="5-Year Avg"
                          stackId="3"
                          stroke="#ffc658"
                          fill="#ffc658"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-medium mb-2">Trading Strategy Recommendations</h3>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-start gap-2">
                          <div className="w-1 h-1 rounded-full bg-primary mt-2"></div>
                          <span>
                            {selectedCommodity === "coffee" ? "Consider long positions for SEP24 contracts due to expected supply tightness" : 
                             selectedCommodity === "cocoa" ? "DEC24 contracts show potential for upside based on seasonal demand patterns" : 
                             "Spread trading between JUN24 and SEP24 contracts may capture seasonal price differentials"}
                          </span>
                        </li>
                        <li className="flex items-start gap-2">
                          <div className="w-1 h-1 rounded-full bg-primary mt-2"></div>
                          <span>
                            {selectedCommodity === "coffee" ? "Monitor Brazilian weather reports for potential impact on harvests" : 
                             selectedCommodity === "cocoa" ? "Watch for policy changes in major producing countries" : 
                             "Pay attention to US-China trade relations for potential market impacts"}
                          </span>
                        </li>
                        <li className="flex items-start gap-2">
                          <div className="w-1 h-1 rounded-full bg-primary mt-2"></div>
                          <span>Set stop-loss orders at 5-7% below entry points to manage risk</span>
                        </li>
                      </ul>
                    </div>
                    
                    <div>
                      <h3 className="font-medium mb-2">Risk Factors</h3>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Weather Risks</span>
                            <span>High</span>
                          </div>
                          <div className="h-2 bg-muted rounded-full overflow-hidden">
                            <div className="h-full bg-red-500 rounded-full" style={{ width: '75%' }}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Political Instability</span>
                            <span>Medium</span>
                          </div>
                          <div className="h-2 bg-muted rounded-full overflow-hidden">
                            <div className="h-full bg-yellow-500 rounded-full" style={{ width: '50%' }}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Market Volatility</span>
                            <span>Medium-Low</span>
                          </div>
                          <div className="h-2 bg-muted rounded-full overflow-hidden">
                            <div className="h-full bg-green-500 rounded-full" style={{ width: '35%' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FuturesServices;
