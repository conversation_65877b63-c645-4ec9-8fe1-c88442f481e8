
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { ArrowUpRight, ArrowDownRight, Info, Coffee, Wheat, Sprout, History } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from "recharts";
import { useToast } from "@/hooks/use-toast";
import { commodities } from "@/data/commodityData";
import { Transaction } from "@/pages/Wallet";
import { formatCurrency } from "@/utils/currencyFormatter";

// Import chart data from commodity data source
const generateChartData = (commodityId: string) => {
  const commodity = commodities.find(c => c.id === commodityId);
  if (!commodity) return [];
  
  // Create hourly data points for today using the current price as base
  const basePrice = commodity.currentPrice;
  const volatility = 0.02; // 2% volatility
  
  return Array.from({ length: 9 }, (_, i) => {
    // Random price fluctuation within volatility range
    const randomChange = (Math.random() - 0.5) * volatility * basePrice;
    return { 
      time: `${9 + i}:00`, 
      price: Math.round((basePrice + randomChange) * 100) / 100 
    };
  });
};

// Filter out beans commodities and map the rest to trading assets
const tradingAssets = commodities
  .filter(commodity => !commodity.id.toLowerCase().includes('bean'))
  .map(commodity => ({
    id: commodity.id,
    name: commodity.name,
    symbol: commodity.id.toUpperCase(),
    price: commodity.currentPrice,
    change: commodity.priceChange,
    volume: commodity.volume || `${Math.floor(Math.random() * 5 + 1)}.${Math.floor(Math.random() * 9)}M`,
    icon: commodity.id.includes("coffee") ? 
      <Coffee className="h-4 w-4" /> : 
      commodity.id.includes("sun") || commodity.id.includes("sesame") ? 
      <Wheat className="h-4 w-4" /> : 
      <Sprout className="h-4 w-4" />
  }));

const Trade = () => {
  const [selectedCommodity, setSelectedCommodity] = useState(tradingAssets[0]);
  const [tradeAmount, setTradeAmount] = useState(1000);
  const [buyQuantity, setBuyQuantity] = useState(
    (1000 / selectedCommodity.price).toFixed(4)
  );
  const [chartData, setChartData] = useState(generateChartData(selectedCommodity.id));
  const [recentTrades, setRecentTrades] = useState<Transaction[]>([]);
  const { toast } = useToast();

  // Load recent trades from localStorage on mount and filter out beans
  useEffect(() => {
    const storedTransactions = localStorage.getItem('transactions');
    if (storedTransactions) {
      try {
        const allTransactions = JSON.parse(storedTransactions) as Transaction[];
        // Filter for buy/sell transactions only and exclude beans
        const tradeTransactions = allTransactions.filter(
          tx => (tx.type === 'buy' || tx.type === 'sell') && 
                !tx.asset.toLowerCase().includes('bean')
        );
        setRecentTrades(tradeTransactions.slice(0, 5)); // Show last 5 trades
      } catch (error) {
        console.error("Error parsing transactions:", error);
      }
    }
  }, []);

  const handleCommodityChange = (value: string) => {
    const commodity = tradingAssets.find(c => c.id === value) || tradingAssets[0];
    setSelectedCommodity(commodity);
    setBuyQuantity((tradeAmount / commodity.price).toFixed(4));
    setChartData(generateChartData(commodity.id));
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    if (!isNaN(value) && value >= 0) {
      setTradeAmount(value);
      setBuyQuantity((value / selectedCommodity.price).toFixed(4));
    }
  };

  const handleSliderChange = (value: number[]) => {
    const amount = value[0] * 100;
    setTradeAmount(amount);
    setBuyQuantity((amount / selectedCommodity.price).toFixed(4));
  };

  const saveTransaction = (type: 'buy' | 'sell', asset: string, amount: number, price: number) => {
    // Skip if this is a beans-related transaction
    if (asset.toLowerCase().includes('bean')) {
      return null;
    }
    
    const newTransaction: Transaction = {
      id: `TX${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
      type,
      asset,
      amount: parseFloat(amount.toFixed(4)),
      price,
      value: tradeAmount,
      status: "completed",
      date: new Date().toISOString()
    };
    
    // Save to localStorage but filter out beans
    try {
      const storedTransactions = localStorage.getItem('transactions') || '[]';
      const transactions = JSON.parse(storedTransactions) as Transaction[];
      const filteredTransactions = transactions.filter(tx => !tx.asset.toLowerCase().includes('bean'));
      filteredTransactions.unshift(newTransaction); // Add to beginning
      localStorage.setItem('transactions', JSON.stringify(filteredTransactions));
      
      // Update recent trades
      setRecentTrades(prev => [newTransaction, ...prev].slice(0, 5));
    } catch (error) {
      console.error("Error saving transaction:", error);
    }
    
    return newTransaction;
  };

  const handleBuy = () => {
    const transaction = saveTransaction('buy', selectedCommodity.name, parseFloat(buyQuantity), selectedCommodity.price);
    
    toast({
      title: "Order Placed",
      description: `Successfully bought ${transaction.amount} ${selectedCommodity.symbol} for ${tradeAmount.toLocaleString()} UGX`,
    });
  };

  const handleSell = () => {
    const transaction = saveTransaction('sell', selectedCommodity.name, parseFloat(buyQuantity), selectedCommodity.price);
    
    toast({
      title: "Order Placed",
      description: `Successfully sold ${transaction.amount} ${selectedCommodity.symbol} for ${tradeAmount.toLocaleString()} UGX`,
    });
  };

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold mb-6">Trade</h1>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader className="pb-0">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-xl">{selectedCommodity.name} ({selectedCommodity.symbol})</CardTitle>
                  <div className="flex items-center mt-1">
                    <span className="text-2xl font-bold mr-2">
                      {formatCurrency(selectedCommodity.price)}
                    </span>
                    <span className={`flex items-center text-sm ${selectedCommodity.change >= 0 ? "text-up" : "text-down"}`}>
                      {selectedCommodity.change >= 0 ? (
                        <ArrowUpRight className="h-4 w-4 mr-1" />
                      ) : (
                        <ArrowDownRight className="h-4 w-4 mr-1" />
                      )}
                      {Math.abs(selectedCommodity.change)}%
                    </span>
                  </div>
                </div>
                <Select
                  value={selectedCommodity.id}
                  onValueChange={handleCommodityChange}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select Asset" />
                  </SelectTrigger>
                  <SelectContent>
                    {tradingAssets.map(c => (
                      <SelectItem key={c.id} value={c.id}>
                        {c.name} ({c.symbol})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="1d">
                <div className="flex justify-between items-center mb-4">
                  <TabsList>
                    <TabsTrigger value="1h">1H</TabsTrigger>
                    <TabsTrigger value="1d">1D</TabsTrigger>
                    <TabsTrigger value="1w">1W</TabsTrigger>
                    <TabsTrigger value="1m">1M</TabsTrigger>
                    <TabsTrigger value="1y">1Y</TabsTrigger>
                  </TabsList>
                  <div className="text-sm text-muted-foreground">
                    Volume: {selectedCommodity.volume}
                  </div>
                </div>

                <TabsContent value="1d" className="mt-0">
                  <div className="h-80 rounded-lg bg-muted p-4">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={chartData}
                        margin={{ top: 10, right: 10, left: 0, bottom: 0 }}
                      >
                        <defs>
                          <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#8BC34A" stopOpacity={0.6} />
                            <stop offset="95%" stopColor="#8BC34A" stopOpacity={0.1} />
                          </linearGradient>
                        </defs>
                        <XAxis 
                          dataKey="time"
                          stroke="hsl(var(--muted-foreground))"
                        />
                        <YAxis 
                          domain={['dataMin - 10', 'dataMax + 10']}
                          stroke="hsl(var(--muted-foreground))"
                          tickFormatter={(value) => `${value.toLocaleString()}`}
                        />
                        <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--muted))" />
                        <Tooltip 
                          contentStyle={{
                            backgroundColor: "hsl(var(--card))",
                            borderColor: "hsl(var(--border))",
                            color: "hsl(var(--card-foreground))"
                          }}
                          formatter={(value: number) => [`${value.toLocaleString()} UGX`, "Price"]}
                        />
                        <Area 
                          type="monotone" 
                          dataKey="price" 
                          stroke="#8BC34A" 
                          fill="url(#colorPrice)"
                          strokeWidth={2}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </TabsContent>

                {['1h', '1w', '1m', '1y'].map(period => (
                  <TabsContent value={period} key={period}>
                    <div className="h-80 flex items-center justify-center rounded-lg bg-muted">
                      <p className="text-muted-foreground">Loading {period} chart data...</p>
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div>
                <CardTitle>Recent Trades</CardTitle>
                <CardDescription>Your recent trading activity</CardDescription>
              </div>
            </CardHeader>
            <CardContent>
              {recentTrades.length === 0 ? (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">No recent trades found</p>
                  <Button 
                    variant="outline" 
                    className="mt-4"
                    onClick={handleBuy}
                  >
                    Make your first trade
                  </Button>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-border text-left">
                        <th className="pb-3 text-muted-foreground font-medium">Type</th>
                        <th className="pb-3 text-muted-foreground font-medium">Asset</th>
                        <th className="pb-3 text-muted-foreground font-medium">Amount</th>
                        <th className="pb-3 text-muted-foreground font-medium">Price</th>
                        <th className="pb-3 text-muted-foreground font-medium">Total</th>
                        <th className="pb-3 text-muted-foreground font-medium">Date</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentTrades.map((trade) => (
                        <tr key={trade.id} className="border-b border-border hover:bg-muted/5 transition-colors">
                          <td className="py-3">
                            <span 
                              className={`px-2 py-1 rounded text-xs font-medium ${
                                trade.type === "buy" 
                                  ? "bg-[#8BC34A]/10 text-[#8BC34A]" 
                                  : "bg-amber-500/10 text-amber-500"
                              }`}
                            >
                              {trade.type === "buy" ? "BUY" : "SELL"}
                            </span>
                          </td>
                          <td className="py-3">{trade.asset}</td>
                          <td className="py-3">{trade.amount}</td>
                          <td className="py-3">{formatCurrency(trade.price || 0)}</td>
                          <td className="py-3">{formatCurrency(trade.value || 0)}</td>
                          <td className="py-3 text-muted-foreground text-sm">
                            {new Date(trade.date).toLocaleString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Trade {selectedCommodity.name}</CardTitle>
              <CardDescription>Buy or sell this commodity</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <Label htmlFor="amount">Amount (UGX)</Label>
                  <div className="mt-2 relative">
                    <Input
                      id="amount"
                      type="number"
                      value={tradeAmount}
                      onChange={handleAmountChange}
                    />
                  </div>
                </div>

                <div>
                  <div className="flex justify-between mb-2">
                    <Label>Trade Size</Label>
                    <span className="text-sm text-muted-foreground">
                      {parseFloat(tradeAmount.toFixed(2)).toLocaleString()} UGX
                    </span>
                  </div>
                  <Slider
                    defaultValue={[10]}
                    max={100}
                    step={1}
                    value={[tradeAmount / 100]}
                    onValueChange={handleSliderChange}
                    className="my-4"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>1,000 UGX</span>
                    <span>10,000 UGX</span>
                  </div>
                </div>

                <div className="border rounded-lg p-4 bg-muted/30">
                  <div className="flex justify-between mb-2">
                    <span className="text-sm text-muted-foreground">You will get:</span>
                    <span className="font-medium">{buyQuantity} {selectedCommodity.symbol}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Price per unit:</span>
                    <span className="font-medium">{formatCurrency(selectedCommodity.price)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-2">
              <Button 
                className="w-full bg-[#8BC34A] hover:bg-[#8BC34A]/90 text-white" 
                onClick={handleBuy}
              >
                Buy {selectedCommodity.name}
              </Button>
              <Button 
                className="w-full" 
                variant="outline"
                onClick={handleSell}
              >
                Sell {selectedCommodity.name}
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Market Information</CardTitle>
              <CardDescription>Current market details</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">24h Change:</span>
                  <span className={`${selectedCommodity.change >= 0 ? "text-up" : "text-down"} font-medium`}>
                    {selectedCommodity.change >= 0 ? "+" : ""}{selectedCommodity.change}%
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">24h Volume:</span>
                  <span className="font-medium">{selectedCommodity.volume}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Market Cap:</span>
                  <span className="font-medium">{formatCurrency(selectedCommodity.price * 1000000)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">All-Time High:</span>
                  <span className="font-medium">{formatCurrency(selectedCommodity.price * 1.25)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Trade;
