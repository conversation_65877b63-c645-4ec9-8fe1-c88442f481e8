
import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { Truck, Phone, Navigation, Package, Thermometer, Droplets, Clock, AlertTriangle, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

// Define types for the shipment tracking data
interface ShipmentLocation {
  latitude: number;
  longitude: number;
  address: string;
  timestamp: string;
  status: string;
}

interface CargoCondition {
  temperature?: number;
  humidity?: number;
  lastUpdated: string;
}

interface Driver {
  name: string;
  phone: string;
  photoUrl?: string;
}

interface Vehicle {
  type: string;
  licensePlate: string;
  capacity: string;
  hasRefrigeration: boolean;
}

interface ShipmentDetails {
  id: string;
  origin: string;
  destination: string;
  commodity: string;
  weight: string;
  volume?: string;
  departureDate: string;
  estimatedArrival: string;
  status: "pending" | "in-transit" | "delivered" | "delayed";
  currentLocation: ShipmentLocation;
  routeProgress: number;
  cargoCondition?: CargoCondition;
  driver: Driver;
  vehicle: Vehicle;
}

interface ShipmentTrackingModalProps {
  isOpen: boolean;
  onClose: () => void;
  shipmentId: string;
}

const ShipmentTrackingModal: React.FC<ShipmentTrackingModalProps> = ({
  isOpen,
  onClose,
  shipmentId
}) => {
  const [shipment, setShipment] = useState<ShipmentDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen && shipmentId) {
      fetchShipmentDetails(shipmentId);
    }
  }, [isOpen, shipmentId]);

  const fetchShipmentDetails = async (id: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would be an API call
      // For now, we'll simulate it with mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate occasional errors for testing
      if (Math.random() < 0.1) {
        throw new Error("Network error");
      }
      
      const mockShipment: ShipmentDetails = {
        id,
        origin: "Kampala, Uganda",
        destination: "Jinja, Uganda",
        commodity: "Coffee (Arabica)",
        weight: "8.5 tons",
        volume: "32 cubic meters",
        departureDate: "2023-05-10T08:30:00Z",
        estimatedArrival: "2023-05-11T14:00:00Z",
        status: Math.random() < 0.7 ? "in-transit" : Math.random() < 0.5 ? "delayed" : "delivered",
        currentLocation: {
          latitude: 0.3476,
          longitude: 32.5825,
          address: "Mukono, Central Region",
          timestamp: new Date().toISOString(),
          status: "Moving eastward on Jinja Road"
        },
        routeProgress: Math.floor(Math.random() * 100),
        cargoCondition: {
          temperature: 22 + Math.random() * 5,
          humidity: 45 + Math.random() * 15,
          lastUpdated: new Date().toISOString()
        },
        driver: {
          name: "John Mutesa",
          phone: "+256 770 123456",
          photoUrl: "https://randomuser.me/api/portraits/men/32.jpg"
        },
        vehicle: {
          type: "Medium Truck",
          licensePlate: "UBX 432K",
          capacity: "12 tons",
          hasRefrigeration: false
        }
      };
      
      setShipment(mockShipment);
    } catch (err) {
      console.error("Error fetching shipment details:", err);
      setError("Failed to load shipment details. Please try again.");
      toast({
        title: "Error",
        description: "Could not load tracking information",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: ShipmentDetails["status"]) => {
    switch (status) {
      case "delivered":
        return <Badge className="bg-green-100 text-green-800 border-green-300">Delivered</Badge>;
      case "in-transit":
        return <Badge className="bg-blue-100 text-blue-800 border-blue-300">In Transit</Badge>;
      case "delayed":
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300">Delayed</Badge>;
      case "pending":
        return <Badge className="bg-gray-100 text-gray-800 border-gray-300">Pending</Badge>;
      default:
        return <Badge>Unknown</Badge>;
    }
  };

  // Format date for better readability
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', { 
      day: 'numeric', 
      month: 'short', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Calculate the time remaining for delivery
  const getTimeRemaining = (estimatedArrival: string) => {
    const now = new Date();
    const arrival = new Date(estimatedArrival);
    const diff = arrival.getTime() - now.getTime();
    
    if (diff <= 0) return "Arrived";
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m remaining`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center">
            <Truck className="mr-2 h-5 w-5" />
            Shipment Tracking
          </DialogTitle>
          <DialogDescription>
            Track your shipment in real-time
          </DialogDescription>
        </DialogHeader>
        
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="h-12 w-12 border-4 border-primary/30 border-t-primary rounded-full animate-spin"></div>
            <p className="mt-4 text-muted-foreground">Loading shipment details...</p>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
            <p className="text-destructive font-medium">{error}</p>
            <Button onClick={() => fetchShipmentDetails(shipmentId)} className="mt-4">
              Retry
            </Button>
          </div>
        ) : shipment ? (
          <div className="space-y-6">
            {/* Shipment overview */}
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div>
                <h3 className="text-lg font-bold">#{shipment.id}</h3>
                <p className="text-sm text-muted-foreground">{shipment.commodity} • {shipment.weight}</p>
              </div>
              <div className="flex items-center gap-2">
                {getStatusBadge(shipment.status)}
                {shipment.status === 'in-transit' && (
                  <Badge variant="outline" className="bg-blue-50">
                    <Clock className="h-3 w-3 mr-1" />
                    {getTimeRemaining(shipment.estimatedArrival)}
                  </Badge>
                )}
              </div>
            </div>
            
            <Separator />
            
            {/* Progress visualization */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>{shipment.origin}</span>
                <span>{shipment.destination}</span>
              </div>
              <div className="relative pt-4">
                <div className="absolute w-full h-1 bg-muted rounded-full"></div>
                <div 
                  className="absolute h-1 bg-primary rounded-full" 
                  style={{ width: `${shipment.routeProgress}%` }}
                ></div>
                <div 
                  className="absolute w-3 h-3 bg-primary rounded-full -translate-y-1"
                  style={{ left: `${shipment.routeProgress}%` }}
                ></div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Current location */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Current Location</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-muted h-32 rounded-md flex items-center justify-center mb-2">
                    <Navigation className="h-8 w-8 text-primary/50" />
                  </div>
                  <div className="space-y-1">
                    <p className="font-medium">{shipment.currentLocation.address}</p>
                    <p className="text-sm text-muted-foreground">
                      {shipment.currentLocation.status}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Updated: {formatDate(shipment.currentLocation.timestamp)}
                    </p>
                  </div>
                </CardContent>
              </Card>
              
              {/* Driver & Vehicle */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Driver & Vehicle</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start space-x-3 mb-3">
                    <div className="w-12 h-12 rounded-full bg-muted overflow-hidden">
                      {shipment.driver.photoUrl ? (
                        <img 
                          src={shipment.driver.photoUrl} 
                          alt={shipment.driver.name}
                          className="w-full h-full object-cover" 
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-primary/10 text-primary">
                          {shipment.driver.name.substring(0, 1)}
                        </div>
                      )}
                    </div>
                    <div>
                      <p className="font-medium">{shipment.driver.name}</p>
                      <a 
                        href={`tel:${shipment.driver.phone}`}
                        className="text-sm flex items-center text-primary hover:underline"
                      >
                        <Phone className="h-3 w-3 mr-1" />
                        {shipment.driver.phone}
                      </a>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <p className="text-muted-foreground">Vehicle Type</p>
                      <p className="font-medium">{shipment.vehicle.type}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">License Plate</p>
                      <p className="font-medium">{shipment.vehicle.licensePlate}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Capacity</p>
                      <p className="font-medium">{shipment.vehicle.capacity}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Refrigeration</p>
                      <p className="font-medium">{shipment.vehicle.hasRefrigeration ? "Yes" : "No"}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Shipment Timeline */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Shipment Timeline</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex">
                      <div className="mr-3 relative">
                        <div className={cn(
                          "h-6 w-6 rounded-full flex items-center justify-center",
                          shipment.status === "delivered" ? "bg-green-100" : "bg-blue-100"
                        )}>
                          {shipment.status === "delivered" ? (
                            <CheckCircle className="h-4 w-4 text-green-700" />
                          ) : (
                            <Package className="h-4 w-4 text-blue-700" />
                          )}
                        </div>
                        <div className="absolute left-3 top-6 h-full w-px bg-muted-foreground/20"></div>
                      </div>
                      <div>
                        <p className="font-medium">Shipment Departed</p>
                        <p className="text-sm text-muted-foreground">{formatDate(shipment.departureDate)}</p>
                        <p className="text-sm mt-1">From: {shipment.origin}</p>
                      </div>
                    </div>
                    
                    <div className="flex">
                      <div className="mr-3 relative">
                        <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                          <Navigation className="h-4 w-4 text-blue-700" />
                        </div>
                      </div>
                      <div>
                        <p className="font-medium">Current Location</p>
                        <p className="text-sm text-muted-foreground">
                          {formatDate(shipment.currentLocation.timestamp)}
                        </p>
                        <p className="text-sm mt-1">{shipment.currentLocation.address}</p>
                      </div>
                    </div>
                    
                    <div className="flex">
                      <div className="mr-3">
                        <div className="h-6 w-6 rounded-full bg-gray-100 flex items-center justify-center">
                          <Navigation className="h-4 w-4 text-gray-500" />
                        </div>
                      </div>
                      <div>
                        <p className="font-medium text-muted-foreground">Estimated Delivery</p>
                        <p className="text-sm">{formatDate(shipment.estimatedArrival)}</p>
                        <p className="text-sm mt-1">To: {shipment.destination}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* Cargo Conditions */}
              {shipment.cargoCondition && (
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Cargo Conditions</CardTitle>
                    <CardDescription>
                      Last updated {formatDate(shipment.cargoCondition.lastUpdated)}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      {shipment.cargoCondition.temperature !== undefined && (
                        <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                          <Thermometer className="h-8 w-8 mr-3 text-blue-500" />
                          <div>
                            <p className="text-sm text-muted-foreground">Temperature</p>
                            <p className="font-bold text-lg">
                              {shipment.cargoCondition.temperature.toFixed(1)}°C
                            </p>
                          </div>
                        </div>
                      )}
                      
                      {shipment.cargoCondition.humidity !== undefined && (
                        <div className="flex items-center p-3 bg-blue-50 rounded-lg">
                          <Droplets className="h-8 w-8 mr-3 text-blue-500" />
                          <div>
                            <p className="text-sm text-muted-foreground">Humidity</p>
                            <p className="font-bold text-lg">
                              {shipment.cargoCondition.humidity.toFixed(1)}%
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
            
            <CardFooter className="px-0 pt-2 pb-0 flex justify-end">
              <Button onClick={onClose}>Close</Button>
            </CardFooter>
          </div>
        ) : null}
      </DialogContent>
    </Dialog>
  );
};

export default ShipmentTrackingModal;
