
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock } from "lucide-react";

// Mock data for futures contracts
const futuresData = [
  {
    id: "FUT-2023-001",
    commodity: "Coffee (Arabica)",
    quantity: "5,000 kg",
    deliveryDate: "June 15, 2025",
    status: "pending",
  },
  {
    id: "FUT-2023-002",
    commodity: "Cocoa",
    quantity: "2,500 kg",
    deliveryDate: "August 22, 2025",
    status: "ongoing",
  },
  {
    id: "FUT-2023-003",
    commodity: "Sesame",
    quantity: "10,000 kg",
    deliveryDate: "July 5, 2025",
    status: "completed",
  },
  {
    id: "FUT-2023-004",
    commodity: "Soybean",
    quantity: "7,500 kg",
    deliveryDate: "September 10, 2025",
    status: "canceled",
  },
  {
    id: "FUT-2023-005",
    commodity: "Coffee (Robusta)",
    quantity: "12,000 kg",
    deliveryDate: "October 30, 2025",
    status: "pending",
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case "pending":
      return "bg-yellow-500/10 text-yellow-500 hover:bg-yellow-500/20";
    case "ongoing":
      return "bg-blue-500/10 text-blue-500 hover:bg-blue-500/20";
    case "completed":
      return "bg-green-500/10 text-green-500 hover:bg-green-500/20";
    case "canceled":
      return "bg-red-500/10 text-red-500 hover:bg-red-500/20";
    default:
      return "bg-gray-500/10 text-gray-500 hover:bg-gray-500/20";
  }
};

export const FuturesContracts = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-medium flex items-center">
          <Calendar className="h-5 w-5 mr-2" />
          Futures Contracts
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {futuresData.map((contract) => (
            <div
              key={contract.id}
              className="flex flex-col space-y-2 p-3 bg-secondary/50 rounded-md"
            >
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">{contract.commodity}</span>
                <Badge
                  variant="outline"
                  className={`capitalize ${getStatusColor(contract.status)}`}
                >
                  {contract.status}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground">
                {contract.quantity}
              </div>
              <div className="flex items-center text-sm text-muted-foreground">
                <Clock className="h-3 w-3 mr-1" />
                Delivery: {contract.deliveryDate}
              </div>
              <div className="text-xs text-muted-foreground/80">
                Contract: {contract.id}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default FuturesContracts;
