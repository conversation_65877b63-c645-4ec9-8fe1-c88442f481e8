
import { NavLink } from "react-router-dom";
import { ChevronDown, Instagram, Youtube, Twitter, Linkedin } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const serviceLinks = [
    { name: 'Transport & Logistics', path: '/services/transportation' },
    { name: 'Hire a Tractor', path: '/services/equipment' },
    { name: 'Warehousing', path: '/services/warehousing' },
    { name: 'Financing', path: '/services/financing' },
    { name: 'Insurance', path: '/services/insurance' },
    { name: 'Land Leasing', path: '/services/leasing' },
    { name: 'Agronomy Services', path: '/services/agronomy' },
    { name: 'Futures Trading', path: '/services/futures' },
  ];

  const aboutLinks = [
    { name: 'Our Mission', path: '/about-us#mission' },
    { name: 'Our Vision', path: '/about-us#vision' },
    { name: 'Our Team', path: '/about-us#team' },
    { name: 'Our History', path: '/about-us#history' },
    { name: 'Leadership', path: '/about-us#leadership' },
  ];

  return (
    <footer className="bg-card border-t border-border mt-auto">
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center">
              <img 
                src="/lovable-uploads/aa3d1364-5e25-4eeb-8811-52dd7d593425.png" 
                alt="Fotis Agro Logo" 
                className="h-42 w-auto object-contain"
                style={{ maxHeight: '150px', width: 'auto' }}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              Empowering agricultural trading with innovative technology solutions.
            </p>
          </div>
          
          <div>
            <h3 className="font-medium mb-4">Platform</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <DropdownMenu>
                  <DropdownMenuTrigger className="text-muted-foreground hover:text-[#8BC34A] transition-colors flex items-center gap-1">
                    Services <ChevronDown size={14} />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="z-50 bg-popover">
                    {serviceLinks.map((link) => (
                      <DropdownMenuItem key={link.path} asChild>
                        <NavLink to={link.path} className="cursor-pointer w-full">
                          {link.name}
                        </NavLink>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </li>
              <li>
                <NavLink to="/dashboard" className="text-muted-foreground hover:text-[#8BC34A] transition-colors">
                  Dashboard
                </NavLink>
              </li>
              <li>
                <NavLink to="/trade" className="text-muted-foreground hover:text-[#8BC34A] transition-colors">
                  Trading
                </NavLink>
              </li>
              <li>
                <NavLink to="/wallet" className="text-muted-foreground hover:text-[#8BC34A] transition-colors">
                  Wallet
                </NavLink>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium mb-4">Company</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <DropdownMenu>
                  <DropdownMenuTrigger className="text-muted-foreground hover:text-[#8BC34A] transition-colors flex items-center gap-1">
                    About <ChevronDown size={14} />
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="z-50 bg-popover">
                    {aboutLinks.map((link) => (
                      <DropdownMenuItem key={link.path} asChild>
                        <NavLink to={link.path} className="cursor-pointer w-full">
                          {link.name}
                        </NavLink>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </li>
              <li>
                <NavLink to="/contact" className="text-muted-foreground hover:text-[#8BC34A] transition-colors">
                  Contact
                </NavLink>
              </li>
              <li>
                <NavLink to="/careers" className="text-muted-foreground hover:text-[#8BC34A] transition-colors">
                  Careers
                </NavLink>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium mb-4">Legal</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <NavLink to="/terms" className="text-muted-foreground hover:text-[#8BC34A] transition-colors">
                  Terms of Service
                </NavLink>
              </li>
              <li>
                <NavLink to="/privacy" className="text-muted-foreground hover:text-[#8BC34A] transition-colors">
                  Privacy Policy
                </NavLink>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-border mt-8 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-muted-foreground text-center w-full md:w-auto">
            &copy; {currentYear} Fotis Agro Trading. All rights reserved.
          </p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <a href="#" className="text-muted-foreground hover:text-[#8BC34A] transition-colors">
              <span className="sr-only">Twitter</span>
              <Twitter className="h-5 w-5" />
            </a>
            <a href="#" className="text-muted-foreground hover:text-[#8BC34A] transition-colors">
              <span className="sr-only">LinkedIn</span>
              <Linkedin className="h-5 w-5" />
            </a>
            <a href="#" className="text-muted-foreground hover:text-[#8BC34A] transition-colors">
              <span className="sr-only">Instagram</span>
              <Instagram className="h-5 w-5" />
            </a>
            <a href="#" className="text-muted-foreground hover:text-[#8BC34A] transition-colors">
              <span className="sr-only">YouTube</span>
              <Youtube className="h-5 w-5" />
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
