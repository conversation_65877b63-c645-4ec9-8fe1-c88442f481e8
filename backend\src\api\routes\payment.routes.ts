import express from 'express';
import { PaymentController } from '../../controllers/payment.controller';
import { authenticateToken } from '../../middlewares/auth';
import { validateRequest } from '../../middlewares/validateRequest';
import { PaymentValidation } from '../../validators/payment.validator';

const router = express.Router();
const paymentController = new PaymentController();

/**
 * @route POST /api/payments/stripe/create-payment-intent
 * @desc Create a Stripe payment intent
 * @access Private
 */
router.post(
  '/stripe/create-payment-intent',
  authenticateToken,
  validateRequest(PaymentValidation.createStripePaymentIntent),
  paymentController.createStripePaymentIntent
);

/**
 * @route POST /api/payments/stripe/webhook
 * @desc Handle Stripe webhook events
 * @access Public
 */
router.post(
  '/stripe/webhook',
  validateRequest(PaymentValidation.stripeWebhook),
  paymentController.handleStripeWebhook
);

/**
 * @route POST /api/payments/mobile-money/initiate
 * @desc Initiate a mobile money payment
 * @access Private
 */
router.post(
  '/mobile-money/initiate',
  authenticateToken,
  validateRequest(PaymentValidation.initiateMobileMoneyPayment),
  paymentController.initiateMobileMoneyPayment
);

/**
 * @route GET /api/payments/mobile-money/status/:transactionId/:provider
 * @desc Check mobile money payment status
 * @access Private
 */
router.get(
  '/mobile-money/status/:transactionId/:provider',
  authenticateToken,
  validateRequest(PaymentValidation.checkMobileMoneyStatus),
  paymentController.checkMobileMoneyStatus
);

export default router; 