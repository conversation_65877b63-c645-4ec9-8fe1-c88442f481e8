
import { <PERSON>, Zap, <PERSON><PERSON>hart2, Wallet } from "lucide-react";

const features = [
  {
    title: "Tokenized Ownership",
    description: "Convert physical commodities into digital tokens for fractional ownership and seamless trading.",
    icon: <Wallet className="h-6 w-6" />,
  },
  {
    title: "Secure Trading",
    description: "Advanced security protocols and multi-factor authentication to protect your assets.",
    icon: <Shield className="h-6 w-6" />,
  },
  {
    title: "Real-time Analytics",
    description: "Track market trends and asset performance with comprehensive analytics tools.",
    icon: <BarChart2 className="h-6 w-6" />,
  },
  {
    title: "High Performance",
    description: "Execute trades quickly with our high-throughput trading engine optimized for speed.",
    icon: <Zap className="h-6 w-6" />,
  },
];

const Features = () => {
  return (
    <section className="py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold mb-3">Platform Features</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Our platform combines cutting-edge technology with user-friendly features to provide a secure and efficient trading experience.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {features.map((feature, index) => (
          <div
            key={index}
            className="bg-card border border-border rounded-lg p-6 hover:shadow-md transition-all hover:border-primary/50"
          >
            <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary mb-4">
              {feature.icon}
            </div>
            <h3 className="font-bold text-lg mb-2">{feature.title}</h3>
            <p className="text-muted-foreground">{feature.description}</p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default Features;
