import { useState, useEffect } from 'react';
import { getWalletBalance, updateWalletBalance } from '@/services/walletService';

export const useWallet = () => {
  const [balance, setBalance] = useState(() => getWalletBalance());

  useEffect(() => {
    const handleBalanceChange = (event: CustomEvent) => {
      setBalance(event.detail.balance);
    };

    // Listen for balance changes
    window.addEventListener('walletBalanceChanged', handleBalanceChange as EventListener);

    return () => {
      window.removeEventListener('walletBalanceChanged', handleBalanceChange as EventListener);
    };
  }, []);

  const updateBalance = (newBalance: number) => {
    updateWalletBalance(newBalance);
    setBalance(newBalance);
  };

  return { balance, updateBalance };
};