import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import { config } from '../config/env';

// Custom error class for API errors
export class ApiError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(statusCode: number, message: string, isOperational = true, stack = '') {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    if (stack) {
      this.stack = stack;
    } else {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

// 404 Not Found middleware
export const notFound = (req: Request, res: Response, next: NextFunction) => {
  const error = new ApiError(404, `Not Found - ${req.originalUrl}`);
  next(error);
};

interface ValidationErrorDetail {
  path: string[];
  message: string;
}

interface PrismaError extends Error {
  code: string;
  meta?: {
    target?: string[];
  };
}

// Error handler middleware
export const errorHandler = (
  err: Error | ApiError,
  req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  next: (err: Error) => void
) => {
  // Default error
  const error = err;
  let statusCode = 500;
  let message = 'Server Error';
  let isOperational = false;
  let errors: { field?: string; message: string }[] = [];

  // If it's our custom API error, use its properties
  if (err instanceof ApiError) {
    statusCode = err.statusCode;
    message = err.message;
    isOperational = err.isOperational;
  } 
  // Handle Prisma errors
  else if (err instanceof Error && 'code' in err) {
    const prismaError = err as PrismaError;
    
    if (prismaError.code.startsWith('P')) {
      statusCode = 400;
      isOperational = true;
      
      // Handle specific Prisma errors
      switch (prismaError.code) {
        case 'P2002': {
          const field = prismaError.meta?.target?.[0] || 'field';
          message = `A record with this ${field} already exists`;
          errors.push({ field, message });
          break;
        }
        case 'P2003':
          message = 'Referenced record does not exist';
          break;
        case 'P2014':
          message = 'Invalid ID provided';
          break;
        default:
          message = 'Database operation failed';
      }
      
      // Log the actual Prisma error for debugging
      logger.error(`Prisma error: ${err.message}`, {
        code: prismaError.code,
        stack: err.stack,
      });
    }
  }
  // Handle JWT errors
  else if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Unauthorized: Invalid or expired token';
    isOperational = true;
  }
  // Handle validation errors
  else if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation error';
    isOperational = true;
    if ('details' in err) {
      const validationError = err as { details: ValidationErrorDetail[] };
      errors = validationError.details.map((detail) => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));
    }
  }
  
  // Log error
  logger.error(`[${req.method}] ${req.url} >> StatusCode:: ${statusCode}, Message:: ${message}`, {
    stack: err.stack,
    isOperational,
    errors,
  });

  // Send response
  const response = {
    status: 'error',
    message,
    ...(errors.length > 0 && { errors }),
    ...(config.NODE_ENV === 'development' ? { stack: err.stack } : {}),
  };

  res.status(statusCode).json(response);
};
