
import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Truck } from "lucide-react";
import { TruckType } from "./TruckDetailsCard";

interface TruckOptionsCardProps {
  trucks: TruckType[];
  onSelectTruck: (truckId: string) => void;
  requiredCapacity?: number;
  selectedTruckId?: string;
}

interface TruckOptionProps {
  truck: TruckType;
  onClick: () => void;
  disabled?: boolean;
  isSelected?: boolean;
}

const TruckOption: React.FC<TruckOptionProps> = ({ 
  truck, 
  onClick, 
  disabled = false,
  isSelected = false
}) => {
  return (
    <Card 
      onClick={disabled ? undefined : onClick}
      className={`border h-full ${disabled 
        ? "opacity-60 cursor-not-allowed bg-muted/30" 
        : isSelected
          ? "cursor-pointer border-primary bg-primary/5 shadow-md"
          : "cursor-pointer transition-all hover:shadow-md hover:border-primary/50"}`}
    >
      <CardContent className="p-4">
        <div className="flex flex-col h-full">
          <div className="flex items-center mb-2">
            <div className="mr-3">
              {truck.icon || <Truck size={24} className="text-primary" />}
            </div>
            <div>
              <h3 className="font-medium text-base">{truck.name}</h3>
              <p className="text-xs text-muted-foreground">
                {truck.capacity && `Up to ${truck.capacityValue} tons`}
              </p>
            </div>
          </div>
          
          <div className="mt-1">
            <Badge 
              variant={truck.availableCount > 10 ? "secondary" : "outline"} 
              className="mb-2 text-xs"
            >
              {truck.availableCount} available
            </Badge>
            <p className="text-xs text-muted-foreground">
              Best for: {Array.isArray(truck.suitable) ? truck.suitable.join(', ') : truck.suitable}
            </p>
          </div>
          
          {disabled && (
            <Badge variant="outline" className="mt-2 bg-red-100 text-red-800 border-red-200 text-xs">
              Capacity insufficient for your load
            </Badge>
          )}
          {isSelected && (
            <Badge variant="outline" className="mt-2 bg-green-100 text-green-800 border-green-200 text-xs">
              Selected
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const TruckOptionsCard: React.FC<TruckOptionsCardProps> = ({ 
  trucks, 
  onSelectTruck, 
  requiredCapacity,
  selectedTruckId 
}) => {
  // Determine if a truck has sufficient capacity for the load
  const isTruckSufficient = (truck: TruckType) => {
    if (!requiredCapacity) return true;
    return truck.capacityValue >= requiredCapacity;
  };
  
  // Group trucks by their type based on the design in the image
  const lightTrucks = trucks.filter(t => t.capacityValue <= 4);
  const mediumTrucks = trucks.filter(t => t.capacityValue > 4 && t.capacityValue <= 8);
  const heavyTrucks = trucks.filter(t => t.capacityValue > 8 && t.capacityValue < 15);
  const refrigeratedTrucks = trucks.filter(t => t.id.includes("refrigerated"));
  
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* Light Trucks */}
        {lightTrucks.length > 0 && (
          <TruckOption
            key={lightTrucks[0].id}
            truck={{
              ...lightTrucks[0],
              name: "Light Truck",
              capacity: `Up to ${lightTrucks[0].capacityValue} tons`
            }}
            onClick={() => onSelectTruck(lightTrucks[0].id)}
            disabled={!isTruckSufficient(lightTrucks[0])}
            isSelected={selectedTruckId === lightTrucks[0].id}
          />
        )}
        
        {/* Medium Trucks */}
        {mediumTrucks.length > 0 && (
          <TruckOption
            key={mediumTrucks[0].id}
            truck={{
              ...mediumTrucks[0],
              name: "Medium Truck",
              capacity: `${Math.min(...mediumTrucks.map(t => t.capacityValue))}-${Math.max(...mediumTrucks.map(t => t.capacityValue))} tons`
            }}
            onClick={() => onSelectTruck(mediumTrucks[0].id)}
            disabled={!isTruckSufficient(mediumTrucks[0])}
            isSelected={selectedTruckId === mediumTrucks[0].id}
          />
        )}
        
        {/* Heavy Trucks */}
        {heavyTrucks.length > 0 && (
          <TruckOption
            key={heavyTrucks[0].id}
            truck={{
              ...heavyTrucks[0],
              name: "Heavy Truck",
              capacity: `${Math.min(...heavyTrucks.map(t => t.capacityValue))}-${Math.max(...heavyTrucks.map(t => t.capacityValue))} tons`
            }}
            onClick={() => onSelectTruck(heavyTrucks[0].id)}
            disabled={!isTruckSufficient(heavyTrucks[0])}
            isSelected={selectedTruckId === heavyTrucks[0].id}
          />
        )}
        
        {/* Refrigerated Trucks */}
        {refrigeratedTrucks.length > 0 && (
          <TruckOption
            key={refrigeratedTrucks[0].id}
            truck={{
              ...refrigeratedTrucks[0],
              name: "Refrigerated Truck",
              capacity: `Up to ${refrigeratedTrucks[0].capacityValue} tons`
            }}
            onClick={() => onSelectTruck(refrigeratedTrucks[0].id)}
            disabled={!isTruckSufficient(refrigeratedTrucks[0])}
            isSelected={selectedTruckId === refrigeratedTrucks[0].id}
          />
        )}
      </div>
    </div>
  );
};

export default TruckOptionsCard;
